package com.ecco.dom;

import java.io.Serializable;
import java.util.UUID;

import org.joda.time.DateTime;

public class OutcomeDataPoint implements Serializable {

    int outcomeIndex;
    long outcomeId;
    int currentPercentMeasure;
    int currentPercentLimit;

    // ********** TO ARCHIVE
    DateTime dateTime;
    UUID workId;
    public DateTime getDateTime() {
        return dateTime;
    }
    public void setDateTime(DateTime dateTime) {
        this.dateTime = dateTime;
    }
    public UUID getWorkId() {
        return workId;
    }
    public void setWorkId(UUID workId) {
        this.workId = workId;
    }
    // ********** TO ARCHIVE

    public long getOutcomeIndex() {
        return outcomeIndex;
    }
    public void setOutcomeIndex(int outcomeIndex) {
        this.outcomeIndex = outcomeIndex;
    }
    public long getOutcomeId() {
        return outcomeId;
    }
    public void setOutcomeId(long outcomeId) {
        this.outcomeId = outcomeId;
    }
    public int getCurrentPercentMeasure() {
        return currentPercentMeasure;
    }
    public void setCurrentPercentMeasure(int currentPercentMeasure) {
        this.currentPercentMeasure = currentPercentMeasure;
    }
    public int getCurrentPercentLimit() {
        return currentPercentLimit;
    }
    public void setCurrentPercentLimit(int currentPercentLimit) {
        this.currentPercentLimit = currentPercentLimit;
    }

}
