package com.ecco.dom;


public enum LowMediumHigh implements IdName<Integer> {

    Unknown(-1), Low(0), Medium(1), High(2);

    int value;

    LowMediumHigh(int value) {
        this.value = value;
    }

    public int toInt() {return value;}

    public static LowMediumHigh fromInt(int value) {
        switch (value) {
            case 0: return Low;
            case 1: return Medium;
            case 2: return High;
            default: return Unknown;
        }
    }

    @Override
    public Integer getId() {
        return value;
    }
    @Override
    public String getName() {
        if (value == 0) {
            return "low";
        }
        if (value == 1) {
            return "medium";
        }
        if (value == 2) {
            return "high";
        }
        return "unknown";
    }

}
