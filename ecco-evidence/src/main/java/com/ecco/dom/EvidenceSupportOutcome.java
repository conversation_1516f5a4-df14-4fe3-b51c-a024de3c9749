package com.ecco.dom;

import java.util.UUID;

import javax.persistence.*;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@javax.persistence.Entity
@Table(name="evdnc_supportoutcomes")
@NamedQueries(@NamedQuery(name = EvidenceSupportOutcome.BULK_UPDATE_AUTHOR_QUERY, query = "update EvidenceSupportOutcome set author = :newContact where author = :oldContact"))
public class EvidenceSupportOutcome extends EvidenceOutcome implements ServiceRecipientId {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceSupportOutcome.bulkUpdateAuthor";

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    private BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;

    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceSupportWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceSupportWork work;


    public static class Builder {

        private final EvidenceSupportOutcome o;

        private Builder(EvidenceSupportOutcome o) {
            this.o = o;
        }

        public EvidenceSupportOutcome build() {
            return o;
        }
    }

    protected Builder build() {
        return new Builder(this);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceSupportOutcome o = new EvidenceSupportOutcome(serviceRecipientId);
        return new Builder(o);
    }


    public EvidenceSupportOutcome() {
        // for hibernate etc
    }

    public EvidenceSupportOutcome(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

    public EvidenceWork getWork() {
        return work;
    }
    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    @Override
    public Integer getServiceRecipientId() {
        return identifier(serviceRecipient);
    }

    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    public void setWork(EvidenceSupportWork work) {
        this.work = work;
    }
}
