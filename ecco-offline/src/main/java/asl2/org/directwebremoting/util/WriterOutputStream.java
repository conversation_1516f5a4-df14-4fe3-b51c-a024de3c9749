/*
 * Copyright 2005 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package asl2.org.directwebremoting.util;

import org.jspecify.annotations.NonNull;

import java.io.IOException;
import java.io.Writer;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;

/**
 * This is not the evil hack you are looking for.
 *
 * <AUTHOR> [joe at getahead dot ltd dot uk]
 */
public final class WriterOutputStream extends ServletOutputStream {

    /**
     * The destination of all our printing
     */
    private Writer writer;

    /**
     * What string encoding should we use
     */
    private String encoding = null;

    /**
     * Buffer for write(int)
     */
    private byte[] buffer = new byte[1];


    /**
     * ctor using platform default encoding
     */
    public WriterOutputStream(Writer writer) {
        this.writer = writer;
    }

    /**
     * ctor that allows us to specify how strings are created
     */
    public WriterOutputStream(Writer writer, String encoding) {
        this.writer = writer;
        this.encoding = encoding;
    }

    @Override
    public void print(String s) throws IOException {
        writer.write(s);
    }

    @Override
    public boolean isReady() {
        return false;
    }

    @Override
    public void setWriteListener(WriteListener writeListener) {
        throw new IllegalStateException("Async not supported");
    }

    @Override
    public void write(@NonNull byte[] ba) throws IOException {
        if (encoding == null) {
            writer.write(new String(ba));
        } else {
            writer.write(new String(ba, encoding));
        }
    }

    @Override
    public void write(@NonNull byte[] ba, int off, int len) throws IOException {
        if (encoding == null) {
            writer.write(new String(ba, off, len));
        } else {
            writer.write(new String(ba, off, len, encoding));
        }
    }

    @Override
    public synchronized void write(int bite) throws IOException {
        buffer[0] = (byte) bite;
        write(buffer);
    }

    @Override
    public void close() throws IOException {
        if (writer != null) {
            writer.close();
            writer = null;
            encoding = null;
        }
    }

    @Override
    public void flush() throws IOException {
        writer.flush();
    }
}
