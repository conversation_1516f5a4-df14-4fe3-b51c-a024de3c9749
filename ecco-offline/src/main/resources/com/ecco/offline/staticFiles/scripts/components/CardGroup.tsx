import {Card as <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Grid} from "@eccosolutions/ecco-mui";
import {CardData, CardGroup, compareCards} from "ecco-components";
import * as React from "react";
import {SessionData} from "ecco-dto";

/**
 * A CardGroup is a CardData that can contain multiple cards, has a header, and can possibly be constrained to render
 * only a limited number of the cards in the group with the option to expand using "more...".
 *
 * The Cards in a CardGroup can be provided inline, or via a 'cards' property which is an Observable<CardData>
 * e.g.
 * <pre>
 * <CardContainer searchInput={} >
 *     <CardGroup title="appointments">
 *         <CardGroup title="today">
 *             {eventsForToday}
 *         </CardGroup>
 *         <CardGroup title="tomorrow">
 *             {eventsForToday}
 *         </CardGroup>
 *         <CardGroup title="previous 7 days" cards={cardsObservable}/>
 *     </CardContainer>
 *  </pre>
 */

interface Props<T extends CardData> {
    title: string;
    group: CardGroup<T>;
    cardFactory: (card: T, serviceChosenId: number, sessionData: SessionData) => JSX.Element;
    serviceChosenId: number;
    sessionData: SessionData;
}

interface State<T extends CardData> {
    loaded: boolean;
    cards: T[];
}

function binaryFind<T>(array: T[], searchElement: T, compare: (a: T, b: T) => number):
    ({found: boolean, index: number}) {

    let minIndex = 0;
    let maxIndex = array.length - 1;

    if (maxIndex < 0) {
        return {found: false, index: 0}
    }

    let currentIndex: number = (minIndex + maxIndex) / 2 | 0;
    let currentElement: T = array[currentIndex];

    while (minIndex <= maxIndex) {
        if (compare(currentElement, searchElement) < 0) {
            minIndex = currentIndex + 1;
        }
        else if (compare(currentElement, searchElement) > 0) {
            maxIndex = currentIndex - 1;
        }
        else {
            return {
                found: true,
                index: currentIndex
            };
        }
        currentIndex = (minIndex + maxIndex) / 2 | 0; // Binary hack. Faster than Math.floor
        currentElement = array[currentIndex];
    }

    return {
        found: false,
        index: compare(currentElement, searchElement) < 0 ? currentIndex + 1 : currentIndex
    };
}

class CardGroupComponent<T extends CardData> extends React.Component<Props<T>, State<T>> { // implements CardData {

    static of<T extends CardData>() {
        return CardGroupComponent as any as new () => CardGroupComponent<T>;
    }

    constructor(props: Props<T>) {
        super(props);
        this.state = {
            loaded: false,
            cards: []
        };
    }

    override componentDidMount(): void {
        this.props.group.cards.subscribe(
            card => this.addCard(card),
            e => this.error(e),
            () => { this.setState({loaded: true}) }
        );
    }

    // a re-render on search input change will cause the results to be filtered
    override render() {
        const cards = !this.state.loaded || this.state.cards.length == 0
            ? <>
                <Grid item xs/>
                <Grid item className="card" md={4} sm={6} xs={12}>
                    <CardUI>
                        <CardHeader title={this.state.loaded ? "none" : "loading..."}/>
                   </CardUI>
                </Grid>
                <Grid item xs/>
            </>
            : this.state.cards
                .map(card => <Grid item key={card.getKey()} md={4} sm={6} xs={12}>
                    {this.props.cardFactory(card, this.props.serviceChosenId, this.props.sessionData)}
                </Grid>);

        return (
            <>
                <Grid item key="title" xs={12}>
                    <h4>{this.props.title}</h4>
                    {this.state.cards.length > 0 && !this.state.loaded && "loading..."}
                </Grid>
                {cards}
            </>
        );
    }

    private error(e: any): void {
        console.error("error loading cards: %o", e); // TODO: add a card representing the error?  Or do a Snack alert
    }


    // TODO: render() should be based on the filterCriteria property (or state??) see pulling up state, such that

    private addCard(card: T): void {
        this.setState( (state: State<T>) => {
            const find = binaryFind(state.cards, card, compareCards);
            const cards = state.cards.slice();
            cards.splice(find.index,0, card);
            state.cards = cards;
            return state;
        });
    }
}

export = CardGroupComponent
