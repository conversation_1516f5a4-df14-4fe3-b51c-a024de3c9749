import $ = require("jquery");
import URI = require("URI");

import MvcUtils = require("../mvc/MvcUtils");
import {CustomFormWithSignatureForm} from "../referral/components/signedCustomForm";

// expect /evidence-snapshot/{bldg|rfrl}/{entityId}
let uri = URI(window.location.href);
const pathParts = MvcUtils.getAppPathComponents(uri);
const pageType = pathParts[pathParts.length - 3];
const serviceRecipientType = pathParts[pathParts.length - 2];
const entityId = parseInt(pathParts[pathParts.length - 1]);
const taskName = URI.parseQuery(uri.query())["taskName"];
const workUuid = URI.parseQuery(uri.query())["workUuid"];
const fromLastSignedSnapshot = URI.parseQuery(uri.query())["fromLastSignedSnapshot"];

// gets enhanced for textares in /printable - see commit msg
CustomFormWithSignatureForm.enhanceForPrinting($("#main-content"), () => {}, entityId, taskName, taskName, null, workUuid, !!fromLastSignedSnapshot);

$("#main-content-wrapper").css({"padding-left": "0px", "padding-right": "0px"})
$("body").css({'width': '210mm'});
$(".container").css({'width': '210mm'});
