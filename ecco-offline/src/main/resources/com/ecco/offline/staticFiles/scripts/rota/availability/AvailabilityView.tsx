import {EccoDate} from "@eccosolutions/ecco-common";
import {Grid, ButtonGroup, Button, Paper, Box} from "@eccosolutions/ecco-mui";
import {DomElementContainer} from "ecco-components-core";
import * as React from "react";
import {FC, useEffect, useState} from "react";
import {AjaxService} from "./availability-ajax";
import {AvailabilityGrid} from "./AvailabilityGrid";

interface Props {
    calendarId: string
    mode: "today" | "month"
}

const service = new AjaxService();
const view = new AvailabilityGrid(service);

export const AvailabilityView: FC<Props> = (props) => {
    const {mode, calendarId} = props;

    const [startDate, setStartDate] = useState(() => mode == "today" ? EccoDate.todayLocalTime() : EccoDate.todayLocalTime().withDate(1));

    const endDate = mode == "today" ? startDate.addDays(1) : startDate.addMonths(1);

    const prev = () => {
        setStartDate(mode == "today" ? startDate.subtractDays(1) : startDate.subtractMonths(1));
    };
    const now = () => {
        const today = EccoDate.todayLocalTime();
        setStartDate(mode == "today" ? today : today.withDate(1));
    };
    const next = () => {
        setStartDate(mode == "today" ? startDate.addDays(1) : startDate.addMonths(1));
    };

    useEffect(() => {
        view.loadCalendar(calendarId, startDate, endDate);
    }, [calendarId, startDate, endDate]);

    // TODO: Could add DatePicker to < today > nav
    const Actions = () =>
        <Paper elevation={2} className="v-gap-15">
            <Box p={1}>
                <Grid container alignItems="baseline">
                    <Grid item xs={12} style={{textAlign: "right"}}>
                        Import <ButtonGroup>
                            <Button onClick={() => view.importPattern(1)}>weekly</Button>
                            <Button onClick={() => view.importPattern(2)}>fortnightly</Button>
                            <Button onClick={() => view.importPattern(4)}>4 weekly</Button>
                        </ButtonGroup>
                    </Grid>
                    <Grid item>
                        Availability from {startDate.formatPretty()}
                    </Grid>
                    <Grid item xs // xs implies xs={true} which gives us flex-grow: 1 to take up space
                    />
                    <Grid item>
                        <ButtonGroup>
                            <Button onClick={prev}>&lt;</Button>
                            <Button onClick={now}>{mode == "today" ? "today" : "this month"}</Button>
                            <Button onClick={next}>&gt;</Button>
                        </ButtonGroup>
                    </Grid>
                    <Grid item>
                        <Button variant="contained" color="primary" onClick={() => {
                            view.save();
                        }}>save</Button>
                    </Grid>
                    <Grid item xs/>
                    <Grid item>
                        Shift-click (and drag) to erase
                    </Grid>
                </Grid>
            </Box>
        </Paper>;

    return <div className="v-gap-15">
        <Actions/>
        <DomElementContainer content={view.domElement()} />
    </div>;
};
