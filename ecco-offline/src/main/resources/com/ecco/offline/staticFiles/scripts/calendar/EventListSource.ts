import {Observable} from "rxjs";

import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {CardSource, loadChunks} from "ecco-components";
import {EventCard, EventCardGroup} from "./cards";
import {filter, flatMap, map} from "rxjs/operators";
import {
    CalendarRepository,
    SessionDataRepository,
    EventResourceDtoWithUserCalendar,
} from "ecco-dto";
import services = require("ecco-offline-data");
import {RotaRepository} from "ecco-rota";
//import {isOffline} from "ecco-dto";
// import {ReferralAjaxRepository} from "referral/ReferralAjaxRepository"
// import environment = require("../environment"); // WARNING online only
// const referralAjaxRepository = new ReferralAjaxRepository(environment.apiClient);


/** Displayed in dashboards -> my -> events - see DashboardAppBar /events & offline /events - see offline/router  */
class EventListSource implements CardSource {

    /**
     * Now calls EventController /calendarIds, not /nearby - so that we are calling the same method when we need a wider date range
     * so we replicate the 'nearby' as -3days and +2 weeks as per the current server impl of /nearby.
     */
    public static getMyNearbyData(nearby: EccoDate, sessionDataRepo: SessionDataRepository, calendarRepo: CalendarRepository): Promise<EventResourceDtoWithUserCalendar[]> {
        return this.getMyEventsByRange(nearby.subtractDays(3), nearby.addDays(7*2), sessionDataRepo, calendarRepo);
    }

    /**
     * Load visits for the carer - see CareEventSource
     */
    public static getMyEventsByRange(from: EccoDate, to: EccoDate, sessionDataRepo: SessionDataRepository, calendarRepo: CalendarRepository): Promise<EventResourceDtoWithUserCalendar[]> {
        return sessionDataRepo.getSessionData()
                .then(sessionData =>
                        calendarRepo.fetchEventsByCalendarIdAndDate([sessionData.getDto().calendarId], from, to)
                                .then(events =>
                                        events.map(event => {
                                            let eventWith = <EventResourceDtoWithUserCalendar> event;
                                            eventWith.calendarIdUserReferenceUri = sessionData.getDto().calendarIdUserReferenceUri;
                                            return eventWith;
                                        })
                                )
                );
    }

    /**
     * Load missed visits for the carer - from the scheduler, which uses CalendarEventSnapshot
     */
    public static getMyEventsSnapshotMissed(before: EccoDate, sessionDataRepo: SessionDataRepository, calendarRepository: CalendarRepository, rotaRepo: RotaRepository): Promise<EventResourceDtoWithUserCalendar[]> {
        return sessionDataRepo.getSessionData()
            .then(sessionData =>
                rotaRepo.findMyMissedEventSnapshotsBeforeUtc(before.toDateTimeMidnight(), sessionData.getDto().individualUserSummary.individualId).then(missed => {
                    // load the actual events from the snapshots
                    const singlePromise = (ids: string[]) => calendarRepository.fetchEventsById(ids).then(events =>
                        events.map(e => {
                            let eventWith = <EventResourceDtoWithUserCalendar> e;
                            eventWith.calendarIdUserReferenceUri = sessionData.getDto().calendarIdUserReferenceUri;
                            return eventWith;
                        })
                    );
                    return loadChunks(
                            missed.map(e => e.eventUid),
                            singlePromise);
                })
            )
    }

    getCards() {
        const eventsQ = EventListSource.getMyNearbyData(EccoDate.todayLocalTime(), services.getFeatureConfigRepository(), services.getCalendarRepository());

        // .then(subject.publish(events)??

        // const referralsQ = isOffline() ? Observable.empty() :
        //     Observable.fromPromise(eventsQ).flatMap<EventResourceDto>(a => a)
        //         .map(event => event.)

        // const username = userSessionMgr.findCurrentUserSession()
        //     .then( sessionData => sessionData.getUserDevice().getUsername());

        //services.getReferralRepository
        //referralAjaxRepository.

        const comingEvents = Observable.fromPromise(eventsQ).pipe(
            flatMap(a => a),
            filter( event => EccoDate.parseIso8601FromDateTime(event.start as string)
                .laterThanOrEqual(EccoDate.todayLocalTime())
            ),
            filter( event => EccoDate.parseIso8601FromDateTime(event.start as string)
                .earlierThan(EccoDate.todayLocalTime().addDays(2))
            ),
            map(dto => new EventCard(dto)));

        const pastEvents = Observable.fromPromise(eventsQ).pipe(
            flatMap(a => a),
            filter( event => EccoDate.parseIso8601FromDateTime(event.start as string)
                .earlierThan(EccoDate.todayLocalTime())
            ),
            map(dto => new EventCard(dto)));

        return Observable.from([
            new EventCardGroup("appointments coming up", comingEvents, EccoDateTime.nowUtc().addDays(0)),
            //new EventCardGroup("appointments tomorrow", tomorrowsEvents, EccoDateTime.nowUtc().addDays(1)),
            new EventCardGroup("appointments past 7 days", pastEvents, EccoDateTime.nowUtc().addDays(7))]);
    }
}
export = EventListSource;
