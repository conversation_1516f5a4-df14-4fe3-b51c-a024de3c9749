import $ = require("jquery");
import commands = require("./commands");
import EditableComponent = require("./EditableComponent");
import {Conversion, WebApiError} from "@eccosolutions/ecco-common";
import {ServiceRecipientAttributeChangeCommand} from "ecco-commands";
import {apiClient} from "ecco-components";
import {CommandAjaxRepository} from "./CommandAjaxRepository";

/*
 * NOTE: Exports Editable. Load editableInit from JS to bootstrap this. From editable-test.ts we must duplicate
 * the code in editableInit.ts
 */
const commandRepository = new CommandAjaxRepository(apiClient);

/**
 * Find items with data-entity-type data-entity-id attributes, and then within those
 * find class="editable-text", "editable-date" etc and enhance.
 */
class Editable {

    constructor() {
    }

    /** Find the different items we support and attach the appropriate component to each of them */
    public attach() {
        const $roots = $(".entityForm");
        $roots.each((index, form) => {
            this.enhanceForm(form);
        });
    }

    private enhanceForm(element:Element) {

        const entityType = element.getAttribute("data-entity-type");
        const entityId = element.getAttribute("data-entity-id");
        const refreshUrl = element.getAttribute("data-refresh-url"); // if we've got this, call it after success
//        console.log( entityType + ":" + entityId );

        const $editableTexts = $(".editable-text", element);
        $editableTexts.each( (index, elem) => {
            // for -text
            const editableField = new EditableText(entityType, entityId, refreshUrl);
            editableField.enhanceField($(elem));
        });

        const $editableDates = $(".editable-date", element);
        $editableDates.each( (index, elem) => {
            // for -text
            const editableField = new EditableDate(entityType, entityId, refreshUrl);
            editableField.enhanceField($(elem));
        });
    }
}

class EditableComponentCommand extends EditableComponent {
    private path: string;
    private attributeType: string;

    constructor(private entityType: string, private entityId: string, private refreshUrl: string) {
        super();
    }

    public override readDataAttributes($container: $.JQuery) {
        const children = $container.children();
        if (children.length > 0) {
            throw new Error(`editable-* should only be used on elements containing plain text data: [${$container.html()}]`);
        }

        this.attributeType = $container.attr("data-ecco-attribute-type");
        this.entityType = $container.attr("data-ecco-type") || $container.attr("data-entity-type") || this.entityType;
        this.entityId = $container.attr("data-ecco-id") || $container.attr("data-entity-id") || this.entityId;
        this.path = $container.attr("data-ecco-path") || $container.attr("data-path");
    }

    public sendCommand(command: commands.UpdateCommand): void {
        let commandSentPromise: Promise<void>;

        if (this.entityType === "referral") {
            commandSentPromise = commandRepository.postReferralUpdateCommand(this.entityId, this.path, command);
        }
        else if (this.entityType === "client") {
            commandSentPromise = commandRepository.postClientUpdateCommand(this.entityId, this.path, command);
        } else if (this.entityType === "serviceRecipient") {
            const srId = Number(this.entityId);
            const attributeCmd = new ServiceRecipientAttributeChangeCommand(srId, this.attributeType, this.path);
            attributeCmd.changeValue(command.oldValue, command.newValue);
            commandSentPromise = commandRepository.postServiceRecipientAttributeCommand(srId, attributeCmd.getCommandDto());
        }

        commandSentPromise
            .then( () => { this.editCommitted(); this.triggerAjaxRefresh();})
            .catch( (error: WebApiError) => {
                alert(error.reason.message);
                this.replaceWithTextSpan();
            });
    }

    private triggerAjaxRefresh() {
        if (this.refreshUrl){
            $.post(this.refreshUrl);
        }
    }
}

class EditableDate extends EditableComponentCommand {

    private $input: $.JQuery;
    private currentDateIso: string;

    constructor(entityType: string, entityId: string, refreshUrl: string) {
        super(entityType, entityId, refreshUrl);
    }

    override makeEditable() {
        this.currentDateIso = this.getContainer().attr("data-raw");

        this.getTextSpan().detach();
        this.$input = $("<input>").attr("type","text")
            .attr("placeholder", "DD/MM/YYYY")
            .val(this.currentAsDDMMYYYY());
        const $form = $("<form>").addClass("editable editable-date")
            .append(this.$input);
        this.addSaveCancel($form);
        this.getContainer().append($form);
    }

    /**
     * get data-raw as DD/MM/YYYY
     */
    private currentAsDDMMYYYY() {
        return Conversion.convertDateFormats(this.currentDateIso, "YYYY-MM-DD", "DD/MM/YYYY");
    }

    override submitEdit(event: $.JQueryEventObject) {
        super.submitEdit(event);
        try {
            const iso = this.getInputAsIso();
            const command = new commands.DateUpdateCommand(this.currentDateIso, iso);
            this.sendCommand(command);
        } catch (e) {
            alert(e.message);
        }
    }

    private getInputAsIso() {
        return Conversion.convertDateFormats(this.$input.val(), "DD/MM/YYYY", "YYYY-MM-DD");
    }

    private getInputAsDDMMMYYYY() {
        return Conversion.convertDateFormats(this.$input.val(), "DD/MM/YYYY", "DD-MMM-YYYY");
    }

    // span should be set to DD-MMM-YYYY format, iso to iso
    override editCommitted() {
        this.getContainer().attr("data-raw", this.getInputAsIso());
        this.getTextSpan().text(this.getInputAsDDMMMYYYY());
        this.replaceWithTextSpan();
    }
}

class EditableText extends EditableComponentCommand {

    private $input: $.JQuery;

    constructor(entityType: string, entityId: string, refreshUrl: string) {
        super(entityType, entityId, refreshUrl);
    }

    override makeEditable() {
        this.getTextSpan().detach();
        this.$input = $("<input>").attr("type","text").val(this.getTextSpan().text());
        const $form = $("<form>").addClass("editable editable-text")
            .append(this.$input);
        this.addSaveCancel($form);
        this.getContainer().append($form);
    }

    override submitEdit(event: $.JQueryEventObject) {
        super.submitEdit(event);
        const command = new commands.TextUpdateCommand(this.getTextSpan().text(), this.$input.val());
        this.sendCommand(command);
    }

    override editCommitted() {
        this.getTextSpan().text(this.$input.val());
        this.replaceWithTextSpan();
    }
}

export default Editable
