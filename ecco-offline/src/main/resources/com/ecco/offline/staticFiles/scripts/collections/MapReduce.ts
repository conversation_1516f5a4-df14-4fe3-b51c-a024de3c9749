
export interface Iterator<TElement> {
    hasNext(): boolean;
    next(): TElement;
}

export interface Iterable<TElement> {
    iterator(): Iterator<TElement>;
    
    /** With each element ... call callback */
    each( callback: (item: TElement, index: number) => void ): void;
}


export interface Group<TElement> {
    key: string;
    elements: MapReduce<TElement>;
}


export interface MapReduce<TElement> extends Iterable<TElement> {

    concat( sequence: MapReduce<TElement> ): MapReduce<TElement>;

    /** Map a function over a list and concatenate the results */
    concatMap<TResult>(f: (element: TElement) => MapReduce<TResult>): MapReduce<TResult>;

    map<TResult>(f: (element: TElement) => TResult): MapReduce<TResult>;

    filter(predicate: (element: TElement) => boolean): MapReduce<TElement>;

    reduce<TResult>(initial: TResult, f: (accumulator: TResult, element: TElement) => TResult): TResult;

    reduce1(f: (accumulator: TElement, element: TElement) => TElement): TElement;

    groupBy(f: (element: TElement) => string): MapReduce<Group<TElement>>;

    count(): number;
}

/** This may be better replaced with a FlattenedSequence - see Lazy.js */
class ConcatenatedSequence<TElement> implements Iterable<TElement> {

    constructor(private first: Iterable<TElement>, private second: Iterable<TElement>) {
    }

    public iterator() {
        var firstItr = this.first.iterator();
        var secondItr = this.second.iterator();
        return {
            hasNext: () => firstItr.hasNext() || secondItr.hasNext(), // TO REVIEW: Do we guarantee that hasNext doesn't mutate anything?
            next: () => firstItr.hasNext() ? firstItr.next() : secondItr.next()
        };
    }
    public each( callback: (item: TElement, index: number) => void ) {
        var iterator = this.iterator(), i = 0;
        while(iterator.hasNext()) {
            callback(iterator.next(), i++);
        }
    }
}


//class FlattenedSequence<TElement> implements Iterable<TElement> {
//
//    constructor(private parent: Iterable<TElement>) {
//    }
//
//    public iterator() {
//        var firstItr = this.parent.iterator();
//        return {
//            hasNext: ?, 
//            next: ?
//        };
//    }
//    public each( callback: (item: TElement, index: number) => void ) {
//        var iterator = this.iterator(), i = 0;
//        while(iterator.hasNext()) {
//            callback(iterator.next(), i++);
//        }
//    }
//}

export class IterableMapReduce<TElement> implements MapReduce<TElement> {
    constructor(private source: Iterable<TElement>) {
    }

    public iterator(): Iterator<TElement> {
        return this.source.iterator();
    }

    public concat( sequence: MapReduce<TElement> ): MapReduce<TElement> {
        var iterable: Iterable<TElement> = new ConcatenatedSequence(this.source, sequence);
        return new IterableMapReduce(iterable);
    }

    /** Map a function over a list and concatenate the results */
    public concatMap<TResult>(f: (element: TElement) => MapReduce<TResult>): MapReduce<TResult> {

        return null; // TODO
    }

    public map<TResult>(f: (element: TElement) => TResult): MapReduce<TResult> {
        var result: Iterable<TResult> = {
            iterator: () => {
                var sourceIterator = this.source.iterator();
                return {
                    hasNext: () => sourceIterator.hasNext(),
                    next: () => f(sourceIterator.next())
                };
            },
            each: function( callback: (item: TResult, index: number) => void ) {
                throw new Error();
            }
        };
        return new IterableMapReduce<TResult>(result);
    }

    public filter(predicate: (element: TElement) => boolean): MapReduce<TElement> {
        var result: Iterable<TElement> = {
            iterator: () => {
                var sourceIterator = this.source.iterator();
                var havePeekedNext = false;
                var peekedHasNext = false;
                var peekedNext: TElement = null;
                function peekNext(): TElement {
                    if (!havePeekedNext) {
                        peekedHasNext = false;
                        while (sourceIterator.hasNext() && !peekedHasNext) {
                            peekedNext = sourceIterator.next();
                            peekedHasNext = predicate(peekedNext);
                        }
                        havePeekedNext = true;
                    }
                    return peekedNext;
                }
                function hasNext(): boolean {
                    peekNext();
                    return peekedHasNext;
                }
                function next(): TElement {
                    var next = peekNext();
                    havePeekedNext = false;
                    return next;
                }

                return {
                    hasNext: hasNext,
                    next: next
                };
            },
            each: function( callback: (item: TElement, index: number) => void ) {
                throw new Error();
            }
        };
        return new IterableMapReduce(result);
    }

    public reduce<TResult>(initial: TResult, f: (accumulator: TResult, element: TElement) => TResult): TResult {
        var sourceIterator = this.source.iterator();
        var accumulator = initial;
        while (sourceIterator.hasNext()) {
            accumulator = f(accumulator, sourceIterator.next());
        }
        return accumulator;
    }

    /** Reduce where first item is the initial value of the accumulator */
    public reduce1(f: (accumulator: TElement, element: TElement) => TElement): TElement {
        var sourceIterator = this.source.iterator();
        if (!sourceIterator.hasNext()) {
            throw new Error("Can't call reduce1 on empty list.");
        }
        var accumulator = sourceIterator.next();
        while (sourceIterator.hasNext()) {
            accumulator = f(accumulator, sourceIterator.next());
        }
        return accumulator;
    }

    public groupBy(f: (element: TElement) => string): MapReduce<Group<TElement>> {
        var result = this.reduce({}, (accumulator: {[k:string]: Array<TElement>}, element: TElement) => {
            var key = f(element);
            if (!(accumulator[key] instanceof Array)) {
                accumulator[key] = [element];
            }
            else {
                accumulator[key].push(element);
            }
            return accumulator;
        });

        // Need to iterate over properties in result object, and each Group passes in the 
        var iterable: Iterable<Group<TElement>> = null; // TODO
        return new IterableMapReduce(iterable);
    }

    public count(): number {
        return this.reduce(0, (count) => count + 1);
    }

    public each( callback: (item: TElement, index: number) => void ) {
        this.reduce( 0, (count, element) => { callback(element, count); return count + 1; });
    }
}

export class ArrayIterable<TElement> implements Iterable<TElement> {
    constructor(private array: TElement[]) {
    }

    public iterator(): Iterator<TElement> {
        var i = 0;
        return {
            hasNext: () => i < this.array.length,
            next: () => this.array[i++]
        };
    }

    public each( callback: (item: TElement, index: number) => void ) {
        this.array.forEach( callback );
    }
}

export class ArrayMapReduce<TElement> extends IterableMapReduce<TElement> {
    constructor(private array: TElement[]) {
        super(new ArrayIterable(array));
    }
}
