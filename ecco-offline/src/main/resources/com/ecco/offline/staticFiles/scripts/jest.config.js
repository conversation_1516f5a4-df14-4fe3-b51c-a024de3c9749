// This should be specified in Configurations -> Templates -> Jest as the config file
module.exports = {

    transform: {
        "^.+\\.tsx?$": "ts-jest",
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(tsx?)$",
    setupFiles: ["./__tests__/setupJest.ts"],
    moduleFileExtensions: [
        "ts",
        "tsx",
        "js",
        "json",
        "node"
    ],
    moduleNameMapper: {
        "services": "<rootDir>/__mocks__/null",
        "punycode": "<rootDir>/__mocks__/null",
        "IPv6": "<rootDir>/__mocks__/null",
        "SecondLevelDomains": "<rootDir>/__mocks__/null"
    },
    modulePaths: [
        "<rootDir>",
        "<rootDir>/lib"
    ],
    snapshotSerializers: ["enzyme-to-json/serializer"],
    // collectCoverage: true,
    // mapCoverage: true
};
