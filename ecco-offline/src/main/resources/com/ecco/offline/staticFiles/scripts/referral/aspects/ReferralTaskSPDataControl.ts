import $ = require("jquery");
import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import TextInput = require("../../controls/TextInput");
import ListDefSelect2List = require("../../data-attr/ListDefSelect2List");
import {SessionDataService} from "../../feature-config/SessionDataService";
import {StringToNumberMap, StringToStringMap} from "@eccosolutions/ecco-common";
import {ReferralTaskSPDataCommand} from "ecco-commands";
import {apiClient} from "ecco-components";
import {ClientAjaxRepository, ReferralAjaxRepository, ReferralDto, SessionData} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";
import {Agency} from "ecco-dto/contact-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import {ContactsAjaxRepository} from "ecco-dto";

const contactRepository = new ContactsAjaxRepository(apiClient);
const clientRepository = new ClientAjaxRepository(apiClient);
const referralRepository = new ReferralAjaxRepository(apiClient);


class BackingData {
    constructor(public sessionData: SessionData,
        public referral: ReferralDto,
        public client: Client,
        public referrerAgency: Agency) {
    }
}

class ReferralTaskSPDataControl extends BaseAsyncCommandForm<BackingData> {

    public static showInModal(serviceRecipientId: number, taskName: string, taskHandle: string, onComplete: () => void) {
        const form = new ReferralTaskSPDataControl(serviceRecipientId, taskName, taskHandle, onComplete);
        showFormInModalDom(form);
        form.load();
    }

    private initValue: StringToNumberMap = {};
    private initTextValue: StringToStringMap = {};
    private cmd = new ReferralTaskSPDataCommand("update", this.serviceRecipientId,
        this.taskName, this.taskHandle);

    constructor(private serviceRecipientId: number, private taskName: string, private taskHandle: string,
                private onComplete: () => void) {
        super("Supporting People data");
        this.setOnFinished(() => this.loadNextPage());
    }

    protected fetchViewData(): Promise<BackingData> {
        return referralRepository.findOneReferralByServiceRecipientId(this.serviceRecipientId)
            .then( (referral) => {
                return clientRepository.findOneClientByReferralId(referral.referralId)
                    .then( (client) => {
                        return SessionDataService.getFeatures().then( (sessionData) => {
                            if (referral.referrerAgencyId) {
                                return contactRepository.findOneAgency(referral.referrerAgencyId)
                                    .then( (agency) => {
                                        return new BackingData(sessionData, referral, client, agency);
                                    });
                            } else {
                                return new BackingData(sessionData, referral, client, null);
                            }
                        });
                    });
                });
    }

    protected render(data: BackingData): void {
        this.element().empty();

        if (this.taskName == "initial-sp_data") {
            this.createList(data, "6c economic status", "spdata_economicStatus", "spdata_economicStatus");
            this.createList(data, "6d has partner", "spdata_yesNo", "spdata_clientHasPartner");
            this.createList(data, "6e economic status partner", "spdata_economicStatus", "spdata_economicStatusPartner");
            this.createList(data, "6f children under 16", "spdata_yesNo", "spdata_hasChildren");
            this.createList(data, "6g number children under 16", "spdata_1-10", "spdata_numberChildren");
            this.element().append(this.createRowElement("7 nat. ins.", $("<span>")
                        .text(data.client.ni ? data.client.ni : '(complete in main file)')));
            this.createList(data, "7 reason no nat. ins.", "spdata_noNatIns", "spdata_noNatIns");
            this.createList(data, "8a disabled person", "spdata_yesNoDontKnow", "spdata_hasDisability");
            this.createList(data, "8b primary disability", "spdata_disabilityType", "spdata_disabilityType");
            this.createList(data, "9 ethnic group", "spdata_ethnicGroup", "spdata_ethnicGroup");
            this.createList(data, "10a client religion", "spdata_clientReligion", "spdata_clientReligion");
            /* probably not using this approach as it involves some complexity on data import
            this.element().append(this.createRowElement("9 ethnic group", $("<span>")
                        .text(data.client.ethnicOrigin ? data.client.ethnicOrigin : '(complete in main file)')));
            this.element().append(this.createRowElement("10a religion", $("<span>")
                        .text(data.client.religion ? data.client.religion : '(complete in main file)')));
            */
            this.createList(data, "10b sexual orientation", "spdata_sexualOrientation", "spdata_sexualOrientation");
            this.createList(data, "10c consider themselves transgender", "spdata_yesNoDontKnowNotDisclose", "spdata_transgender");
            this.createList(data, "10d ex-armed forces", "spdata_yesNoDontKnowNotDisclose", "spdata_exArmedForces");
            this.createList(data, "11a primary client group", "spdata_clientGroup", "spdata_primaryClientGroup");
            this.createList(data, "11b secondary client group 1", "spdata_clientGroup", "spdata_secondary1ClientGroup");
            this.createList(data, "11c secondary client group 2", "spdata_clientGroup", "spdata_secondary2ClientGroup");
            this.createList(data, "11d secondary client group 3", "spdata_clientGroup", "spdata_secondary3ClientGroup");
            this.createList(data, "12a.i accepted under Care Management (Social Services)", "spdata_yesNoDontKnow", "spdata_socialServices");
            this.createList(data, "12a.ii accepted under Secondary Mental Health Services", "spdata_yesNoDontKnow", "spdata_mentalHealthServices");
            this.createList(data, "12a.iii accepted under Probation or YOTs", "spdata_yesNoDontKnow", "spdata_probationYOTs");
            this.createList(data, "12a.iv accepted under DIP", "spdata_yesNoDontKnow", "spdata_dip");
            this.createList(data, "12b immediately prior to support, this client was", "spdata_priorHousingStatus", "spdata_priorHousingStatus");
            this.createList(data, "12c.i assessed as higher risk under CPA", "spdata_yesNoDontKnow", "spdata_highRiskCPA");
            this.createList(data, "12c.ii assessed as higher risk under MAPPA", "spdata_yesNoDontKnow", "spdata_highRiskMAPPA");
            this.createList(data, "12c.iii assessed as higher risk under MARAC", "spdata_yesNoDontKnow", "spdata_highRiskMARAC");
            this.createList(data, "12d subject to ASBO/CBO", "spdata_yesNoDontKnow", "spdata_ASBO_CBO");
            this.createList(data, "12e history of leaving care", "spdata_yesNoDontKnow", "spdata_historyLeavingCare");
            this.createList(data, "12f has received care 2 years prior to support", "spdata_yesNoDontKnow", "spdata_history2Years");
            /* probably not using this approach as it involves some complexity on data import
            var sourceOfReferral = data.referral.source;
            if (data.referrerAgency) {
                sourceOfReferral = data.referrerAgency.agencyCategory + " (" + sourceOfReferral + ")";
            }
            this.element().append(this.createRowElement("source of referral", $("<span>")
                        .text(sourceOfReferral ? sourceOfReferral : '(complete in main file)')));
            */
            this.createList(data, "13 source of referral", "spdata_sourceOfReferral", "spdata_sourceOfReferral");
            this.createList(data, "14 type of referral", "spdata_typeOfReferral", "spdata_typeOfReferral");
            this.createList(data, "15 accomm. status (starting support)", "spdata_accomType", "spdata_accomTypeStarting");
            this.createList(data, "16a accomm. status (prior to support)", "spdata_accomType", "spdata_accomTypePrior");
            this.createList(data, "16b same accom. now (as prior to support)", "spdata_yesNo", "spdata_continuingAccomm");
            this.createList(data, "16c.i&ii accomm. LA/ONS code (prior to support)", "spdata_LAONS", "spdata_LAONSPrior");
            this.createText(data, "16c.iii post code for accomm. (prior to support)", "spdata_postCodePrior");
            this.createText(data, "17a how long in this area (years)", "spdata_yearsInAreaPrior");
            this.createText(data, "17a how long in this area (months)", "spdata_monthsInAreaPrior");
            this.createText(data, "17a how long in this area (days)", "spdata_daysInAreaPrior");
            this.createList(data, "17b.i living in area < 6 months", "spdata_yesNo", "spdata_livingAreaLess6Months");
            this.createList(data, "17b.ii&iii if yes, what is the LA", "spdata_LAONS", "spdata_livingAreaLess6MonthsLAONS");
            this.createText(data, "17b.iv how long there (years)", "spdata_yearsInArea");
            this.createText(data, "17b.iv how long there (months)", "spdata_monthsInArea");
            this.createText(data, "17b.iv how long there (days)", "spdata_daysInArea");
            this.createText(data, "18 how long living in LA/ONS (prior to support) (years)", "spdata_yearsInONSAreaPrior");
            this.createText(data, "18 how long living in LA/ONS (prior to support) (months)", "spdata_monthsInONSAreaPrior");
            this.createText(data, "18 how long living in LA/ONS (prior to support) (days)", "spdata_daysInONSAreaPrior");
            this.createList(data, "is this record complete", "spdata_yesNo", "spdata_finalCheckStart");
        }

        if (this.taskName == "exit-sp_data") {
            this.createList(data, "type of accommodation (after support)", "spdata_accomType", "spdata_accomTypePost");
            this.createList(data, "which LA/ONS code (after support)", "spdata_LAONS", "spdata_LAONSPost");
            this.createList(data, "successful move or end", "spdata_yesNo", "spdata_successfulMove");
            this.createList(data, "result in greater independence", "spdata_yesNo", "spdata_moreIndependent");
            this.createList(data, "is this record complete", "spdata_yesNo", "spdata_finalCheckEnd");
        }

    }

    private createList(data: BackingData, label: string, listName: string, key: string): void {
        const $listElem = this.createListElement(listName);
        const listCtrl = new ListDefSelect2List($listElem, undefined, (newValue) => {
            this.onListChange(key, newValue)
        }, true);
        this.createInitialValue(listCtrl, data, listName, key);
        this.element().append(this.createRowElement(label, $listElem));
    }

    private createText(data: BackingData, label: string, key: string): void {
        const $elem = $("<span>");
        const ctrl = new TextInput(key);
        ctrl.change((newValue: string) => { this.onTextChange(key, newValue) });
        const initValue = data.referral.textMap[key];
        ctrl.setVal(initValue);
        this.initTextValue[key] = initValue;
        $elem.append(ctrl.element());
        this.element().append(this.createRowElement(label, $elem));
    }

    // noinspection JSMethodCanBeStatic
    private createListElement(key: string): $.JQuery {
        const $container = $("<span>");
        $container.attr("list-name", key);
        const $loading = $("<select>").prop("disabled", true).append($("<option>").text("loading..."));
        $container.append($loading);
        return $container;
    }

    /**
     * Initial value can be from a previously saved value, but also from a default in the list
     * which needs to trigger a command ready for saving when the form is first opened
     */
    private createInitialValue(listCtrl: ListDefSelect2List, data: BackingData, listName: string, key: string) {
        let initValue = data.referral.choicesMap[key] ? data.referral.choicesMap[key].id : null;
        const defaultListValue = data.sessionData.getListDefinitionEntriesByListName(listName)
            .filter((entry) => entry.getDefault()).pop();

        if (initValue == null && defaultListValue != null) {
            this.onListChange(key, defaultListValue.getId().toString());
            initValue = defaultListValue.getId();
        }

        this.initValue[key] = initValue;
        listCtrl.init(data.sessionData, listName, initValue ? initValue.toString() : null);
    }

    // noinspection JSMethodCanBeStatic
    private createRowElement(label: string, $input: $.JQuery): $.JQuery {
        return $("<div>").attr("class", "e-row")
            .append($("<span>").attr("class", "e-label").attr("width", "28%").text(label))
            .append($("<span>").attr("class", "input").attr("width", "68%").append($input));
    }

    private onListChange(key: string, newValueStr: string) {
        const initValue = this.initValue[key];
        const newValue = newValueStr == "-1" ? null : parseInt(newValueStr);
        this.updateListCommand(key, initValue, newValue);
    }

    private onTextChange(key: string, newValue: string) {
        const initValue = this.initTextValue[key];
        this.updateTextCommand(key, initValue, newValue);
    }

    private updateListCommand(key: string, oldVal: number, newVal: number) {
        this.cmd.changeChoicesMapEntry(key, oldVal, newVal);

        if (this.cmd.hasChanges()) {
            if (this.commandQueue.size() == 0) {
                this.commandQueue.addCommand(this.cmd);
            }
            this.enableSubmit();
        } else {
            // turn off the ui changes if there is nothing to save (eg merged cmd results in nothing)
            this.disableSubmit();
            this.commandQueue.clear();
        }
    }

    private updateTextCommand(key: string, oldVal: string, newVal: string) {
        this.cmd.changeTextMapEntry(key, oldVal, newVal);

        if (this.cmd.hasChanges()) {
            if (this.commandQueue.size() == 0) {
                this.commandQueue.addCommand(this.cmd);
            }
            this.enableSubmit();
        } else {
            // turn off the ui changes if there is nothing to save (eg merged cmd results in nothing)
            this.disableSubmit();
            this.commandQueue.clear();
        }
    }

    private loadNextPage() {
        if (this.onComplete){
            this.onComplete();
        }
    }
}
export = ReferralTaskSPDataControl;
