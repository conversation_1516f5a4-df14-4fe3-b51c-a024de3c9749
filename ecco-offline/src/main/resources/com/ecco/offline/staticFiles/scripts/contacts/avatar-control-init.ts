import $ = require("jquery");

import AvatarControl from "./AvatarControl";

$(() => {
    $(".avatar").each((i, avatarElement) => {
        var $avatar = $(avatarElement);

        var contactId = parseInt($avatar.attr("data-contact-id"), 10);

        var imageIdAttr = $avatar.attr("data-image-id");
        var imageId = imageIdAttr && parseInt(imageIdAttr, 10);

        new AvatarControl(contactId, imageId).attach($avatar);
    });
});