import {EccoDate} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import {
    BuildingSearch,
    CommandForm,
    CommandSubform,
    EccoAPI,
    ModalCommandForm,
    SmallSpinner,
    useClient,
    useServicesContext
} from "ecco-components";
import {default as React, FC, useState} from "react";
import {mountWithServices} from "../offline/ServicesContextProvider";
import {ClientResidenceUpdateCommand} from "ecco-commands";

/** Button to show and edit the client residence.
 * NOTE: We are using the term Widget to indicate something that displays some data *and* allows it to be maniuplated */
const ClientResidenceWidget: FC<{srId: number, clientId: number, filterOutParents?: boolean | undefined}> = ({srId, clientId, filterOutParents}) => {
    const {client} = useClient(clientId);
    const [show, setShow] = useState(false);

    return <>
            <span className="button"
                  onClick={() => setShow(true)}
            >{client ? (client.residenceName || "- none selected -") : null}</span>
            {show && client && <ClientResidenceModal
                srId={srId}
                residenceId={client.residenceId}
                filterOutParents={filterOutParents}
                setShow={setShow}
            />}
        </>;

};

export function loadAndAttachClientResidence(element: HTMLElement, serviceRecipientId: number, clientId: number, filterOutParents = false) {
    mountWithServices(
        <ClientResidenceWidget clientId={clientId} srId={serviceRecipientId} filterOutParents={filterOutParents}/>,
        element, <SmallSpinner/>);
}


type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    setShow: (show: boolean) => void
    srId: number;
    residenceId: number | null;
    filterOutParents?: boolean | undefined;
    required?: boolean | undefined
}

interface Props extends ModalProps {
    readOnly: boolean;
    filterOutParents?: boolean | undefined;
}

interface State {
    residenceId: number | null;
}

export class ClientResidenceCommandForm extends CommandSubform<Props & LocalProps, State> {

    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {
            residenceId: props.residenceId
        }
    }

    getErrors(): string[] {
        const errors = [];
        if (this.props.required && !this.state.residenceId) {
            errors.push("you must select a residence")
        }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        // if there is a change
        if (this.props.residenceId != this.state.residenceId) {

            // Send a command with startDate indicating the date the change happens
            const cmd = new ClientResidenceUpdateCommand(this.props.srId, "residence")
                .changeResidence(this.props.residenceId, this.state.residenceId)
                .changeStartDate(null, EccoDate.todayLocalTime());

            commandQueue.addCommand(cmd);
        }
    }

    render() {
        return <BuildingSearch
                filterOutParents={this.props.filterOutParents}
                buildingId={this.state.residenceId}
                onChange={residenceId => this.setState({residenceId})}
                primaryOnly={false}
            />;
    }

}
/* @Exemplar - see usage */
export const ClientResidenceModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title="change residence"
            action="save"
            maxWidth="sm"
        >
            {form => <ClientResidenceCommandForm
                {...props}
                readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};

