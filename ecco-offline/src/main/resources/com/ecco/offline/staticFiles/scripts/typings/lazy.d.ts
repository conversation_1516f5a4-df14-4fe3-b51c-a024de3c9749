// Type definitions for Lazy.js 0.3.2
// Project: https://github.com/dtao/lazy.js/
// Definitions by: <PERSON> <https://github.com/Bartvds>
// Definitions: https://github.com/boris<PERSON>kov/DefinitelyTyped

declare module LazyJS {

    interface LazyStatic {
        (value: string): StringLikeSequence;
        <T>(value: T[]): ArrayLikeSequence<T>;
        <T>(value: Object): ObjectLikeSequence<T>;

        strict(): LazyStatic;

        generate<T>(generatorFn: GeneratorCallback<T>, length?: number): GeneratedSequence<T>;

        range(to: number): GeneratedSequence<number>;
        range(from: number, to: number, step?: number): GeneratedSequence<number>;

        repeat<T>(value: T, count?: number): GeneratedSequence<T>;
    }

    type Callback = () => void;

    type ErrorCallback = (error: any) => void;

    type ValueCallback<T> = (value: T) => void;

    type GetKeyCallback<T> = (value: T) => string;

    type TestCallback<T> = (value: T) => boolean;

    type MapCallback<T, U> = (value: T) => U;

    type MapStringCallback = MapCallback<string, string>;

    type NumberCallback<T> = (value: T) => number;

    type MemoCallback<T, U> = (memo: U, value: T) => U;

    type GeneratorCallback<T> = (index: number) => T;

    type CompareCallback<T> = (x: T, y: T) => number;

    type FulfilledCallback<T, U> = (value: T) => U;

    type RejectedCallback<T> = (reason: any) => T;

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    interface Iterator<T> {
        new (sequence: Sequence<T>): Iterator<T>;
        current(): T;
        moveNext(): boolean;
    }

    interface GeneratedSequence<T> extends Sequence<T> {
        new(generatorFn: GeneratorCallback<T>, length: number): GeneratedSequence<T>;
        length(): number;
    }

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    module Sequence {
        function define(methodName: string[], overrides: Object): Function;
    }

    interface Sequence<T> extends SequenceBase<T> {
        each(eachFn: ValueCallback<T>): boolean;

        first(): T;
        first(count: number): Sequence<T>;
        indexOf(value: any, startIndex?: number): number;

        last(): T;
        last(count: number): Sequence<T>;
        lastIndexOf(value: any): number;

        reverse(): Sequence<T>;
    }

    interface SequenceBase<T> {
        // TODO improve define() (needs ugly overload)
        async(interval?: number): AsyncSequence<T>;
        chunk(size: number): Sequence<T[]>;
        compact(): Sequence<T>;
        concat(...sequence: Array<T|T[]|Sequence<T>>): Sequence<T>;
        consecutive(length: number): Sequence<T[]>;
        contains(value: T): boolean;
        countBy(keyFn: GetKeyCallback<T>): ObjectLikeSequence<number>;
        countBy(propertyName: string): ObjectLikeSequence<number>;
        dropWhile(predicateFn: TestCallback<T>): Sequence<T>;
        every(predicateFn: TestCallback<T>): boolean;
        filter(predicateFn: TestCallback<T>): Sequence<T>;
        find(predicateFn: TestCallback<T>): T;
        findWhere(properties: Object): T;

        flatten<U>(): Sequence<U>;
        groupBy(field: keyof T): ObjectLikeSequence<T>;
        groupBy<U>(keyFn: GetKeyCallback<T>, valFn?: (val: T) => U | string): ObjectLikeSequence<T>;
        initial(count?: number): Sequence<T>;
        intersection(...sequence: Array<T|T[]|Sequence<T>>): Sequence<T>;
        invoke<U>(methodName: string): Sequence<U>;
        isEmpty(): boolean;
        join(delimiter?: string): string;
        map<U>(mapFn: MapCallback<T, U>): Sequence<U>;

        max(valueFn?: NumberCallback<T>): T;
        min(valueFn?: NumberCallback<T>): T;
        none(valueFn?: TestCallback<T>): boolean;
        pluck<U>(propertyName: string): Sequence<U>;
        reduce(aggregatorFn: MemoCallback<T, T>): T;
        reduce<U>(aggregatorFn: MemoCallback<T, U>, memo: U): U;
        reduceRight(aggregatorFn: MemoCallback<T, T>): T;
        reduceRight<U>(aggregatorFn: MemoCallback<T, U>, memo: U): U;
        reject(predicateFn: TestCallback<T>): Sequence<T>;
        rest(count?: number): Sequence<T>;
        shuffle(): Sequence<T>;
        some(predicateFn?: TestCallback<T>): boolean;
        sort(sortFn?: CompareCallback<T>, descending?: boolean): Sequence<T>;
        sortBy(sortFn: string, descending?: boolean): Sequence<T>;
        sortBy(sortFn: NumberCallback<T>, descending?: boolean): Sequence<T>;
        sortedIndex(value: T): number;
        size(): number;
        sum(valueFn?: NumberCallback<T>): number;
        takeWhile(predicateFn: TestCallback<T>): Sequence<T>;
        union(...sequence: Array<T|T[]|Sequence<T>>): Sequence<T>;
        uniq(key?: keyof T): Sequence<T>;
        uniq(uniqFn: NumberCallback<T>): Sequence<T>;
        where(properties: Object): Sequence<T>;
        without(...sequence: Array<T|T[]|Sequence<T>>): Sequence<T>;
        zip<U>(...sequence: Array<U|U[]|Sequence<U>>): Sequence<[T, U]>;

        toArray(): T[];
        toObject(): Object;
    }

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    module ArrayLikeSequence {
        function define(methodName: string[], overrides: Object): Function;
    }

    interface ArrayLikeSequence<T> extends Sequence<T> {
        // define()X;
        concat(...sequence: ArrayLikeSequence<T>[]): ArrayLikeSequence<T>;
        concat(...sequence: Array<T|T[]|Sequence<T>>): Sequence<T>;
        first(): T;
        first(count?: number): ArrayLikeSequence<T>;
        get(index: number): T;
        length(): number;
        map<U>(mapFn: MapCallback<T, U>): ArrayLikeSequence<U>;
        pop(): ArrayLikeSequence<T>;
        rest(count?: number): ArrayLikeSequence<T>;
        reverse(): ArrayLikeSequence<T>;
        shift(): ArrayLikeSequence<T>;
        slice(begin: number, end?: number): ArrayLikeSequence<T>;
    }

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    module ObjectLikeSequence {
        function define(methodName: string[], overrides: Object): Function;
    }

    interface ObjectLikeSequence<T> extends Sequence<T> {
        assign(other: Object): ObjectLikeSequence<T>;
        // throws error
        //async(): X;
        defaults(defaults: Object): ObjectLikeSequence<T>;
        functions(): Sequence<T>;
        get(property: string): ObjectLikeSequence<T>;
        invert(): ObjectLikeSequence<T>;
        keys(): Sequence<string>;
        omit(properties: string[]): ObjectLikeSequence<T>;
        /** pairs is a sequence of arrays: [string, Array<T>] */
        pairs(): Sequence<[string, Array<T>]>;
        pick(properties: string[]): ObjectLikeSequence<T>;
        toArray(): T[];
        toObject(): Object;
        /** A sequence of arrays of the values for each key */
        values(): Sequence<T>;
    }

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    module StringLikeSequence {
        function define(methodName: string[], overrides: Object): Function;
    }

    interface StringLikeSequence extends SequenceBase<string> {
        charAt(index: number): string;
        charCodeAt(index: number): number;
        contains(value: string): boolean;
        endsWith(suffix: string): boolean;

        first(): string;
        first(count: number): StringLikeSequence;

        indexOf(substring: string, startIndex?: number): number;

        last(): string;
        last(count: number): StringLikeSequence;

        lastIndexOf(substring: string, startIndex?: number): number;
        mapString(mapFn: MapStringCallback): StringLikeSequence;
        match(pattern: RegExp): StringLikeSequence;
        reverse(): StringLikeSequence;

        split(delimiter: string): StringLikeSequence;
        split(delimiter: RegExp): StringLikeSequence;

        startsWith(prefix: string): boolean;
        substring(start: number, stop?: number): StringLikeSequence;
        toLowerCase(): StringLikeSequence;
        toUpperCase(): StringLikeSequence;
    }

    // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    interface AsyncSequence<T> {
        each(callback: ValueCallback<T>): AsyncHandle<boolean>;

        first(): AsyncHandle<T>;
        first(count: number): AsyncSequence<T>;
        indexOf(value: any, startIndex?: number): AsyncHandle<number>;

        last(): AsyncHandle<T>;
        last(count: number): AsyncSequence<T>;
        lastIndexOf(value: any): AsyncHandle<number>;

        reverse(): AsyncSequence<T>;

        chunk(size: number): AsyncSequence<T[]>;
        compact(): AsyncSequence<T>;
        concat(...sequence: Array<T|T[]|Sequence<T>|AsyncSequence<T>>): AsyncSequence<T>;
        consecutive(length: number): AsyncSequence<T[]>;
        contains(value: T): AsyncHandle<boolean>;
        countBy(keyFn: GetKeyCallback<T>): AsyncSequence<number>;
        countBy(propertyName: string): AsyncSequence<number>;
        dropWhile(predicateFn: TestCallback<T>): AsyncSequence<T>;
        every(predicateFn: TestCallback<T>): AsyncHandle<boolean>;
        filter(predicateFn: TestCallback<T>): AsyncSequence<T>;
        find(predicateFn: TestCallback<T>): AsyncHandle<T>;
        findWhere(properties: Object): AsyncHandle<T>;

        flatten<U>(): AsyncSequence<U>;
        groupBy(field: string): AsyncSequence<T>;
        groupBy(keyFn: GetKeyCallback<T>): AsyncSequence<T>;
        initial(count?: number): AsyncSequence<T>;
        intersection(...sequence: Array<T|T[]|Sequence<T>|AsyncSequence<T>>): AsyncSequence<T>;
        invoke<U>(methodName: string): AsyncSequence<U>;
        isEmpty(): AsyncHandle<boolean>;
        join(delimiter?: string): AsyncHandle<string>;
        map<U>(mapFn: MapCallback<T, U>): AsyncSequence<U>;

        max(valueFn?: NumberCallback<T>): AsyncHandle<T>;
        min(valueFn?: NumberCallback<T>): AsyncHandle<T>;
        none(valueFn?: TestCallback<T>): AsyncHandle<boolean>;
        pluck<U>(propertyName: string): AsyncSequence<U>;
        reduce(aggregatorFn: MemoCallback<T, T>): AsyncHandle<T>;
        reduce<U>(aggregatorFn: MemoCallback<T, U>, memo: U): AsyncHandle<U>;
        reduceRight(aggregatorFn: MemoCallback<T, T>): AsyncHandle<T>;
        reduceRight<U>(aggregatorFn: MemoCallback<T, U>, memo: U): AsyncHandle<U>;
        reject(predicateFn: TestCallback<T>): AsyncSequence<T>;
        rest(count?: number): AsyncSequence<T>;
        shuffle(): AsyncSequence<T>;
        some(predicateFn?: TestCallback<T>): AsyncHandle<boolean>;
        sort(sortFn?: CompareCallback<T>, descending?: boolean): AsyncSequence<T>;
        sortBy(sortFn: string, descending?: boolean): AsyncSequence<T>;
        sortBy(sortFn: NumberCallback<T>, descending?: boolean): AsyncSequence<T>;
        sortedIndex(value: T): AsyncHandle<number>;
        size(): AsyncHandle<number>;
        sum(valueFn?: NumberCallback<T>): AsyncHandle<number>;
        takeWhile(predicateFn: TestCallback<T>): AsyncSequence<T>;
        union(...sequence: Array<T|T[]|Sequence<T>>): AsyncSequence<T>;
        uniq(): AsyncSequence<T>;
        where(properties: Object): AsyncSequence<T>;
        without(...sequence: Array<T|T[]|Sequence<T>|AsyncSequence<T>>): AsyncSequence<T>;
        zip<U>(...sequence: Array<U|U[]|Sequence<U>|AsyncSequence<U>>): AsyncSequence<[T, U]>;

        toArray(): AsyncHandle<T[]>;
        toObject(): AsyncHandle<Object>;
    }

    interface AsyncHandle<T> {
        then<U>(onFulfilled?: FulfilledCallback<T, U>, onRejected?: RejectedCallback<U>): AsyncHandle<U>;
        cancel(): void;
        onComplete(callback: Callback): AsyncHandle<T>;
        onError(callback: ErrorCallback): AsyncHandle<T>;
    }
}

declare var Lazy: LazyJS.LazyStatic;

declare module 'lazy' {
    export = Lazy;
}
