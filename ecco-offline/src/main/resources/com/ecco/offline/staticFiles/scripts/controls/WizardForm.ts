import $ = require("jquery");
import ElementContainer = require("../controls/ElementContainer");
import View = require("../controls/View");
import WizardView = require("./WizardView");
import WizardState = require("./WizardState");


/**
 * Wraps a WizardView such that this form can replace the WizardView to provide wizard functionality
 * See WizardTestForm.ts
 */
class WizardForm implements View {

    /** this component is responsible for the page we are up to */
    private currentPage = 0;
    private maxReachedPage = 0;
    /** have a container that we are in control of - so we can switch its contents */
    private pageContainer = new ElementContainer();
    private statusContainer = new ElementContainer();
    private wrapperContainer = new ElementContainer()
            .append(this.statusContainer).append(this.pageContainer);
    private $status = $("<span>");

    constructor(
            private totalPages: number,
            private wizardView: WizardView) {
        const initialPage = wizardView.getWizardInitialPage();
        if ((typeof initialPage !== 'undefined') && (initialPage != null)) {
            this.currentPage = initialPage;
        } else {
            // see GenericTypeReviewDto.lastIncompleteReview
            // if we create a Review dated before an already complete one, then we get nothing back
            const msg = "invalid request - 'currentPage' is null";
            alert(msg);
            throw new Error(msg);
        }
        wizardView.setWizardTriggerPage(() => this.advancePage());
        wizardView.setWizardPreviousPage(() => this.previousPage());
        wizardView.setWizardCancelPage(() => this.cancelPage());
        wizardView.setWizardRerenderPage(() => this.updateContainers());
        wizardView.setGetWizardState(() => this.getWizardState());
    }

    public loadWizardFirstPage() {
        this.updateContainers();
    }

    public advancePage() {
        this.currentPage++;
        if (this.currentPage > this.maxReachedPage) {
            this.maxReachedPage = this.currentPage;
        }
        if (this.currentPage < this.totalPages) {
            this.updateContainers();
        } else {
            this.wizardView.wizardCompleted();
        }
    }

    private getWizardState(): WizardState {
        return {
            currentPage: this.currentPage,
            maxPageReached: this.maxReachedPage
        };
    }

    public previousPage() {
        this.currentPage--;
        if (this.currentPage > -1) {
            this.updateContainers();
        }
    }

    public cancelPage() {
        this.wizardView.renderWithCancelConfirmation();
    }

    /* never used, but might be soon...
    public jumpTo(pageNumber: number) {
        let pageIndex = pageNumber - 1;
        this.currentPage =
                pageIndex < 0 ? 0 :
                pageIndex >= this.totalPages ? this.totalPages-1 : pageIndex;
        this.updateContainers();
    }
    */

    title() {
        return this.wizardView.title();
    }

    element() {
        //return this.wrapperContainer.element();
        return this.wizardView.element();
    }

    getFooter() {
        return this.wizardView.getFooter();
    }

    private updateContainers() {
        this.wizardView.renderWithPage(this.currentPage);
        //this.pageContainer.setContent(this.wizardView.renderWithPage(this.currentPage));
        //this.statusContainer.setContent(this.status());
        const percent = this.percentComplete().toFixed() + "%";
        this.$status.text(percent);
    }

    public status() {
        return this.$status;
    }

    private percentComplete(): number {
        let complete = 100;
        if (this.totalPages > 0) {
            complete = 0;
            if (this.currentPage > 0)
                complete = ((this.currentPage) * 100) / this.totalPages;
        }
        if (complete > 100)
            complete = 100;
        return complete;
    }

}
export = WizardForm;
