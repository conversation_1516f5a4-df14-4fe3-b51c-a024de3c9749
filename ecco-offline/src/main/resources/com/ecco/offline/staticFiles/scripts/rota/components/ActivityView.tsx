import {EccoDate, ReloadEvent, Result, WebApiError} from "@eccosolutions/ecco-common";
import {<PERSON><PERSON>, Card, CardActions, CardContent, CardHeader, Grid} from "@eccosolutions/ecco-mui";
import {
    EditAppointmentCommandForm,
    EditScheduleModalWrapper,
    useAdditionalStaff,
    useDemandSchedule,
    useServiceRecipient,
    useServicesContext,
    withCommandForm
} from "ecco-components";
import {button,
    dropdownList,
    EccoV3Modal,
    link,
    possiblyModalForm,
} from "ecco-components-core";
import {CalendarDayNames, EventResourceDto, ListDefinitionEntry} from "ecco-dto";
import {Activity, AllocationOptions} from "ecco-rota";
import * as React from "react";
import {FC, useEffect, useState} from "react";
import {showInCommandForm} from "../../components/CommandForm";
import UnassignedActivitiesRowControl from "./UnassignedActivitiesRowControl";
import {getAllocationParams} from './allocation';
import {
    AppointmentRecurringActionCommand,
    AppointmentRecurringActionCommandDto,
    commandSentResultBus
} from "ecco-commands";
import ServiceRecipientView from "./ServiceRecipientView";
import {AuditHistory} from "../../service-recipients/components/AuditHistory";
import {DemandResource} from "ecco-rota";
import {WorkerJobAssignmentGridItems} from "./workers/WorkerJobAssignmentGridItems";
import {CareOrEventCard} from "../../care/CareOrEventCard";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import {AdditionalStaffText} from "ecco-components";
import {lazyControlWrapperNoPadding} from "../../components/ControlWrapper";

// see also SchedulerView.tsx
const VisitLink: FC<{visitEventId: string}> = props => {
    const [showVisit, setShowVisit] = useState(false);
    const [visitEvent, setVisitEvent] = useState<EventResourceDto | null>(null);

    const {calendarRepository} = useServicesContext();

    useEffect(() => {
        showVisit && (!visitEvent || visitEvent.uid != props.visitEventId)
            && calendarRepository.fetchEventsById([props.visitEventId])
                .then(events => setVisitEvent(events[0]));
    }, [showVisit, props.visitEventId]);

    const VisitModal = <EccoV3Modal
            title={""}
            show={showVisit}
            onCancel={() => setShowVisit(false)}
            action="close"
        >
            {visitEvent &&
                <ServicesContextProvider>
                    <CareOrEventCard key={visitEvent.uid} event={visitEvent}/>
                </ServicesContextProvider>
            }
        </EccoV3Modal>
    return <>{link(`visit`, () => setShowVisit(true))}{VisitModal}</>
}

export const RiskStatusArea: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {serviceRecipient} = useServiceRecipient(props.srId);
    if (!serviceRecipient) {
        return <></>;
    }

    const Component = lazyControlWrapperNoPadding(() => import("../../evidence/risk/RiskStatusAreaControl").then(i => ({default: i.RiskStatusAreaControl})), props.srId);
    return <Component/>;
};

// FIXME: This class has gained functionality that should be extracted into maintainable components - extract them

export const ActivityView: FC<{activity: Activity, showRecurring: boolean,
                    recurringAllocate: boolean, setRecurringAllocate: (boolean) => void, weekView: boolean,
                    showExpandActionCard: boolean, setShowExpandActionCard: (boolean) => void}> = props => {
    const {activity, showRecurring, recurringAllocate, setRecurringAllocate, weekView, showExpandActionCard, setShowExpandActionCard} = props;
// TODO: Summary of stuff + link to open client file
    const [showAgreements, setShowAgreements] = useState(false);
    const [showDrop, setShowDrop] = useState(false);
    const [reason, setReason] = useState<number | undefined>(undefined);
    const [rateCardEntries, setRateCardEntries] = useState<ListDefinitionEntry[]>([]);
    const [showAudit, setShowAudit] = useState<string | null>(null);
    const [applyingAudit, setApplyingAudit] = useState(false);
    const [showSchedule, setShowSchedule] = useState(false);

    const {sessionData, rotaRepository, referralRepository} = useServicesContext();

    const {schedule} = useDemandSchedule(activity.getAgreementId(), activity.getScheduleId());
    const {serviceRecipient} = useServiceRecipient(activity.getServiceRecipientId());

    // LOAD the appointments on the additionalStaff schedules at the date/time of this activity
    // NB for offline this solution needs moving server-side
    const {additionalStaff} = useAdditionalStaff(activity, activity.getRef())

    // useEffect(() => {
    //     ResizeEvent.bus.fire();
    // },
    //     [activity, showExpandActionCard]);

    /**
     * SHOW BREAKDOWN
     *
     *  GIVEN
     *  - 'this' care run 'activity' is an appointment RotaAppointmentViewModel (from appointment schedule 'patterns-availability') which is the demand
     *  - it knows nothing of being a run in itself, it thinks its a client
     *  - to know we can do something with it needs some property: hateos / nonRotaEvent ...
     *  THEN
     *  - we can then find the care run as a resource, so that we can find the appointments in it
     *  - which amounts to the BuildingCareRunRotaHandler which shows demand as availability, and client appointments as that
     *  - so we need to get to L113 this.rotaHandlerSupport.addConfirmedRecurrences(interval, rotaEntry, calendarId, AppointmentSchedule.class, careRun.getServiceRecipient().getId());
     *  - this populates rotaEntry, which is the RotaResourceViewModel of the rota
     *  - therefore we should get a reduced rota for this demand and allow the usual 'unallocate' to fall back into the standard flow
     *  - for now we get the whole rota, hard coded link
     *  - and do a quick operation to deallocate - since things are based on events
     *
     *  IDEAL: not have the reload after an operation, but then need to hook into RotaControl.assignHandlerToActivity etc.
     *  IDEAL: treat the shifts as drop-targets when still allocated so can accept
     */
    const getUnderlyingAppointments = () => {
        // use the activity.getDto.links to determine building id - since we don't have on the 'whole organisation'
        const careRun = activity.isCareRun()
        if (!careRun) {
            return
        }
        // determine the rota from the care run
        const careRunSrId = activity.getServiceRecipientId()
        const activityEnd = activity.getEnd()!; // Always non-null for carerun

        // NB the demandFilter (buildings:-) is there to choose the BuildingCareRunRotaHandler - but the demand is not loaded
        // NB the resource is already known - no need to all resources or demands
        // NB the careruns:${careRunSrId} is the only example of resourceFilter breakdown
        //      otherwise its always :all
        rotaRepository.findRotaByDate(activity.getStart().toEccoDate(), activityEnd.toEccoDate(),
                `careruns:${careRunSrId}`, `buildings:-`, true, false)
            .then(r => {
            // APTS ON THE SHIFT ALLOCATED IN THE TIME
            const newApts = r.getAllActivities()
                .filter(
                    // check the time is included, since the rota loads the whole day - so we can include apts outside the shift
                    // allocate (or just show) to the workerJob all the rota appointments
                    it => {
                        const candidateEnd = it.getEnd();
                        return it.isAllocated()
                                && it.getStart().laterThanOrEqual(activity.getStart())
                                // Somewhat hacky (needs extracting to method on Activity, and tests) way to
                                // deal with all day events that start within a shift - hell that should not be possible
                                && candidateEnd ? candidateEnd.earlierThanOrEqual(activityEnd)
                                    : activity.getStart().earlierThanOrEqual(activityEnd);
                    }
                );

            // WILL WE BE ON A WORKERJOB ROW?
            const workerJobs = activity.getAllocatedWorkerJobs();

            // YES... SO SWAP OVER
            if (workerJobs && workerJobs.length > 0) {
                const workerJob = workerJobs[0];

                // dealloc me - the shift BUT not-persisting
                // no allocateRecurring here, since its a carerun breakdown and not persistent
                activity.deallocateWorkerJob(workerJob, false);

                newApts.forEach(alloc => {
                    // no allocateRecurring here, since its a carerun breakdown and not persistent
                    // noinspection JSIgnoredPromiseFromCall
                    workerJob.allocateActivity(alloc, false);
                    // TODO instead of reload, perhaps set up RotaControl.assignHandlerToActivity(activity);
                    //alloc.deallocateWorker(alloc.getAllocatedWorkers()[0]);
                })
            }

            // if NOT assigned
            if (!workerJobs || workerJobs.length == 0) {
                const ctl = activity.getDodgyCallback();
                // assign is the process of unassigning
                (ctl as any as UnassignedActivitiesRowControl).onActivityAssigned(activity);
                newApts.forEach(alloc => {
                    (ctl as any as UnassignedActivitiesRowControl).insertActivity(alloc);
                })
            }

        });
    };

    const showDropReasonModal = () => {
        // TODO it would be better to load the rateCardId directly, BUT we also need to search for rateCardName (when that is used)

        // find the rate card entries to choose from
        rotaRepository.findScheduleById(activity.getAgreementId(), activity.getScheduleId()).then(sched =>
            rotaRepository.findRateCardsForAgreement(sched.agreementId).then(rateCards => {
                const rateCardMatch = rateCards.filter(rc => rc.rateCardId === sched.rateCardId).pop(); // HERE
                // find the entries matchingChargeCategoryId's to choose from
                const appointmentOutcomes = sessionData.getListDefinitionEntriesByListName("eventStatusRateId");
                if (rateCardMatch) {
                    const eventStatusRateIds = rateCardMatch.rateCardEntries.map(e => e.matchingChargeCategoryId);
                    const entries = appointmentOutcomes
                        .filter(ld => eventStatusRateIds.indexOf(ld.getId()) > -1);
                    setRateCardEntries(entries);
                }
                else {
                    setRateCardEntries(appointmentOutcomes);
                }
                setReason(undefined);
                setShowDrop(true);
            })
        );
    };

    const srId = activity.getServiceRecipientId();
    const dropActivity = () => {
        activity.drop(srId, reason)
            .then(() => {
                // without a reload, the apt doesn't move
                ReloadEvent.bus.fire();
            })
            .catch((result: WebApiError) => {
                if (result.reason) {
                    alert(result);
                } else {
                    alert("Failed to drop activity due to an unknown error. Code=" + result.statusCode);
                }
            });
    };

    const reinstate = button("reinstate", () => {
                activity.reinstate()
                    .then( () => {
                        // without a reload, the apt doesn't move
                        ReloadEvent.bus.fire();
                    })
                    .catch( (result: WebApiError) => {
                        if (result.reason) {
                            alert(result);
                        } else {
                            alert("Failed to reinstate activity due to an unknown error. Code=" + result.statusCode);
                        }
                    })
            });


    function allocateWorker(workerJob: DemandResource) {
        if (recurringAllocate) {
            return getAllocationParams(activity)
                .then(params => {
                    if (params !== null) {
                        activity.allocateWorkerJob(workerJob, undefined, params)
                            .then(() => {
                                if (weekView) ReloadEvent.bus.fire(); // because we need to update view to see which days got allocated
                            });
                        }
                    }
                );
        }
        return activity.allocateWorkerJob(workerJob);
    }

    function deallocateWorker(workerJob: DemandResource) {
        activity.deallocateWorkerJob(workerJob, true, recurringAllocate)
            .then(() => {
                if (weekView) ReloadEvent.bus.fire(); // because we need to update view to see which days got deallocated
            })
    }

    const assign = <WorkerJobAssignmentGridItems activity={activity}
                                              showRecurring={showRecurring}
                                              recurring={recurringAllocate}
                                              onRecurringChange={setRecurringAllocate}
                                              onAllocate={allocateWorker}
                                              onDeallocate={deallocateWorker}/>;

    const isCareRun = activity.isCareRun()

    const runBreakdown = isCareRun
        && link("run breakdown", () => getUnderlyingAppointments());

    const dropForm = <>
        {dropdownList("reason", (o) => setReason(o.reasonId), {reasonId: reason}, "reasonId",
            rateCardEntries.map(a => { return {id: a.getId(), name: a.getDisplayName(), disabled: false}}),
            undefined, undefined, true)
        }
        </>;

    const more = <Button style={{marginLeft: "20px"}} onClick={() => {
        setShowExpandActionCard(!showExpandActionCard)
    }}>{!showExpandActionCard ? `more` : `less`}</Button>

    const edit = <>{!activity.isDropped() && link(`edit ${isCareRun ? 'run' : 'appointment'}`,
        () => showInCommandForm(withCommandForm(commandForm => {
                const Thing = () => {
                    const eccoAPI = useServicesContext();
                    return possiblyModalForm(
                        "edit appointment",
                        true, true,
                        () => commandForm.cancelForm(),
                        () => commandForm.submitForm(),
                        false, // TODO could emitChangesTo and see if there are any commands
                        false,
                        <EditAppointmentCommandForm
                            serviceId={serviceRecipient && sessionData.getServiceCategorisation(serviceRecipient?.serviceAllocationId).serviceId}
                            services={eccoAPI}
                            setShow={() => {}}
                            readOnly={false}
                            activity={activity}
                            commandForm={commandForm}
                        />
                    );
                };
                return <Thing/>;
            }
        )))}</>;

    /** ADMIN function to help extend recurrences */
    function manageRecurrencesRepeat() {
        const r = {message: "can't repeat this (not recurring or not assigned)"} as Result;

        referralRepository().findOneCommand(showAudit!).then(audit => {
            const orig = audit as AppointmentRecurringActionCommandDto;
            // only allow recurring commands to repeat
            if (orig.commandName == AppointmentRecurringActionCommand.discriminator) {
                // NB assume just one allocation
                if (activity.getAllocatedWorkerJobs().length == 1) {
                    // determine the same part allocations
                    const days = orig.partSchedule && orig.partSchedule.days && orig.partSchedule.days.added || [];
                    const options: AllocationOptions = {
                        untilDate: EccoDate.parseIso8601(schedule!.end),
                        days
                    };
                    activity.getAllocatedWorkerJobs()[0].allocateActivity(activity, undefined, undefined, options, true);
                    return;
                }
            }
            commandSentResultBus.fire(r);
        }).catch(() => {
            commandSentResultBus.fire(r);
        });
    }

    /** ADMIN function to help extend recurrences */
    const manageRecurrences = <>{sessionData.hasRoleSysAdmin() &&
            <>
                <EccoV3Modal
                    title="audit"
                    show={!!showAudit}
                    onCancel={() => {
                        setApplyingAudit(false);
                        setShowAudit(null)
                    }}
                    action="save"
                    saveEnabled={false}
                >
                    <>
                        <AuditHistory uuid={showAudit!} sessionData={sessionData}/>
                        {schedule &&
                        <Grid container direction="row">
                            <Grid item xs={3} sm={2}>&nbsp;</Grid>
                            <Grid item xs={9} sm={10}><small>{applyingAudit ? "applying..." :
                                link("overwrite", () => {
                                    setApplyingAudit(true);
                                    manageRecurrencesRepeat();
                                })
                            } from {activity.getStart().formatIso8601()} to {schedule.end}</small></Grid>
                        </Grid>
                        }
                    </>
                </EccoV3Modal>
                <Grid container direction="row">
                    <Grid item xs={3} sm={2}><small>Apt ref: </small></Grid>
                    <Grid item xs={9} sm={10}><small>{activity.getRef()}</small></Grid>
                </Grid>
                <Grid container direction="row">
                    <Grid item xs={3} sm={2}><small>Updated by: </small></Grid>
                    <Grid item xs={9} sm={10}><small>{activity.getUpdatedBy()}
                        {activity.getUpdatedBy() && link("view", () => {
                            const split = activity.getUpdatedBy().split("/");
                            const uuid = split[split.length - 1];
                            setShowAudit(uuid);
                        })
                        }</small></Grid>
                </Grid>
            </>
    }</>;

    /** ADMIN function to help extend recurrences */
    const manageSchedule = sessionData.hasRoleSysAdmin() && <>
        { showSchedule &&
            <EditScheduleModalWrapper
                serviceRecipientId={activity.getServiceRecipientId()}
                agreementId={activity.getAgreementId()}
                scheduleId={activity.getScheduleId()}
                setShow={show => setShowSchedule(show)}
            />
        }
        {schedule && link(!schedule.parentScheduleId ? "edit" : "edit [child]",
            () => setShowSchedule(true))}
    </>;

    const scheduleLine = `${activity.getStart().formatHoursMinutes()} - ${activity.getEnd()!.formatHoursMinutes()} on ${activity.getStart().formatDatePretty()}${activity.getEvent() ? ` (${activity.getEvent()})` : ""} [s-id ${activity.getScheduleId()}]`;

    return <div>
        <EccoV3Modal
            title={activity.getServiceRecipientName()}
            show={showAgreements}
            onCancel={() => setShowAgreements(false)}
            action="close"
        >
            <ServiceRecipientView srId={srId} srName={activity.getServiceRecipientName()} isCareRun={isCareRun}/>
        </EccoV3Modal>
        <EccoV3Modal
            title="drop from schedule"
            show={showDrop}
            onCancel={() => setShowDrop(false)}
            onSave={() => {
                dropActivity();
                setShowDrop(false);
            }}
            action="save"
            saveEnabled={reason != null}
        >
            {dropForm}
        </EccoV3Modal>
        <Card elevation={0}>
            <CardHeader
                title={<>
                    {isCareRun ? "Run: " : ""}
                    <a onClick={() => setShowAgreements(true)}>
                        {activity.getServiceRecipientName()}
                    </a> {activity.getLocation()}
                </>}
                subheader={
                    <>
                        {scheduleLine}
                        {manageSchedule}
                        {more}
                    </>
                }
            />
            <CardContent style={{paddingBottom: "0px", paddingTop: "0px"}}>
                {showExpandActionCard &&
                <>
                    {manageRecurrences}
                    <Grid container direction="row">
                        <Grid item xs={3} sm={2}>Schedule</Grid>
                        <Grid item xs={9} sm={10}>{!schedule ? "..." :
                                schedule.start == schedule.end ? "Single appointment"
                                        : <><span style={{textTransform: "capitalize"}}>
                                    {schedule.calendarDays.map(d => CalendarDayNames[d]).join(",")}
                                </span> until {EccoDate.iso8601ToFormatShort(schedule.end)}</>
                        }
                        </Grid>
                        <Grid item xs={3} sm={2}>Tasks:</Grid>
                        <Grid item xs={9} sm={10}>{schedule && schedule.parameters && schedule.parameters.tasks}</Grid>
                        <Grid item xs={3} sm={2}>Requires:</Grid>
                        <Grid item xs={9} sm={10}>{activity.getRequiredAttributes().join(", ")}</Grid>
                        <Grid item xs={3} sm={2}>Flags:</Grid>
                        <Grid item xs={9} sm={10}>
                            <RiskStatusArea srId={srId}/>
                        </Grid>
                        {schedule && schedule.parameters && schedule.parameters.appointmentPreferences && <>
                            <Grid item xs={3} sm={2}>Preferences:</Grid>
                            <Grid item xs={9} sm={10}>{schedule.parameters.appointmentPreferences}</Grid>
                        </>}
                        <Grid item xs={12}>
                            <AdditionalStaffText additionalStaff={additionalStaff} />
                        </Grid>
                    </Grid>
                </>
                }
            </CardContent>
            <CardActions>
                <Grid container direction="row" alignItems="center">
                    <Grid item>{edit}</Grid>
                    <Grid item>{!activity.isDropped() && link("drop from schedule", showDropReasonModal)}</Grid>
                    <Grid item><VisitLink visitEventId={activity.getRef()} /></Grid>
                    <Grid item>{runBreakdown}</Grid>
                    <Grid item xs /*give flex-grow: 1 for spacing*//>
                    {activity.isDropped() ? reinstate : assign /* TODO: dropdown for allocate matches and partial matches.  */}
                </Grid>
            </CardActions>
        </Card>
    </div>;
};
