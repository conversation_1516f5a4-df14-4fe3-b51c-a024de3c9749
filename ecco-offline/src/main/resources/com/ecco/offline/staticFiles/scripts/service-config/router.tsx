import * as React from "react"

import {RouteFallbackWithDiagnostics} from "ecco-components";
import {Route, Switch} from "react-router";
import {RouteManagedPage} from "../components/RouteManagedPage";

import {default as SettingAppBar} from "./SettingAppBar";
import * as ReactDom from "react-dom"

function SettingsPage() {
    return (
        <div className="container-fluid top-gap-15">
            <div>list of settings</div>
        </div>
    );
}

ReactDom.render(
    <RouteManagedPage>
        <Route path="/nav/r/settings">
            <SettingAppBar>
                <Switch>
                    <Route path="/nav/r/settings">
                        <SettingsPage />
                    </Route>
                    <RouteFallbackWithDiagnostics/>
                </Switch>
            </SettingAppBar>
        </Route>
    </RouteManagedPage>,
    document.getElementById("maincontrol"));
