import $ = require("jquery")
import dataTableEvent = require("../../controls/dataTableEvent");
    import ReportDataTable = dataTableEvent.ReportDataTable;
import DataTable = require("./../../controls/DataTable");
import dataTableColumn = require("./../../controls/dataTableColumn");
import reportDto = require("ecco-reports");
import {ServiceDto, ProjectDto} from "ecco-dto/service-config-dto";

// temporary 'declare's - to get things going
// also see https://github.com/borisyankov/DefinitelyTyped/blob/master/jquery.dataTables/jquery.dataTables.d.ts
// but upgrading to datatables version 1.10 is completely different - check the d.ts has moved with it
// there is also some consideration in the datatables code itself which is worth investigating
declare var dataTableInit: any;
declare var dataTable: any;

class SmartStepDataTable implements ReportDataTable {

    private table: DataTable;
    private services: ServiceDto[];
    private projects: ProjectDto[];
    private lastChartFilterColumn: number = -1;

    constructor(private dataTableId: string, private dataTablesId: string, private dataTableSearchId: string, private clickRow: (data: any) => void) {
        var $tableElm = $('<table>').attr("id", dataTableId);
        $('#'+dataTablesId).append($tableElm);

        var $quickFilterElm = $('<div>').attr("id", dataTableSearchId).css("display", "none");
        $('#'+dataTablesId).append($quickFilterElm);

        this.table = new DataTable(this.dataTableId, this.dataTableSearchId, null, (data) => this.clickRow(data), this.dataColumns());
    }

    public getDataTableSearchId() {
        return this.dataTableSearchId;
    }

    public render(entries: reportDto.ActionDto[]): void {
        this.table.draw(entries);
    }

    public visibleIds(): number[] {
        // get the filtered rows since we
        // quick trick to get criteria - see http://stackoverflow.com/questions/6478414/jquery-datatables-how-to-get-filtered-visible-rows
        // the underscore notation is allowed from 1.9: http://www.datatables.net/forums/discussion/214/how-to-get-searched-or-filtered-data/p1
        var visibleData: any[] = this.table.getVisibleData();

        // efficient lookup of value: http://stackoverflow.com/questions/7364150/find-object-by-id-in-array-of-javascript-objects
        var filteredByIds: number[] = []; // was {}
        for (var i = 0, len = visibleData.length; i < len; i++) {
            filteredByIds[visibleData[i].actionId] = visibleData[i];
        }
        return filteredByIds;
    }

    /**
     * we need to remember the last filter from the chart, else we end up with compounding filters when
     * clicking on a chart segment
     */
    public undoChartFilterApplied(): void {
        // apply the filter to the specific column
        if (this.lastChartFilterColumn > -1) {
            this.table.clearFilterColumn(this.lastChartFilterColumn);
            this.lastChartFilterColumn = -1;
        }
    }

    public chartFilterApplied(search: string, column: string): void {
        // find the index from the column name that we are using in the graph
        var idx = -1;
        // get the index
        var columns = this.dataColumns();
        for (var i = 0, len = columns.length; i < len; i++) {
            if (columns[i].property == column) {
                idx = i;
            }
        }
        this.lastChartFilterColumn = idx;
        // apply the filter to the specific column
        this.table.filterColumn(search, idx);
    }

    public dataColumns(): dataTableColumn.DataTableColumn[] {

        // create an object of an interface, so we are compiled safely
        // we coud use the incoming data, but that's not helpful if we need to display
        // the table before the data is loaded, or if there is no data returned
        var as: reportDto.ActionDto = {
            actionId: 0,
            actionName: '',
            outcomeId: 0,
            outcomeName: '',
            serviceId: 0,
            serviceName: '',
            projectId: 0,
            projectName: '',
            author: '',
            relevant: 0,
            achieved: 0
        };

        // convert to columns, choosing what we want to show
        var allProperties: string[] = [];
        var columns: dataTableColumn.DataTableColumn[] = [];
        for (var p in as) {
            switch (p) {
                case 'actionId':
                    columns.push({title: "ss-id", property: p, type: dataTableColumn.FilterType.text}); //, display: this.convertClass()});
                    break;
                case 'actionName':
                    columns.push({title: "ss-name", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'outcomeId':
                    columns.push({title: "outcomeId", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'outcomeName':
                    columns.push({title: "outcomeName", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'serviceId':
                    columns.push({title: "serviceId", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'serviceName':
                    columns.push({title: "serviceName", property: p, type: dataTableColumn.FilterType.text}); //, display: this.convertClass()});
                    break;
                case 'projectId':
                    columns.push({title: "projectId", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'projectName':
                    columns.push({title: "projectName", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'author':
                    columns.push({title: "author", property: p, type: dataTableColumn.FilterType.text});
                    break;
                case 'relevant':
                    columns.push({title: "relevant", property: p, type: dataTableColumn.FilterType.num});
                    break;
                case 'achieved':
                    columns.push({title: "achieved", property: p, type: dataTableColumn.FilterType.num});
                    break;
                default:
                    columns.push({title: p, property: p, type: dataTableColumn.FilterType.text});
            }
        }
        return columns;
    }

}
export = SmartStepDataTable
