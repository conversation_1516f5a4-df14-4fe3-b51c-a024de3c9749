import $ = require("jquery");
import ListDefHierarchicalSharedValueAutoSelectList = require("./ListDefHierarchicalSharedValueAutoSelectList");
import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";

var repositoryDefault = new SessionDataAjaxRepository(apiClient);

/**
 * Enhance with select controls with backing data from cfg_list_definitions.
 */
class Enhancer {

    constructor() {
    }

    /** Find the items we support and attach the appropriate component to them */
    public attach() {
        new ListDefHierarchicalSharedValueAutoSelectList($(".listdef-auto-select-list"), repositoryDefault).load();
    }

}


var enhancer = new Enhancer();
enhancer.attach();

