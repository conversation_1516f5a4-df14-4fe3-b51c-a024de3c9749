import {useEffect} from "react";
import {HistoryLinkEvent} from "ecco-dto";
import QuestionnaireHistoryListControl from "./QuestionnaireHistoryListControl";
import SupportHistoryListControl from "../support/SupportHistoryListControl";

export function useHistoryLinkEventIntegration() {
    // run on first render, and not again
    // this is to register the 'history' button
    useEffect(() => {
        const handler = (event: HistoryLinkEvent) => {
            switch (event.type) {
                case "EVIDENCE_QUESTIONNAIRE":
                    QuestionnaireHistoryListControl.showInModal(event.srId, event.taskName);
                    return;
                case "EVIDENCE_SUPPORT":
                    SupportHistoryListControl.showInModal(event.srId, event.taskName);
                    return;
            }
        }

        // we're mounted several times - in 'support' and 'risk' tabs, but we don't want to open several times
        if (!HistoryLinkEvent.bus.hasHandlers()) {
            HistoryLinkEvent.bus.addHandler(handler);
            return () => {
                HistoryLinkEvent.bus.removeHandler(handler);
            }
        }
        return () => {
        }
    }, [])
}