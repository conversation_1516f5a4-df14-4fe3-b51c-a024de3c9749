
import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import commands = require("./commands");
import * as dto from "ecco-dto/reports/charts-dto";
import Form = require("../../controls/Form");
import InputGroup = require("../../controls/InputGroup");
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import TextAreaInput = require("../../controls/TextAreaInput");
import TextInput = require("../../controls/TextInput");
import CheckboxInput = require("../../controls/CheckboxInput");
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ChartAjaxRepository} from "ecco-dto"
import {apiClient} from "ecco-components";

const repository = new ChartAjaxRepository(apiClient);


class ReportDefEditor extends BaseAsyncCommandForm<dto.ChartDefinitionDto> {

    public static showInModal(reportDefUuid: string) {
        var form = new ReportDefEditor(reportDefUuid);
        form.load();
        showFormInModalDom(form);
    }


    private form = new Form();
    private definition = new TextAreaInput("definition", 25);
    private name = new TextInput("name");
    private friendlyName = new TextInput("friendlyName");
    private showOnDashboardManager = new CheckboxInput("show on dashboard manager");
    private showOnDashboardFile = new CheckboxInput("show on dashboard file");
    private orderby = new TextInput("order by");
    private origChartDto: dto.ChartDefinitionDto;


    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private reportDefUuid: string) {
        super("Admin: Edit Chart Definition");
        this.definition.element().addClass("input-sm");
    }

    protected fetchViewData(): Promise<dto.ChartDefinitionDto> {
        if (this.reportDefUuid) {
            return repository.findChartDefByUuid(this.reportDefUuid);
        }
        else {
            return Promise.resolve(null);
        }
    }

    protected override render(chartDto: dto.ChartDefinitionDto) {
        this.origChartDto = chartDto;

        if (chartDto) {
            this.name.setVal(chartDto.name);
            this.friendlyName.setVal(chartDto.friendlyName);
            this.showOnDashboardManager.setChecked(chartDto.showOnDashboardManager);
            this.showOnDashboardFile.setChecked(chartDto.showOnDashboardFile);
            this.orderby.setVal(chartDto.orderby.toString());
            this.definition.setVal(JSON.stringify(chartDto.definition, null, '    '));
        }
        this.form.append( new InputGroup("name", this.name).enableValidation() );
        this.form.append( new InputGroup("friendly name", this.friendlyName) );
        this.form.append( this.showOnDashboardManager );
        this.form.append( this.showOnDashboardFile );
        this.form.append( new InputGroup("order", this.orderby).enableValidation() );
        this.form.append( new InputGroup("definition", this.definition).enableValidation() );

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        try {
            var parsedDef = JSON.parse(this.definition.val());
        } catch (e) {
            alert("Not saving due to parse error: " + e);
            return Promise.reject<void>(e);
        }
        if (this.origChartDto) {
            var origDefJson = JSON.stringify(this.origChartDto.definition, null, '    ');
            cmd = new commands.ReportDefChangeCommand("update", Uuid.parse(this.reportDefUuid))
                .changeName(this.origChartDto.name, this.name.val())
                .changeFriendlyName(this.origChartDto.friendlyName, this.friendlyName.val())
                .changeShowOnDashboardManager(this.origChartDto.showOnDashboardManager, this.showOnDashboardManager.isChecked())
                .changeShowOnDashboardFile(this.origChartDto.showOnDashboardFile, this.showOnDashboardFile.isChecked())
                .changeDefinition(origDefJson, this.definition.val())
                .changeOrderby(this.origChartDto.orderby, parseInt(this.orderby.val()));
        }
        else {
            cmd = new commands.ReportDefChangeCommand("add", Uuid.randomV4())
                .changeName(null, this.name.val())
                .changeFriendlyName(null, this.friendlyName.val())
                .changeShowOnDashboardManager(null, this.showOnDashboardManager.isChecked())
                .changeShowOnDashboardFile(null, this.showOnDashboardFile.isChecked())
                .changeDefinition(null, this.definition.val())
                .changeOrderby(null, parseInt(this.orderby.val()));
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }
}

export = ReportDefEditor;
