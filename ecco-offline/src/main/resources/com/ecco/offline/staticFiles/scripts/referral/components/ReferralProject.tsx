import * as React from "react"
import {CommandQueue, EditReferralDestinationCommand} from "ecco-commands";
import {
    CommandSubform,
    useCurrentServiceRecipientWithEntities,
    withCommandForm
} from "ecco-components";
import {
    createDropdownList,
    possiblyModalForm
} from "ecco-components-core";
import {ServiceRecipientWithEntities, SessionData} from "ecco-dto";
import {ProjectDto as Project} from "ecco-dto/service-config-dto";
import {TaskWithTitle} from "ecco-dto/workflow-dto";
import {Col, Row} from "react-bootstrap";

export const ReferralProjectDialog = (props: {serviceRecipientId: number, task: TaskWithTitle, region?: boolean | undefined, asSelectList?: boolean | undefined}) => {
    const {resolved, reload} = useCurrentServiceRecipientWithEntities()
    return withCommandForm(commandForm =>
            <ReferralProjectEditor
                sr={resolved.serviceRecipient}
                sessionData={resolved.serviceRecipient.features}
                afterSave={reload}
                task={props.task}
                commandForm={commandForm}
                region={props.region}
            />
    );
};

interface EditorProps {
    sr: ServiceRecipientWithEntities;
    sessionData: SessionData;
    task: TaskWithTitle;
    /** Set true to force a dropdown list instead of clickable options */
    asSelectList?: boolean | undefined;
    region?: boolean | undefined;
    afterSave: () => void

}

interface State {
    currProjectId: number;
    currProject: Project
}

export class ReferralProjectEditor extends CommandSubform<EditorProps, State> {

    constructor(props) {
        super(props);
        const svcCat = this.props.sessionData.getServiceCategorisation(this.props.sr.serviceAllocationId);
        this.state = {
            currProjectId: svcCat.projectId,
            currProject: this.findProject(svcCat.projectId)
        };
    }

    findProject(projectId: number | null) {
        return projectId
            ? this.props.sessionData.getProject(projectId)
            : null;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const svcCat = this.props.sessionData.getServiceCategorisation(this.props.sr.serviceAllocationId);
        ReferralProjectEditor.emitCommand(commandQueue, this.props.sr.serviceRecipientId, this.props.task.taskHandle,
                svcCat.projectId, this.state.currProjectId);
    }

    public static emitCommand(commandQueue: CommandQueue,
                              serviceRecipientId: number,
                              taskHandle: string,
                              projectIdFrom: number, projectIdTo: number) {
        const cmd = new EditReferralDestinationCommand(serviceRecipientId, taskHandle)
                .changeProject(projectIdFrom, projectIdTo);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    render() {
        const svcCat = this.props.sessionData.getServiceCategorisation(this.props.sr.serviceAllocationId);
        return possiblyModalForm(
            this.props.task.title,
            true, true,
            () => this.props.commandForm.cancelForm(),
            () => this.props.commandForm.submitForm().then(this.props.afterSave),
            this.state.currProjectId == svcCat.projectId,
            false,
            <ReferralProject sessionData={this.props.sessionData}
                             onProjectChange={projectId => this.setState({currProjectId: projectId,
                                                                            currProject: this.findProject(projectId)})}
                             initialProjectId={svcCat.projectId}
                             currProject={this.state.currProject}
                             projects={this.props.sessionData.getServiceCategorisationProjects(svcCat.serviceId, svcCat?.id || true)}
                             asSelectList={this.props.asSelectList}
                             region={this.props.region}
            />
        );
    }
}

interface Props extends React.ClassAttributes<ReferralProject> {
    sessionData: SessionData;
    initialProjectId: number;
    currProject: Project | null;
    projects: Project[];
    onProjectChange: (projectId: number | null) => void;
    /** Set true to force a dropdown list instead of clickable options */
    asSelectList?: boolean | undefined;
    /** Allow region to be selected */
    region?: boolean | undefined;
}

export class ReferralProject extends React.Component<Props, {}> {

    constructor(props: Props) {
        super(props);
        this.state = {}; // TODO: can delete .. it's stateless
    }

    private handleProjectClick = (id: number) => {
        this.props.onProjectChange(id);
    };

    override render() {

        const projectLabel = this.props.sessionData.getDto().messages["project"];

        return this.props.asSelectList
            ? <Row>
                <Col xs={12}>
                    {createDropdownList("project", "project",
                        this.props.currProject == null ? null : this.props.currProject.id,
                        this.handleProjectClick, this.props.projects, {})}
                </Col>
            </Row>

            : <div className='row ecco-rounded'>
               <div className='col-xs-10 col-xs-offset-1 text-center'>
                <p>{`which ${projectLabel} has been requested?`}</p>
                <ul className='list-unstyled double-spaced'>
                    {this.props.projects.map( (project: Project) => {
                        const css = this.props.currProject != null && project.id == this.props.currProject.id ? " curr-state"
                            : project.id == this.props.initialProjectId ? " prev-state"
                                : "";
                        return (
                            <li
                                key={project.id}>
                                <a
                                    className={"btn btn-link" + css}
                                    onClick={() => this.handleProjectClick(project.id)}>
                                    {project.name}
                                </a>
                            </li>
                        );
                    })}
                </ul>
               </div>
            </div>;
    }
}
