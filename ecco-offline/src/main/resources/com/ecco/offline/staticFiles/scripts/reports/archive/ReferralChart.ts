import $ = require("jquery-jqplot-bundle");
import ReferralChartOptions = require("./ReferralChartOptions");
import ReportChart = require("./ReportChart");
import charts = require("../../controls/charts");
import events = require("./events");
import Analysers = require("ecco-reports");
import {ReferralDto as Referral} from "ecco-dto";
import Lazy = require("lazy");

// temporary 'declare's - to get things going
// also see https://github.com/borisyankov/DefinitelyTyped/blob/master/jquery.dataTables/jquery.dataTables.d.ts
// but upgrading to datatables version 1.10 is completely different - check the d.ts has moved with it
declare var dataTableInit: any;
declare var dataTable: any;

/**
 * A report, around referrals.
 */
class ReferralChart implements ReportChart<Referral> {

    private $status: $.JQuery;
    private $options: $.JQuery;
    private $summary: $.JQuery;
    private $chart: $.JQuery;
    private $download: $.JQuery;
    private chartId: string;

    private chartOptions: ReferralChartOptions;
    private filteredValue: string;
    private getData: () => Referral[];
    private getVisibleIds: () => number[];

    constructor($container: $.JQuery) {
        this.$status = $(".statusHolder", $container);
        this.$options = $(".optionsHolder", $container);
        this.$summary = $(".summaryHolder", $container);
        this.$chart = $(".graphHolder", $container);
        this.chartId = this.$chart.attr("id");
        this.$download = $(".downloadLink", $container);
    }

    public setupDataSources(getData: () => any[], getVisibleIds: () => number[]) {
        this.getData = getData;
        this.getVisibleIds = getVisibleIds;
    }

    public setupChartChange(onChartChange: () => void) {
        this.chartOptions = new ReferralChartOptions((source) => onChartChange());
    }

    public getPropertyApplied() {
        return this.getPropertyFromChartOptions();
    }
    public getSearchApplied() {
        return this.filteredValue;
    }

    public renderArea() {
        //var $dataTable: $.JQuery = $("#" + this.dataTableId);
        //$dataTable.empty();

        // display the status
        var $title = $("<div style=\"font-weight: bold;\">").text("referrals");
        var $total = $("<div>").text("referral total: " + this.getData().length);
        this.$status
            .empty()
            .append($title)
            .append($total);

        // display the options
        this.$options
            .empty()
            .append("<span>").text('chart: ');
        this.chartOptions.attachTo(this.$options);

        this.filteredValue = null;
    }

    public render() {
        var entries = this.getData();

        this.$chart.empty();

        // if we haven't chosen a graph, we have nothing to show
        if (!this.isShowing()) {
            return;
        }

        // ensure we can see it
        this.$chart.show();

        var data = this.generateDataStructures(entries);

        // if we have no data, we have nothing to show
        if (data.length == 0) {
            return;
        }



        // draw graph
        var control = new charts.ChartControl();
        this.$chart.append(control.element());
        control.setData(this.generateChartData(), {canClickThrough: true});

        // chart download
        // there are better ways - one good idea was to post the jqplotToImageStr({}) to the server to get the image and filename back
        var $downloadBtn: $.JQuery = $("<a href='#' class='button'>").text("download");
        $downloadBtn.click( () => {
            control.saveImage("chart.png");
        });
        this.$download
            .empty()
            .append($downloadBtn);
    }

    public isShowing() {
        // if we haven't chosen a graph, we have nothing to show
        return this.chartOptions && this.chartOptions.getByCriteria() != "-1";

    }

    private generateChartData(): charts.ChartData {
        var visibleIds = this.getVisibleIds();
        var visibleReferrals = Lazy(this.getData()).filter((r) => visibleIds[r.referralId] != null)
                .map((r) => ({referral: r, sessionData: null}));
        var output = Analysers.countByReferralPropertyReport(visibleReferrals,
                (val) => this.getChartOptionsPropertyOf(val.referral));

        var outputArray = output.toArray();

        var dataSeries = new charts.DataSeries(null, null, outputArray,
                new charts.PieChartDataRepresentation<charts.KeyValuePair>((item) => item));

        dataSeries.addClickEventHandler((event: charts.ClickEvent<charts.KeyValuePair>) => {
            this.filteredValue = event.getDatums()[0].key;
            events.ChartDataSelectionEvent.bus.fire(new events.ChartDataSelectionEvent(this));
        });

        return new charts.ChartData([dataSeries]);
    }

    private generateDataStructures(entries: Referral[]): any[] {
        var visibleIds = this.getVisibleIds();
        var data: any[] = [];

        for (var i = 0; i < entries.length; ++i) {
            var r = entries[i];

            if (visibleIds[r.referralId] != null) {
                // build up the count against the property in question
                var exists = false;
                for (var s = 0; s < data.length; ++s) {
                    var dbl = data[s];
                    if (dbl[0] == this.getChartOptionsPropertyOf(r)) {
                        dbl[1] = dbl[1]+1;
                        data[s] = dbl;
                        exists = true;
                    }
                }
                if (!exists) {
                    var index = data.length;
                    data[index] = [this.getChartOptionsPropertyOf(r), 1];
                }
            }
        }

        return data;
    }

    private getChartOptionsPropertyOf(r: Referral): string {
        if (this.chartOptions.getByCriteria() == 'byService')
            return "r.referredServiceName";
        if (this.chartOptions.getByCriteria() == 'byProject')
            return "r.currentProjectName";
        if (this.chartOptions.getByCriteria() == 'bySource')
            return r.source;
        if (this.chartOptions.getByCriteria() == 'byStatus')
            return r.statusMessageKey;
        if (this.chartOptions.getByCriteria() == 'byWorker')
            return r.supportWorkerDisplayName;
        return "";
    }

    private getPropertyFromChartOptions(): string {
        // data table lookup index by ..
        if (this.chartOptions.getByCriteria() == 'byService')
            return "referredServiceName";
        if (this.chartOptions.getByCriteria() == 'byProject')
            return "currentProjectName";
        if (this.chartOptions.getByCriteria() == 'bySource')
            return "source";
        if (this.chartOptions.getByCriteria() == 'byStatus')
            return "statusMessageKey";
        if (this.chartOptions.getByCriteria() == 'byWorker')
            return "supportWorkerDisplayName";
        return "";
    }

}

export = ReferralChart;
