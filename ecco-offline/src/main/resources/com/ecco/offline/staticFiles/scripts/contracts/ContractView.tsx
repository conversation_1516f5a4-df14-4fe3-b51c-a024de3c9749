import {RateCardsView, Ta<PERSON><PERSON>uilder, useContract, useAppBarOptions, useServicesContext} from "ecco-components";
import * as React from "react";
import {FC} from "react";
import {AuditHistory} from "../service-recipients/components/AuditHistory";
import {Container} from '@eccosolutions/ecco-mui';

type Props = {
    contractId: number
};
export const ContractView: FC<Props> = props => {
    const {contractId} = props;
    const {contract} = useContract(contractId);
    const {sessionData} = useServicesContext();

    useAppBarOptions(`${contract?.name}`, [contract?.contractId]);

    const tabs = new TabsBuilder()
        .addTab("overview", <p>Contract: {contract?.name}</p>,
            undefined, "fa-file-text-o")
        .addTab("rate cards", <RateCardsView contractId={contractId}/>,
            undefined, "fa-gbp")
        .addTab("audit history",
            contract && <AuditHistory serviceRecipientId={contract.serviceRecipientId} sessionData={sessionData}/>,
            undefined, "fa-history"
        )
        .build();
    return <Container maxWidth="lg">{tabs}</Container>
};

export default ContractView;
