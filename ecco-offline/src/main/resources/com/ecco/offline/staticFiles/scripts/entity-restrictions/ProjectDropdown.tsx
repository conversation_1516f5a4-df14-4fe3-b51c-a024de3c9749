import {useServicesContext} from "ecco-components";
import {dropdownList} from "ecco-components-core";

interface Props<T> {
    serviceId: number
    label: string
    setter: (newState: T) => void
    state: T
    propertyKey: Extract<keyof T, string>
}

export function ProjectDropdown<T>({serviceId, label, setter, state, propertyKey}: Props<T>) {
    const {sessionData} = useServicesContext()
    const projects = sessionData.getServiceCategorisationProjects(serviceId, true);
    return projects
            .filter(p => p.id != null) // HACK around null entry
            .length
        ? dropdownList(label, setter, state, propertyKey, projects, {}, !serviceId)
        : null
}