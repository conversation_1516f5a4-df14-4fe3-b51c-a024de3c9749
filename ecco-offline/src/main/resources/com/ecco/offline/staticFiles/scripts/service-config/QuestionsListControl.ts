import $ = require("jquery");

import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import EditQuestionForm = require("./EditQuestionForm");
import Question = dto.Question;
import {apiClient} from "ecco-components";
import * as dto from "ecco-dto/service-config-dto";
import {QuestionGroupAjaxRepository} from "ecco-dto";

var repository = new QuestionGroupAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<Question> {

    constructor(question: Question) {
        super(question, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditQuestionForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        return $("<span>").text(this.entry.name);
    }

    protected getEntryIconClass(): string {
        return "fa fa-question";
    }
}

class QuestionsListControl extends BaseAsyncListControl<Question> {

    constructor() {
        super("add new question", "no questions defined", "fa fa-cogs");
    }

    protected fetchViewData(): Promise<Question[]> {
        return repository.findAllQuestions();
    }
    protected createItemControl(question: Question) {
        return new EntryControl(question);
    }

    protected addNewEntity() {
        EditQuestionForm.showInModal(null);
    };

}
export = QuestionsListControl;
