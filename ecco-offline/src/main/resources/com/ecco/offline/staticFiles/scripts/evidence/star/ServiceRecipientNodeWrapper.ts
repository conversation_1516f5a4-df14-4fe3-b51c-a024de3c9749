
import {ServiceRecipientWithEntities} from "ecco-dto";
import NodeProxy = require("./NodeProxy");
import GraphContext = require("../../evidence/graph/GraphContext");
import {OutcomeStarArmDefData, StarCommandFactory} from "../../evidence/star/domain";
import {DynamicTreeNode} from "../../draw/dynamic-tree";
import OutcomeStarArmNodeWrapper = require("../../evidence/star/OutcomeStarArmNodeWrapper");

// mimic ReferralNode
class ServiceRecipientNodeWrapper implements NodeProxy {

    private node: DynamicTreeNode;
    private outcomeStarArmNodesById = new Map<number, OutcomeStarArmNodeWrapper>();

    constructor(private recipient: ServiceRecipientWithEntities,
                private context: GraphContext,
                private commandFactory: StarCommandFactory) {
        this.node = new DynamicTreeNode(recipient && recipient.displayName.replace(" ","\n") || "(none)");
    }

    /**
     * Add an OutcomeStarArm with its questions
     */
    public addOutcomeStarArm(outcomeStarArmDefData: OutcomeStarArmDefData) {
        var node = this.outcomeStarArmNodesById.get(outcomeStarArmDefData.id);
        if (!node) {
            node = new OutcomeStarArmNodeWrapper(outcomeStarArmDefData, this.context, this.commandFactory);
            this.node.addChild(node.getNode());
            this.outcomeStarArmNodesById.set(outcomeStarArmDefData.id, node);
        }
        return node;
    }

    public updateArmAnswerVisuals(armId: number, answer: string) {
        this.outcomeStarArmNodesById.get(armId).updateArmAnswerVisuals(answer);
    }

    public progressArmAnswerVisuals(armId: number, answer: string) {
        this.outcomeStarArmNodesById.get(armId).progressArmAnswerVisuals(answer);
    }

    public getNode(): DynamicTreeNode {
        return this.node;
    }

}
export = ServiceRecipientNodeWrapper;
