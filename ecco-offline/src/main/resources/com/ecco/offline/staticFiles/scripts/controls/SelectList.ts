import $ = require("jquery");
import StringInputControl = require("./StringInputControl");
import {ValidationErrors, ValidationChecksBuilder, ValidationCheck} from "../common/validation";

interface EntryWithHiddenAndDefault {
    key: string;
    value: string;
    isHidden?: boolean;
    isDefault?: boolean;
}

class SelectList extends StringInputControl {

    private initiallySelectedKey: string;

    /** Result to return from selected() when the empty entry is selected. -1 is historical and will gradually vanish */
    private emptyEntryResult = "-1";

    private triggerChangeOnPopulate = true;

    public constructor(idAndName: string, private addEmptyEntry = true, private emptyEntryText = '-',
            id?: string,
            private readOnly = false,
            private className?: string) {
        super($("<select>"), undefined, idAndName, id);
        if (readOnly) {
            this.setReadOnly();
        }
        if (className) {
            this.element().addClass(className);
        }
        this.clear();
    }

    /** NOTE: Use "" for null entries.  Null will default to returning the text value */
    public withEmptyEntryValue(result: string) {
        this.emptyEntryResult = result;
        this.clear(); // to update blank entry
        return this;
    }

    /** Set what the default entry is for 'nothing', which is by default "-" returning "-1" */
    public withEmptyEntryText(text: string) {
        this.emptyEntryText = text;
        this.clear(); // to update blank entry
        return this;
    }

    public addOptionGroupFromList<L>(groupName: string, items:L[], extractor: (item: L) => EntryWithHiddenAndDefault,
        isSelected?: (item:L) => boolean) {

        var $group =  $("<optgroup>").attr("label", groupName);
        this.addOptions($group, items, extractor, isSelected);
        this.element().append($group);
        return this;
    }

    public clear() {
        this.element().empty();
        if (this.addEmptyEntry) {
            if (this.emptyEntryText == null) {
                // blank allows placeholders on select2
                // see http://stackoverflow.com/a/21414098
                this.element().append($("<option>"));
            } else {
                this.element().append($("<option>")
                    .attr("value", this.emptyEntryResult)
                    .text(this.emptyEntryText));
            }
        }
        return this;
    }

    public override setReadOnly() {
        this.element().attr('disabled', 'disabled');
    }

    public populateFromList<L>(items:L[], extractor: (item: L) => EntryWithHiddenAndDefault, isSelected?: (item:L) => boolean) {
        this.addOptions(this.element(), items, extractor, isSelected);
        return this;
    }


    private addOptions<L>($el: $.JQuery, items:L[], extractor: (item: L) => EntryWithHiddenAndDefault,
            isSelected?: (item:L) => boolean) {
        if (items) {
            var hasMatch = isSelected && items.some(isSelected);
            $.each(items, (i, item: L) => {
                var entry = extractor(item);
                var selectedAsDefault = !hasMatch && entry.isDefault;
                var hiddenNotSelected = entry.isHidden && !(isSelected && isSelected(item));
                if (!hiddenNotSelected) { // do not show if hidden (and not selected the hidden value)
                    $el.append(this.createOptionTag(entry.key, entry.value,
                        selectedAsDefault || (isSelected && isSelected(item))));
                }
            });
        }
        this.triggerChangeOnPopulate && this.element().change(); // need to fire when changed as not done by default
    }

    public noTriggerChangeOnPopulate() {
        this.triggerChangeOnPopulate = false;
        return this;
    }

    private createOptionTag(key: string, value: string, selected?: boolean) {
        var option = $("<option>")
                    .attr("value", key)
                    .text(value);
        if (selected) {
            this.initiallySelectedKey = key;
            option.attr("selected", "selected");
        }
        return option;
    }

    public getInitiallySelectedKey() { return this.initiallySelectedKey; }

    /** get the selected value */
    public selected(defaultValueAsNull: boolean) {
        const val = this.element().val();
        return (defaultValueAsNull && this.emptyEntryResult == val) ? null : val;
    }

    /** get the selected value */
    public selectedValAsNumber() {
        return this.addEmptyEntry && (this.emptyEntryResult == this.element().val()) ? null
            : parseInt(this.element().val());
    }

    public length() {
        return this.element().children('option').length;
    }

    /** Return the text of the selected entry */
    public selectedValText(): string {
        return this.element().find("option:selected").text();
    }

    public override isValid() {
        return this.selectedValAsNumber() != null;
    }

    public override validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors) {
        if (checks.isChecked(ValidationCheck.Required)) {
            errors.requireNotNull(field, this.selectedValAsNumber());
        }
    }

}

export = SelectList;
