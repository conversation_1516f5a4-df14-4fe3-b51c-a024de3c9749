package com.ecco.dom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.Transient;

import com.ecco.calendar.dom.Calendarable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;

import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.entity.ConfigurableIntKeyedEntity;
import com.ecco.security.event.CalendarableCreated;

@MappedSuperclass
public abstract class CalendarableIntKeyedEntity extends ConfigurableIntKeyedEntity implements Calendarable {

    private static final long serialVersionUID = 1L;

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Column(name = "calendarId", length = 40)
    private String calendarId;


    public CalendarableIntKeyedEntity() {
        super();
    }

    protected abstract boolean isCalendarable();

    public CalendarableIntKeyedEntity(Integer id) {
        super(id);
    }

    @Override
    public String getCalendarId() {
        return calendarId;
    }

    @Override
    public void setCalendarId(String calendarId) {
        this.calendarId = calendarId;
    }

    /** Handle the case where the contact is created as a straightforward entity. */
    @PrePersist
    public void prePersist() {
        prePersistCall();
    }

    protected void prePersistCall() {
        fireCalendarableCreated();
    }

    protected void fireCalendarableCreated() {
        if (isCalendarable()) {
            if (calendarId == null) {
                final CalendarableCreated event = new CalendarableCreated(this, this);
                messageBus.publishBeforeTxEnd(event);
            }
        }
    }

}