package com.ecco.security.config;

import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.root.EccoEnvironment;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.security.DefaultKeyGenerator;
import com.ecco.security.KeyGenerator;
import com.ecco.security.ReferenceDataImpl;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.service.LoginListener;
import com.ecco.security.service.UserManagementService;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.users.repository.UserCommandRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.crypto.password.MessageDigestPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.persistence.EntityManager;

@SuppressWarnings("deprecation") // We have to use old SHA-1 password encoder for existing sites
@Configuration(proxyBeanMethods = false)
@EnableScheduling
@EnableJpaRepositories(basePackageClasses={
        IndividualRepository.class, UserCommandRepository.class},
        repositoryBaseClass= QueryDslJpaEnhancedRepositoryImpl.class)
public class SecurityConfig {

    @Bean
    public ReferenceDataImpl applicationReferenceData(SettingsService settingsService,
                                                      ApplicationProperties applicationProperties,
                                                      EccoEnvironment eccoEnvironment,
                                                      SoftwareModuleService softwareModuleService,
                                                      @Value("${misc.analytics.webPropertyId}") String analyticsWebPropertyId) {
        return new ReferenceDataImpl(settingsService, applicationProperties, eccoEnvironment, softwareModuleService, analyticsWebPropertyId);
    }

    /** We use SHA-1 password encoder with external salt here.
     * TODO: We could provide this bean for backwards compatibility (to avoid password resets), but
     * use {@link org.springframework.security.crypto.password.StandardPasswordEncoder} for
     * new sites. */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new MessageDigestPasswordEncoder("SHA-1"); // TODO: Upgrade with DelegatingPasswordEncoder
    }
    // TODO for new and legacy encoderings, we can use the factory based on the {prefix} in the encoded pwd
    //    @Bean
    //    public PasswordEncoder passwordEncoder() {
    //        return PasswordEncoderFactories.createDelegatingPasswordEncoder();
    //    }

    @Bean
    public KeyGenerator userDeviceKeyGenerator() {
        return new DefaultKeyGenerator(128);
    }

    @Bean
    public UserManagementService userManagementService(PasswordEncoder encoder, EntityManager entityManager,
            MessageBus<ApplicationEvent> messageBus, SettingsService settingsService, GroupRepository groupRepository,
                                                       SoftwareModuleService softwareModuleService) {
        return new UserManagementServiceImpl(encoder, entityManager, messageBus, settingsService, groupRepository, softwareModuleService);
    }

    @Bean
    public LoginListener loginListener() {
        return new LoginListener();
    }

}
