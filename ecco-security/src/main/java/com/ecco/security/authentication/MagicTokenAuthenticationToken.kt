package com.ecco.security.authentication

import com.ecco.security.dom.MagicToken
import org.springframework.security.authentication.AbstractAuthenticationToken
import org.springframework.security.core.GrantedAuthority
import org.springframework.util.Assert

class MagicTokenAuthenticationToken private constructor(
    private val principal: String?,
    private var credentials: String?,
    authorities: MutableCollection<out GrantedAuthority>? = null,
    val targetPath: String? = null,
) : AbstractAuthenticationToken(authorities) {
    init {
        if (authorities != null) {
            super.setAuthenticated(true)
        }
    }

    companion object {
        fun unauthenticated(credentials: String) = MagicTokenAuthenticationToken(null, credentials)

        fun authenticated(token: MagicToken, details: Any?): MagicTokenAuthenticationToken = MagicTokenAuthenticationToken(
            token.targetUser.username,
            token.credentials,
            token.targetUser.authorities,
            token.targetPath,
        ).also { it.details = details }
    }

    override fun getPrincipal() = principal

    override fun getCredentials() = credentials

    override fun setAuthenticated(authenticated: <PERSON><PERSON><PERSON>) {
        Assert.isTrue(
            !authenticated,
            "Cannot set this token to trusted: Create a new token by calling MagicTokenAuthenticationToken.authenticated(...) instead",
        )
        super.setAuthenticated(false)
    }

    override fun eraseCredentials() {
        super.eraseCredentials()
        this.credentials = null
    }
}