package com.ecco.security.service;

import java.util.LinkedHashSet;

public class AuditUserSearchResult {

    private final AuditUserSearchScope scope;
    private final LinkedHashSet<UserRevision> revisions;

    public AuditUserSearchResult(AuditUserSearchScope scope, LinkedHashSet<UserRevision> revisions) {
        this.scope = scope;
        this.revisions = revisions;
    }

    public AuditUserSearchScope getScope() {
        return scope;
    }

    public LinkedHashSet<UserRevision> getRevisions() {
        return revisions;
    }
}
