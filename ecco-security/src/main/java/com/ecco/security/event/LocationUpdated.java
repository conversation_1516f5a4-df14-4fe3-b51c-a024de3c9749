package com.ecco.security.event;

import org.springframework.context.ApplicationEvent;

import java.net.URI;

/**
 * Event fired when a location is updated, allowing all iCalendar LOCPARAM properties referencing
 * that location to also be updated.
 * This event is used to keep LOCATION properties in sync with contact addresses, for example.
 */
public class LocationUpdated extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    private final URI locationReference;
    private final String value;

    public LocationUpdated(Object source, URI locationReference, String value) {
        super(source);
        this.locationReference = locationReference;
        this.value = value;
    }

    public URI getLocationReference() {
        return locationReference;
    }

    public String getValue() {
        return value;
    }
}
