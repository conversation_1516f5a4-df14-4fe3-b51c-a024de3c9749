package com.ecco.security.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

import javax.persistence.*;

import org.springframework.security.core.GrantedAuthority;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "sec_groups")
public class Group extends AbstractLongKeyedEntity {
    public static final String CLIENT_GROUP = "client";
    public static final String MANAGER_GROUP = "manager";
    public static final String STAFF_GROUP = "staff";
    public static final String SENIORMANAGER_GROUP = "senior manager";

    @Column(name = "group_name", nullable = false)
    private String name;

    @OneToMany(mappedBy = "group", fetch = FetchType.EAGER)
    private Set<GroupAuthority> authorities;

    @OneToMany(mappedBy = "group")
    private Set<GroupMember> memberships;

    protected Group() {}

    public Group(String name) {
        this.name = name;
    }

    /**
     * Build a Group from a list of {@link GrantedAuthority}
     */
    public Group(String name, List<GrantedAuthority> grantedAuthorities) {
        this.name = name;
        setGrantedAuthorities(grantedAuthorities);
    }

    public String getName() {
        return name;
    }

    protected void setName(String name) {
        this.name = name;
    }

    public Set<GroupAuthority> getAuthorities() {
        return authorities;
    }


    private void setGrantedAuthorities(List<GrantedAuthority> grantedAuthorities) {
        authorities = new HashSet<>();
        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            GroupAuthority groupAuthority = new GroupAuthority(this, grantedAuthority.getAuthority());
            authorities.add(groupAuthority);
        }
    }

    public Set<GroupMember> getMemberships() {
        return memberships;
    }

    public void setMemberships(Set<GroupMember> memberships) {
        this.memberships = memberships;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || !(o instanceof Group)) return false;

        Group group = (Group) o;

        if (getName() != null ? !getName().equals(group.getName()) : group.getName() != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return getName() != null ? getName().hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Group{" +"name='" + name + '\'' + '}';
    }
}
