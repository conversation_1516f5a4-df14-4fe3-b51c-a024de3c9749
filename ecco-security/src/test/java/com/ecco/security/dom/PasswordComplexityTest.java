package com.ecco.security.dom;

import org.junit.Test;

import static com.ecco.security.dom.PasswordComplexity.ONE_DIGIT_ONE_UPPER;
import static com.ecco.security.dom.PasswordComplexity.ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8;
import static org.junit.Assert.*;

public class PasswordComplexityTest {

    @Test
    public void verifiesModeratePassPhrasesCorrectly() {
        assertFalse("Should fail for 13 chars or less", ONE_DIGIT_ONE_UPPER.isSecureEnough("1234567890123"));
        assertTrue("Should pass for 14 chars or more", ONE_DIGIT_ONE_UPPER.isSecureEnough("12345678901234"));

        assertTrue("Should pass with 3 in a row", ONE_DIGIT_ONE_UPPER.isSecureEnough("12345678901222"));
        assertFalse("Should fail with 4 in a row", ONE_DIGIT_ONE_UPPER.isSecureEnough("12345678901111"));
    }

    @Test
    public void verifiesPasswordWithSpecialCharsCorrectly() {
        assertFalse("Should fail for 7 chars or less",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("!1Le567"));
        assertTrue("Should pass for 8 chars with min checks met",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("!1Le5678"));
        assertTrue("Should pass for 8 chars with min checks met",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("123!5Le8"));

        assertFalse("Should fail if missing special",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("12345Le8"));
        assertFalse("Should fail if missing upper",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("123!5le8"));
        assertFalse("Should fail if missing lower",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("123!5LE8"));
        assertFalse("Should fail if missing digit",
                ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8.isSecureEnough("abc!eLeh"));

    }
}