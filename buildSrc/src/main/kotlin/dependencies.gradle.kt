val properties = object {}.javaClass.getResourceAsStream("dependencies.properties").use { input ->
    java.util.Properties().apply { load(input) }
}

/** was 2.5.5. but that doesn't support recent gradle. 2.7.18 is current for that series */
const val springBootVersion = "2.7.18"
const val springCloudVersion = "2021.0.8"
const val tomcatVersion = "9.0.102"
val kotlinVersion = properties["kotlin.version"] as String // Set in buildSrc/build.gradle.kts
const val brotliVersion = "1.7.1"
const val dependencyManagementVersion = "1.1.4"
const val queryDslVersion = "4.4.0"
const val mapstructVersion = "1.6.3"

const val lombokVersion = "1.18.34"
const val synyxMessagesourceVersion = "0.6.1"
//const val hibernateVersion = "5.4.32.Final"
const val jaxbApiVersion = "2.3.3" // versions 3 and 4 also available for jakarta.xml.bind - Spring Boot: javax-jaxb.version (for api)
const val jaxbVersion = "2.3.8" //  Spring Boot: glassfish-jaxb.version (for impl)

