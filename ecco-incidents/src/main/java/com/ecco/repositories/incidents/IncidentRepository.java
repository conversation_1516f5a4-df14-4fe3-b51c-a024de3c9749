package com.ecco.repositories.incidents;

import com.ecco.dom.incidents.Incident;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.Optional;

public interface IncidentRepository extends QuerydslPredicateExecutor<Incident>, CrudRepositoryWithFindOne<Incident, Integer> {

    @Query("SELECT i.serviceRecipient.id from Incident i where id = ?1")
    int getServiceRecipientId(int incidentId);

    Optional<Incident> findIncidentByServiceRecipientId(Integer id);
}
