package com.ecco.dom.incidents;

import java.util.UUID;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;

/**
 * Commands around auxiliary data.
 * NB Currently unused as we wrap it in the servicerecipient creation.
 * Could be re-instated - see InboundIncidentController#createIncidentOnly
 */
@Entity
@Table(name = "inc_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update")
public class IncidentCommand extends BaseIntKeyedCommand {

    @Column(nullable=false)
    private Integer incidentId;

    public IncidentCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                           long userId, @Nonnull String body, Integer incidentId) {
        super(uuid, remoteCreationTime, userId, body);
        this.incidentId = incidentId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected IncidentCommand() {
        super();
    }

    public Integer getIncidentId() {
        return incidentId;
    }
}
