package com.ecco.groupsupport.repositories;

import com.ecco.dom.groupsupport.GroupSupportActivity;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface GroupSupportActivityRepository extends QueryDslPredicateAndProjectionExecutor<GroupSupportActivity, Long> {

    /**
     * Get the next after the supplied date.  See the SO question below.  We may need to add conditions for where
     * they are equal.
     *
     * @return relevant activities where serviceId matches supplied id, or serviceId is null (meaning global)
     *
     * @see http://stackoverflow.com/questions/8748986/get-records-with-highest-smallest-whatever-per-group/8749095#8749095
     */
    @Query( nativeQuery=true,
            value = "SELECT t1.*"
                    + " FROM grp_activitytypes gsat"
                    + " LEFT OUTER JOIN grp_activities t1"
                    + " ON gsat.id = t1.activitytypeid"
                    + " LEFT OUTER JOIN services_projects sa1"
                    + " ON t1.serviceAllocationId = sa1.id"
                    + " LEFT OUTER JOIN grp_activities t2"
                    + " ON t1.activitytypeid = t2.activitytypeid AND t1.fromdate < t2.fromdate"
                    + " WHERE t2.activitytypeid IS NULL"
                    + " AND sa1.serviceId = ?1"
                    + " AND t1.fromdate >= ?2")
    List<GroupSupportActivity> findNextByService_Id(Long serviceId, Date date);

    GroupSupportActivity findOneByUuid(UUID uuid);

    long countByParentId(long parentId);

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    void deleteByUuid(UUID activityUuid);

}
