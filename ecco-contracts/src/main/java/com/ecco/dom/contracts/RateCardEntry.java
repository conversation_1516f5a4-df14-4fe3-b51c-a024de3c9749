package com.ecco.dom.contracts;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.entity.IntKeyedBitemporalEntity;

import lombok.*;
import org.hibernate.annotations.Type;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * The actual charge determined from a combination of factors. Allows calculations to determine:
 *      - Attended @ £50/hr, Late Cancellation @ £30/flat, No-Show @ £50/flat, Cancelled @ £0/flat.
 * In creating schedules, we know the categoryType and some factors and contract. This allows us to find the contracts
 * appropriate RateCardEntry's.
 * When completing a scheduled visit we will know all the details, which allows us to put the exact RateCardEntry on the events table.
 * TODO For PAY, consider re-using RateCards, but entirely different entries recorded on contacts_events.
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "fin_ratecardentries")
public class RateCardEntry extends IntKeyedBitemporalEntity {

    private static final long serialVersionUID = 1L;

    public enum ChargeType { FIXED, TEMPORAL, FIXED_TEMPORAL };

    /**
     * Associated rate
     */
    @ManyToOne
    @JoinColumn(name="rateCardId", nullable=false)
    RateCard rateCard;

    /**
     * Skip this rate card entry altogether in the calculations. Never deleted.
     */
    boolean disabled;

    /**
     * Indicate this is the default entry to use for calculations we don't have all the matching criteria.
     * The default would be the first default found from a list of possible RateCardEntry matches.
     */
    boolean defaultEntry;


    /**
     * MATCHING CRITERIA
     */

    /**
     * The outcome of the event of the visit: attended / late cancellation / did not attend.
     * Recorded on the EventEntry (CustomEventImpl) as eventStatusRateId.
     * This is sometimes referred to as the 'rate'.
     */
    @Nullable
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chargeCategoryListDefId")
    ListDefinitionEntry matchingChargeCategory;

    /**
     * The type of matchingChargeCategory, used to match before the matchingChargeCategory.
     * The appointmenttype (appointments) OR resourcetype (resources)
     *      -> eg one-to-one against workers, or 'bed' for resources
     * Possibly better as 'demand types' but we want to avoid confusion with demandedResourceType
     */
    @Nullable
    @Column
    Integer matchingCategoryTypeId; // eg 'resource' listdef could be 'dayService' / 'bed' OR appointmentType as 'one-to-one'

    /**
     * A list of additional matching criteria based on circumstance: on-call/night/bank-hol/highlyQualified/mileage.
     * It could also include 'timeRange' (based on cronSchedule?) that this entry is applicable for.
     * Recorded on the EventEntry (CustomEventImpl) as eventStatusRateId.
     * This is sometimes referred to as staffBand, or payElement or enhanced charges.
     * Coded using JSON to allow multiples - as per DemandParameters.
     * NB This could be deduced automatically from other data (time of visit, worker details, temporalUnit)
     * but that gets complex. Also, any automation would still need this data to match this RateCardEntry.
     */
    @Nullable
    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    @Builder.Default
    private Map<String, Object> matchingFactors = new HashMap<>();


    /**
     * CHARGES
      */

    /**
     * The type of charge this entry should look at.
     * eg 'fixed' uses 'fixedCharge' / 'temporal' uses temporalUnits / 'temporalWithFixed' could be both / 'custom' is configurable
     * 'custom' means the charge is specified later (otherwise we'd just add another RateCardEntry). When creating the RateCardEntry's
     * it would have to exist as 'custom' with blank details - although any populated ones could be seen as defaults. This first entry
     * would be the one to use as the default, and when it came to populating the custom charges a new RateCardEntry would be created
     * and associated accordingly.
     */
    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "chargeType")
    ChargeType chargeTypeFixedTemporal;

    /**
     * Fixed charge. Duration could still be relevant because a successive rate card may be applicable.
     */
    @Nullable
    BigDecimal fixedCharge;

    /**
     * The amount of time to charge in.
     * eg hours/day/night - [see ECCO-832 ECCO-1462]
     * This is the billing
     */
    @Nullable
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unitMeasurementId")
    UnitOfMeasurement unitMeasurement;

    /**
     * The number of units of the unitMeasurementId - eg 1 unit of measurement 'night shift', or 45 units of '1 minute'
     */
    Integer units;

    /**
     * Charge per <code>units</code> of <code>unitMeasurement</code>
     */
    @Nullable
    BigDecimal unitCharge;

    /**
     * A successive rate card entry to be used after this rate card entry has been applied.
     * This allows for a run of rate cards to be applied to cater for more complex scenarios
     * such as 'minimum time of 15 minutes then 5 minute intervals'. The first rate card would
     * specify a duration of 15 minutes and either a fixedCharge or duration with unitsToRepeatFor as 1.
     * NB Alternatively, we could specify unitChargeMinimum, but that might need its own charge - and
     * on it goes - so this approach makes more data but is more powerful and concise.
     * Similarly, 'mileage' can be a matchingFactor and the units to charge either a fixed amount or a unit charge
     * where unitsToRepeatFor could also cater for the first x miles at one rate, and then a separate charge.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "childRateCardEntryId")
    RateCardEntry childRateCardEntry;

    /**
     * The trigger that determines when the charging of this entry stops and the charges for the child
     * entries start. Useful for temporal charge types, but could be used for fixed charges (depending on the calculation).
     *  - null entry means unlimited on this entry (the default calculation moves to the children for non-whole temporal units)
     *  - value of x would apply this entry x times, then move to the child
     */
    @Nullable
    Integer unitsToRepeatFor;

    // @PreSetEntity was non-standard
    @PrePersist
    @PreUpdate
    void verify() {
        boolean assertFixedCharge = chargeTypeFixedTemporal == ChargeType.FIXED || chargeTypeFixedTemporal == ChargeType.FIXED_TEMPORAL;
        boolean assertTemporal = chargeTypeFixedTemporal == ChargeType.TEMPORAL || chargeTypeFixedTemporal == ChargeType.FIXED_TEMPORAL;

        if (assertFixedCharge) {
            Assert.state(fixedCharge != null, "fixedCharge is null on a fixed-capable charge type");
        }
        if (assertTemporal) {
            Assert.state(unitMeasurement != null, "unitMeasurement is null on a temporal-capable charge type");
            Assert.state(units != null, "units is null on a temporal-capable charge type");
            Assert.state(unitCharge != null, "unitCharge is null on a temporal-capable charge type");
        }
    }

    public void setChargeTypeFixedTemporal(@Nullable String name) {
        this.chargeTypeFixedTemporal = name == null ? null : ChargeType.valueOf(name);
    }
}
