package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.Recurrence;
import com.ecco.calendar.event.DirectoryEntryUpdated;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.calendar.core.CalendarException;
import com.ecco.calendar.core.CalendarServiceBase;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Interval;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.filter.EventStampFilter;
import org.osaf.cosmo.model.filter.NoteItemFilter;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import java.time.Instant;
import java.util.Set;
import java.util.stream.Stream;

import static com.ecco.calendar.cosmo.CosmoHelper.syncAttendeesWithCalendars;

@WriteableTransaction
public abstract class CosmoCalendarBase implements CalendarServiceBase {

    private static final Logger log = LoggerFactory.getLogger(CosmoCalendarBase.class);
    protected EntityUriMapper entityUriMapper;
    protected ContentService contentService;
    protected UserService userService;
    private final MessageBus<ApplicationEvent> messageBus;

    /** For CGLIB only */
    public CosmoCalendarBase() {
        this(null, null, null, null);
    }

    public CosmoCalendarBase(ContentService contentService, UserService userService,
                             EntityUriMapper entityUriMapper, MessageBus<ApplicationEvent> messageBus) {
        this.contentService = contentService;
        this.userService = userService;
        this.entityUriMapper = entityUriMapper;
        this.messageBus = messageBus;
    }

    // for testing
    public void setContentService(ContentService contentService) {
        this.contentService = contentService;
    }
    public void setEntityUriMapper(EntityUriMapper entityUriMapper) {
        this.entityUriMapper = entityUriMapper;
    }

    public static boolean isRecurrenceUid(String uid) {
        return StringUtils.contains(uid, ModificationUid.RECURRENCEID_DELIMITER);
    }

    public static Instant getInstantFromRecurrenceHandler(Recurrence.RecurrenceHandle handle) {
        var mod = new ModificationUid(handle.toString());
        return mod.getRecurrenceId().toInstant();
    }

    @Override
    public boolean isRecurrence(String uid) {
        return CosmoCalendarBase.isRecurrenceUid(uid);
    }

    /**
     * Called currently by:
     *  - EventServiceImpl persistAndSync for non-rota
     *  - CalendarEntryCommandHandlerSupport addRecurringCalendarEntry for non-rota
     *
     *  Does the same as addAttendeesToRecurringEntry and confirmRecurrence
     *  except that here we seem to call 'contentService.addItemToCollection(item, collection)'
     *  which does appear to do some extra processing (see addItemToCollectionInternal)
     *  to check the item name is unique and to remove tombstones where Tombstone.java
     *  "Represents something that was removed from an item".
     *  TODO replace this with similar to addAttendeesToRecurringEntry
     *    but note the differences - we can live without tombstones, but the unique name isn't FK'd
     */
    @Override
    public void addAttendeesToEntry(String calendarId, String uuid) throws CalendarException {

        // as per postEntry
        // StandardProvider.process does getTarget which calls createCollectionTarget
        // START org.osaf.cosmo.atom.provider.StandardTargetResolver.createCollectionTarget
        Item itemTarget = contentService.findItemByUid(calendarId);
        if (itemTarget == null) {
            return;
        }
        if (! (itemTarget instanceof CollectionItem))
        {
            return;
            // END
        }

        CollectionItem collection = (CollectionItem) itemTarget;
        if (log.isDebugEnabled()) {
            log.debug("joining item to collection " + collection.getUid());
        }

        try {
            Item item = contentService.findItemByUid(uuid);
            if (item == null) {
                throw new CalendarException("error joining cosmo event to collection: " + "Item with uuid " + uuid + " not found");
            }
            if (! (item instanceof NoteItem)) {
                throw new CalendarException("error joining cosmo event to collection: " + "Item with uuid " + uuid + " is not a note");
            }

            contentService.addItemToCollection(item, collection);
            syncAttendeesWithCalendars(item, entityUriMapper);
            contentService.updateContent((NoteItem) item);
        } catch (CollectionLockedException e) {
            throw new CalendarException("error joining cosmo event to collection: " + "collection locked");
        }
    }

    protected CollectionItem findCalendar(String uid) {
        Assert.notNull(uid, "Calendar uid cannot be null");
        return (CollectionItem) contentService.findItemByUid(uid);
    }

    @Override
    public String findCalendarIdFromItemId(String itemId) {
        var note = contentService.findItemByUid(itemId);
        // if already a 'calendarId' level, then return
        if (note.getName().equals(CosmoCalendarService.SYSTEM_COLLECTION)) {
            return itemId;
        // else its below SystemCollection (otherwise we'd return above since homecollection -> SystemCollection)
        } else {
            return CosmoCalendarService.getOwnerParent(note).getUid();
        }
    }

    protected Stream<Item> findNoteItems(String calendarId, @Nullable Interval interval) {
        EventStampFilter eventFilter = new EventStampFilter();
        if (interval != null) {
            eventFilter.setTimeRange(interval.getStart().toDate(), interval.getEnd().toDate());
        }
        eventFilter.setExpandRecurringEvents(true);

        NoteItemFilter itemFilter = new NoteItemFilter();
        itemFilter.getStampFilters().add(eventFilter);
        CollectionItem calendar = findCalendar(calendarId);
        Assert.notNull(calendar, "Requested calendarId not found");
        itemFilter.setParent(calendar);
        itemFilter.setFilterProperty(EventStampFilter.PROPERTY_INCLUDE_MASTER_ITEMS, "false"); // Only return the expanded events
        Set<Item> items = contentService.findItems(itemFilter);
//        log.info("calendarId: {}, interval: {} returned {} item(s)", new Object[]{calendarId, interval, items.size()});
        return items.stream();
    }

    protected void fireDirectoryEntryUpdated(User user) {
        DirectoryEntryUpdated event = new DirectoryEntryUpdated(this, entityUriMapper.uriForEntity(user), CosmoHelper.displayName(user), user.getEmail());
        messageBus.publishBeforeTxEnd(event);
    }

    protected void logDebugMsgAndThrow(String msg, Throwable cause) {
        CalendarException e = new CalendarException(msg, cause);
        log.debug(e.getMessage(), e);
        throw e;
    }

}
