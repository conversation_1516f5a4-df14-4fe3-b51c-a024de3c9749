
good detail on options:
	http://www.mail-archive.com/<EMAIL>/msg00039.html

for synching....(a feed is different, see below)
========

http://chandlerproject.org/Projects/ChandlerServerEndUserManual
check out the account browser: You can turn on access to the Home directory by clicking on the 'Settings' link in the upper right-hand corner, select the 'Advanced' tab in the dialog and check 'Show Account Browser link'
check out subscribe to feed links
save to icalendar or view a calendar in hCalendar format


seems ppl write an 'exchange' interface for outlook to fool it and allow all sorts of (industry standard) devices to then connect to their server, of which outlook is just another client.

but we don't want a full blown exchange server? ... like these... or is cosmo/bede like that anyway!
http://www.sogo.nu/about/overview.html uses http://www.openchange.org/ (openchange is a exchange impl)
funambol (have their own? exchange impl?)
zimbra (have their own exchange impl)
bedework uses http://openconnector.org/
if we used an exchange-style program, could outlook connect to 2 exchanges??

the alternative is to get outlook to talk these standards - such as calDAV [calDav is a way to access scheduling info using the format icalendar]
such as http://openconnector.org/ and 
but that relies on the organisation changing things...
[syncML is a way of syncing data - standard to avoid vendor-lockin. also, its focus is on contact and calendar etc.]

whilst I am sure cosmo can provide a feed (it appears not from this link)
http://chandlerproject.org/Projects/GetStarted#Subscribe%20to%20ICAL%20(Google,%20.Mac)
and here it seems possible but tricky: http://markmail.org/message/nwlpxk3xdqktydr5#query:+page:1+mid:nwlpxk3xdqktydr5+state:results
in a nutshell Outlook adheres to a different standard for free/busy...

outlook itself doesn't do great at being icalendar compatible! icalendar is the format of the message.
