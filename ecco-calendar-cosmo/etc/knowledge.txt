

OVERVIEW
========
http://chandlerproject.org/Projects/CosmoDevelopmentHome

run osaf-server/cosmo server - with the minor modifications (not the bundle)
use jonas' ui modifications in a comso-web-integration project to get through to our project
modify med according to the integration servlet solution
determine mysql - the data dump comes with some collections by default for 'clients' user

cosmo-web-integration
	see jonas/restructure/web project for the jsp/js...

	version the js dirs?


scheduling - see cosmo/calendar/query

calendar
	- make failure cause applicationexcption so rolls back tran
	- extract the clickatell sending (http api needs changing with cosmo-integration)

upgrade the clickatell to use httpclient 4 because the cosmo-integration does
- !!!or make separate project (and port and servlet...)!!!!

look at scheduler in cosmo - the applicationContext-scheduler

use of js development (and reloadable)
use of multiple servlets
use of technologies - JSR and webdav
use of importing (and exporting) calendar events (could mimic for contacts - vcards)
use of tickets for sharing - Spring Security implemented - see http://chandlerproject.org/Projects/CosmoTickets
understands webdav requests
uses carddav protocol - already handles vcard
use of interceptor to measure performance of dao's
heavy jsp use of vars (inc jsp and servlet base paths)
handles timeout with java timer - can display to the user...with 'you are about to logout in 5...4...'

========
OVERVIEW
========
========
http://downloads.osafoundation.org/cosmo/releases/1.0.0/index.html
is the web front to a chandler server (but seems to include the server too)


using cosmo - well maintained, standards, and spring etc...!
(see calendar bookmarks for others, and ideas)
went through web.xml...

interesting puts cosmo.server.admin as ServletContext attribute
some filters add 'X-Cosmo-Version' etc to the header - since its a server
uses OpenSessionInViewFilter
Spring Security filters used depending on source - eg webdav, web, cmp(cosmo management protocol), mc(morse code - used for command line status)
some filters are:
webui-security
	defaultHttpSessionContextIntegrationFilter,protocolTicketAuthenticationClearingFilter,webuiFormBasedProcessingFilter,defaultExceptionTranslationFilter,defaultFilterInvocationInterceptor
dav-security
	protocolHttpRequestContextIntegrationFilter,protocolBasicProcessingFilter,protocolTicketProcessingFilter,clientICalendarFilterManagerFilter,davExceptionTranslationFilter,davFilterInvocationInterceptor
webcal-security
	protocolHttpRequestContextIntegrationFilter,protocolBasicProcessingFilter,protocolTicketProcessingFilter,clientICalendarFilterManagerFilter,protocolExceptionTranslationFilter,webcalFilterInvocationInterceptor
pim-security
	pimHttpSessionContextIntegrationFilter,protocolTicketAuthenticationClearingFilter,pimFormBasedProcessingFilter,protocolTicketProcessingFilter,pimAnonymousProcessingFilter,pimExceptionTranslationFilter,pimFilterInvocationInterceptor,protocolExtraTicketProcessingFilter
[
the protocolTicketAuthenticationClearingFilter is a spring AbstractAuthenticationToken (Spring Security)
and represents some temporary? ticket access to an 'Item' of data
]
also uses http://tuckey.org/urlrewrite/ to mimic mod_rewrite in apache
http-logging for bespoke logging of requests

we should consider what security is required for what we need [probably by the address, and hence filters used]
client-hacks-filter isn't required since its only used under /atom/
useful to consider the retry-filter, which looks for exceptions such as deadlocks and tries again
filters may need:
	webcal-security (/webcal), cmp-security (/cmp), pim-security (/pim), default-security (/admin, /account, /login, /welcome, /loginfailed, /logout, /browse, /help)
	webui-security (/security_check)
servlets may need (consider servlets as entry points into the system?)
	cmp, webcal, springDespatcherServlet (others include dav, atom, mc)
cmp servlet - REST implementation - a style of webservice so prob don't need unless internally used
webcal servlet - iCalendar access protocol
spring servlet - used for the /pim which seems to be what we need most


INTERESTING
===========
use of js development (and reloadable)
use of multiple servlets
use of technologies - JSR and webdav
use of importing (and exporting) calendar events (could mimic for contacts - vcards)
use of tickets for sharing - Spring Security implemented - see http://chandlerproject.org/Projects/CosmoTickets
understands webdav requests
uses carddav protocol - already handles vcard
use of interceptor to measure performance of dao's
heavy jsp use of vars (inc jsp and servlet base paths)
handles timeout with java timer - can display to the user...with 'you are about to logout in 5...4...'


=======
INDEPTH
=======
=======

apache licence 2
http://chandlerproject.org/Projects/FAQ
http://chandlerproject.org/Developers/WebHome
http://en.wikipedia.org/wiki/Apache_License
Creative Commons Attribution License 3.0.
Chandler logo image and the contents of all Chandler Project web sites is available under the terms of the Creative Commons Attribution License 3.0.

http://chandlerproject.org/Projects/GetStarted

vision: http://chandlerproject.org/Projects/Vision

http://chandlerproject.org/Projects/ChandlerServerEndUserManual
check out the account browser: You can turn on access to the Home directory by clicking on the 'Settings' link in the upper right-hand corner, select the 'Advanced' tab in the dialog and check 'Show Account Browser link'
check out subscribe to feed links
save to icalendar or view a calendar in hCalendar format

language to region mapper: http://demo.icu-project.org/icu-bin/locexp?d_=en


faq
"Today, Chandler sends and receives email (sent from other Chandler users) and downloads messages via 3 IMAP folders."
http://chandlerproject.org/Projects/GetStarted#Collaborate%20on%20Notes%20and%20Events
email will be in desktop (chandler) but should show subscribing to imap folders
chandler creates folders in an imap client, dragging email to these folders then shows them in the dashboard
    * Chandler Events
    * Chandler Messages
    * Chandler Starred
What's working
    * Send Chandler notes and events out as email. (Unlike "regular" email, you can edit and re-send Chandler emails.) Instructions.
    * Receive Chandler notes and events via email sent by other Chandler users.
    * Add messages from your email client into special Chandler IMAP folders to download them into Chandler where you can manage them as Chandler notes and events. Instructions.
    * Reply and Forward
    * Independent windows for composing messages

What still needs to be done
    * Download Flagged messages into Chandler
    * Download all email into Chandler
    * Attachments
    * Sync IMAP Flags and Read/Unread status
    * Sync IMAP Folders with Chandler (especially Drafts and Sent)
    * Conversation threads
    * Rich text editing for composing emails
    * Rules for filtering messages into collections automatically
    * Junk mail detection
    * Spell-check


read TDD
clover
subvlipse
scaling over 100,000?

chandler = desktop!

freebusy: the ticket allows only access to freebusy information (only relevant for CalDAV calendar collections or regular DAV collections which contain calendar collections somewhere beneath them)

create users using cosmo management protocol - /cmp atom stuff

can the od people view the admin or /home page? and create tickets - we want to block that really

As of March 2009, all development on Chandler is volunteer

if we program to /cmp/user/{username}/service, we will never be out of date in our cosmo calls - see http://chandlerproject.org/Projects/CosmoUserServiceDocument

rest on spring mvc is added in 3 - but isn't that webflow??
 - http://blog.springsource.com/2009/03/08/rest-in-spring-3-mvc/

chandler (old name cosmo?) is a tomcat server, the osaf public version is called chandler hub
default is root, cosmo

http://chandlerproject.org/Projects/ChandlerServerEndUserManual
*  Native Calendar collections, which is the internal format, and allows Cosmo to support multiple protocols for read and read/write access including the Desktop sync protocol - Morse Code, CalDAV, the cosmo feed service (atompub based), and webcal (read-only).
* "webcal", in which an entire calendar is stored on the server in a single resource (file). This is the format popularized by iCal and supported by just about every calendar client, but it is too limited to allow effective read-write subscriptions; usually only the person who published the calendar can update it.
Cosmo has the ability to make a native calendar collection masquerade as a webcal resource, so that clients which only support webcal can still subscribe (read-only) to CalDAV calendars. Cosmo does not allow webcal clients to update subscribed native calendars.

Cosmo supports read-only, read-write and free-busy tickets

http://chandlerproject.org/Developers/WebHome#Client%20Access%20to%20Chandler%20Server
we chose cosmo feed service

a collection can contain anything - lists, events etc
publish things into a collection and then sharing it
try to generate a ticket, click drop down next to collection, invite generates url
enable root user - to see root pages - see tickets?
can extend our cosmo-interface to handle tickets - see http://chandlerproject.org/Projects/CosmoTicketSubscriptions

scalable - http://chandlerproject.org/Projects/FAQ#*Q*%20%20Is%20cosmo%20horizontally%20scala
converting to mysql - http://chandlerproject.org/Documentation/CosmoDatabaseConversion (uses DdlUtils)
					- and http://chandlerproject.org/Documentation/ServerBundleStartupConfiguration
					- and cosmo-migration seems to be a whole project dedicated to it!
acls - not yet! - http://chandlerproject.org/Projects/FAQ#*Q*%20%20Does%20cosmo%20support%20groups%20a
"The only type of access control currently implemented by Cosmo is based on tickets"
current ticket workings - http://chandlerproject.org/Projects/CosmoTicketSubscriptions
tickets can be sent around med users - can subscribe to each others events
in the restful atom way, we should do the same with contacts??
but rest should not be the main architecture of med, its designed as a good way to communicate with others
we may as well implement an acl rather than share contacts (within med) through atom - but sharing vcards is different
also a good way to export/import info

seems that we can 'publish' on the local server and list them - http://chandlerproject.org/Projects/CosmoMicroformats#Preferences

COSMO LIMITS - http://chandlerproject.org/Projects/CosmoFeedServiceSpec#Restrictions


installing cosmo recommendations - http://chandlerproject.org/Documentation/ServerBundleStartupConfiguration

see if we can find a hCalendar viewer which can show the info better

atom description in cosmo! - http://chandlerproject.org/Projects/CosmoFeedServiceSpec

could have multiple servlets/projects? but then need integrated transactions and security
does removing the 'accept' in an atom body to prevent updating?

can import a calendar - see http://chandlerproject.org/Projects/CosmoFeedCollections#Importing%20an%20icalendar%20collectio
 - simply get the file and do a post to chandler, or point chandler to the url of the file!

could have httpclient with local context if needed to basic-auth each user

look at request-injection since can specify a name as 'my collection</span>' which would do something?
could use it to lookup someone else's calendar? - but cosmo could do this already, no security, just guess tickets?
we could do with checking that our incoming MED_COLLECTION_NAME can't be MED_UUID - else it would replace twice, inseucre request?

we could specify the repeatable entries - look at the code to see how it gets for a certain range
and use cosmo to specify all the 'events' in med - getting the events from cosmo
could this help with reminders? - could have 'daily' reminders from 5 days before (eg first reminder, end is start of actual event, repeatable daily...)
how handle concurrency - google data apis have extended atom to handle concurrency

if we were to extend med - we should use atom (like rss but editable) in a rest-ful way (ie use the response headers to indiciate success etc) - no session
