<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- We can use this for our test schema and some data, but it should not be part of live deployment -->
    <changeSet id="ECCO-1990-create-ql-schema" author="danp">

        <createTable tableName="hgmclent">
            <column name="comp_id" type="INTEGER"/>
            <column name="client_no" type="INTEGER"/>
            <column name="surname" type="VARCHAR(30)"/>
            <column name="forename" type="VARCHAR(30)"/>
            <column name="gender" type="VARCHAR(30)"/>
            <column name="dob" type="DATE"/>
            <column name="ni_no" type="VARCHAR(30)"/>
            <column name="pref_lang" type="VARCHAR(30)"/>
            <column name="impair" type="VARCHAR(30)"/>
            <column name="eth_org" type="VARCHAR(30)"/>
            <column name="religion" type="VARCHAR(30)"/>
            <column name="sex_orient" type="VARCHAR(30)"/>
            <column name="tel_no1" type="VARCHAR(30)"/>
            <column name="tel_no2" type="VARCHAR(30)"/>
            <column name="e_mail" type="VARCHAR(30)"/>
        </createTable>

        <createTable tableName="hgmcorad">
            <column name="comp_id" type="INTEGER"/>
            <column name="client_no" type="INTEGER"/>
            <column name="c_add_1" type="VARCHAR(30)"/>
            <column name="c_add_2" type="VARCHAR(30)"/>
            <column name="c_add_3" type="VARCHAR(30)"/>
            <column name="c_add_4" type="VARCHAR(30)"/>
            <column name="c_add_5" type="VARCHAR(30)"/>
            <column name="c_post_code" type="VARCHAR(30)"/>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-1990-dummy-ql-data" author="danp">
        <insert tableName="hgmclent">
            <column name="comp_id" valueNumeric="22222"/>
            <column name="client_no" valueNumeric="11111"/>
            <column name="surname" value="Bryce"/>
            <column name="forename" value="Megan"/>
            <column name="gender" value="FEMALE"/>
            <column name="dob" valueDate="1980-02-24"/>
            <column name="ni_no" value="TG703265U"/>
            <column name="pref_lang" value="ENGLISH"/>
            <column name="impair" value="N"/>
            <column name="eth_org" value="GYPSY"/>
            <column name="religion" value="CHRISTIAN"/>
            <column name="sex_orient" value="HETERO"/>
        </insert>

        <insert tableName="hgmcorad">
            <column name="comp_id" valueNumeric="22222"/>
            <column name="client_no" valueNumeric="11111"/>
            <column name="c_add_1" value="Flat 1"/>
            <column name="c_add_2" value="Test House"/>
            <column name="c_add_3" value="Test Lane"/>
            <column name="c_add_4" value="Testington"/>
            <column name="c_add_5" value="Testingtonshire"/>
            <column name="c_post_code" value="TE1 2ST"/>
        </insert>

        <insert tableName="hgmclent">
            <column name="comp_id" valueNumeric="22223"/>
            <column name="client_no" valueNumeric="11113"/>
            <column name="surname" value="Brown"/>
            <column name="forename" value="Megan"/>
            <column name="gender" value="FEMALE"/>
            <column name="dob" valueDate="1980-02-24"/>
            <column name="ni_no" value="TG703265Z"/>
            <column name="pref_lang" value="ENGLISH"/>
            <column name="impair" value="Multiple Sclerosis"/> <!-- Invalid mapping -->
            <column name="eth_org" value="GYPSY"/>
            <column name="religion" value="CHRISTIAN"/>
            <column name="sex_orient" value="HETERO"/>
        </insert>

        <insert tableName="hgmcorad">
            <column name="comp_id" valueNumeric="22223"/>
            <column name="client_no" valueNumeric="11113"/>
            <column name="c_add_1" value="Flat 2"/>
            <column name="c_add_2" value="Test House"/>
            <column name="c_add_3" value="Test Lane"/>
            <column name="c_add_4" value="Testington"/>
            <column name="c_add_5" value="Testingtonshire"/>
            <column name="c_post_code" value="TE1 2ST"/>
        </insert>
    </changeSet>

</databaseChangeLog>
