package com.ecco.integration.ql;

import static org.springframework.http.HttpStatus.OK;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.util.StringUtils.hasText;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import com.ecco.dto.*;
import com.ecco.integration.core.Utils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import com.ecco.integration.api.TooManyResultsException;

/**
 * Provides an implementation of the default REST API which delegates to QL to return its results.
 */
@RestController
@RequestMapping("/ql")
@Slf4j
public class QLClientQueryAdapter {

    private static final String REF_ALIAS = "Ref";
    private static final String CODE_ALIAS = "Code";

    private static final String SURNAME_ALIAS = "Surname";
    private static final String FORENAME_ALIAS = "Firstname";
    private static final String GENDER_ALIAS = "Gender";
    private static final String DOB_ALIAS = "DOB";
    private static final String NI_ALIAS = "NI";
    private static final String LANGUAGE_ALIAS = "Language";
    private static final String DISABILITY_ALIAS = "Disability";
    private static final String ETHNICITY_ALIAS = "Ethnicity";
    private static final String RELIGION_ALIAS = "Religion";
    private static final String SEXUALITY_ALIAS = "SO";
    private static final String ADDRESS_LINE_1_ALIAS = "AddLine1";
    private static final String ADDRESS_LINE_2_ALIAS = "AddLine2";
    private static final String ADDRESS_LINE_3_ALIAS = "AddLine3";
    private static final String ADDRESS_LINE_4_ALIAS = "AddLine4";
    private static final String ADDRESS_LINE_5_ALIAS = "AddLine5";
    private static final String POST_CODE_ALIAS = "PostCode";
    private static final String PHONE_ALIAS = "Phone";
    private static final String MOBILE_ALIAS = "Mobile";
    private static final String EMAIL_ALIAS = "Email";

    private static final String ALERT1_ALIAS = "Alert1";
    private static final String ALERT2_ALIAS = "Alert2";
    private static final String ALERT3_ALIAS = "Alert3";
    private static final String ALERT4_ALIAS = "Alert4";
    private static final String ALERT5_ALIAS = "Alert5";
    private static final String ALERT6_ALIAS = "Alert6";
    private static final String ALERT7_ALIAS = "Alert7";
    private static final String ALERT8_ALIAS = "Alert8";
    private static final String ALERT9_ALIAS = "Alert9";

    private static final String REF_PARAM = "ref";
    private static final String LASTNAME_PARAM = "surname";
    private static final String FIRSTNAME_PARAM = "forename";
    private static final String GENDER_PARAM = "gender";
    private static final String BIRTHDATE_PARAM = "dob";

    public static final String DEFAULT_BUSINESS_KEY = "DEFAULT";
    public static final String GENDER_DOMAIN = "GENDER";
    public static final String LANGUAGE_DOMAIN = "LANGUAGE";
    public static final String DISABILITY_DOMAIN = "DISABILTY";
    public static final String ETHNICITY_DOMAIN = "ETHNIC_ORIGIN";
    public static final String RELIGION_DOMAIN = "RELIGION";
    public static final String SEXUALITY_DOMAIN = "SEXUAL_ORIENTATION";

    public static final String FLAG_ALERT_DOMAIN = "FLAG_ALERT";
    public static final String FLAG_IMPAIR_DOMAIN = "FLAG_IMPAIR";

    protected static final String ECCO_DOMAIN = "ECCO";

    private static final String CLIENT_FIELDS_CLAUSE = "c.client_no as " + REF_ALIAS + ", " +
            "c.client_no as " + CODE_ALIAS + ", " +
            "c.surname as " + SURNAME_ALIAS + ", " +
            "c.forename as " + FORENAME_ALIAS + ", " +
            "c.gender as " + GENDER_ALIAS + ", " +
            "c.dob as " + DOB_ALIAS + ", " +
            "c.ni_no as " + NI_ALIAS + ", " +
            "c.pref_lang as " + LANGUAGE_ALIAS + ", " +
            "c.impair as " + DISABILITY_ALIAS + ", " +
            "c.eth_org as " + ETHNICITY_ALIAS + ", " +
            "c.religion as " + RELIGION_ALIAS + ", " +
            "c.sex_orient as " + SEXUALITY_ALIAS + ", " +
            "c.tel_no1 as " + PHONE_ALIAS + ", " +
            "c.tel_no2 as " + MOBILE_ALIAS + ", " +
            "c.e_mail as " + EMAIL_ALIAS + ", " +
            "a.c_add_1 as " + ADDRESS_LINE_1_ALIAS + ", " +
            "a.c_add_2 as " + ADDRESS_LINE_2_ALIAS + ", " +
            "a.c_add_3 as " + ADDRESS_LINE_3_ALIAS + ", " +
            "a.c_add_4 as " + ADDRESS_LINE_4_ALIAS + ", " +
            "a.c_add_5 as " + ADDRESS_LINE_5_ALIAS + ", " +
            "a.c_post_code as " + POST_CODE_ALIAS;

    private static final String CLIENT_BASE_QUERY =
            " FROM hgmclent AS c "
                    + "LEFT OUTER JOIN hgmcorad AS a "
                    + "ON a.comp_id = c.comp_id AND a.client_no = c.client_no "
                    + "WHERE ";
    private static final String CLIENT_QUERY_EXTERNALREF_CLAUSE = "lower(c.client_no) = :" + REF_PARAM + " ";
    private static final String CLIENT_QUERY_LASTNAME_CLAUSE = "lower(c.surname) LIKE :" + LASTNAME_PARAM + " ";
    private static final String CLIENT_QUERY_FIRSTNAME_CLAUSE = "and lower(c.forename) LIKE :" + FIRSTNAME_PARAM + " ";
    private static final String CLIENT_QUERY_GENDER_CLAUSE = "AND c.gender = :" + GENDER_PARAM + " ";
    private static final String CLIENT_QUERY_BIRTHDATE_CLAUSE = "AND c.dob = :" + BIRTHDATE_PARAM + " ";

    private static final String FLAGS_FIELDS_CLAUSE = "c.alert1 as " + ALERT1_ALIAS + ", " +
            "c.alert2 as " + ALERT2_ALIAS + ", " +
            "c.alert3 as " + ALERT3_ALIAS + ", " +
            "c.alert4 as " + ALERT4_ALIAS + ", " +
            "c.alert5 as " + ALERT5_ALIAS + ", " +
            "c.alert6 as " + ALERT6_ALIAS + ", " +
            "c.alert7 as " + ALERT7_ALIAS + ", " +
            "c.alert8 as " + ALERT8_ALIAS + ", " +
            "c.alert9 as " + ALERT9_ALIAS;

    private static final String FLAGS_BASE_QUERY =
            " FROM hgmclent AS c WHERE " + CLIENT_QUERY_EXTERNALREF_CLAUSE;

    @Value("${max.results}")
    private int maxResults = 100;

    private final NamedParameterJdbcTemplate jdbcTemplate;
    private final Properties referenceDataMapping;
    private final QLWebServiceClientConnector connector;

    @Autowired
    public QLClientQueryAdapter(
            NamedParameterJdbcTemplate jdbcTemplate,
            @Qualifier("referenceDataMapping") Properties referenceDataMapping,
            QLWebServiceClientConnector connector) {
        this.jdbcTemplate = jdbcTemplate;
        this.referenceDataMapping = referenceDataMapping;
        this.connector = connector;
    }

    @GetJson(value = "/check-log/")
    public String checkLogs() {
        log.trace("A TRACE Message");
        log.debug("A DEBUG Message");
        log.info("An INFO Message");
        log.warn("A WARN Message");
        log.error("An ERROR Message");

        return "Check out the Logs to see the log output...";
    }

    /** For QL - create a "contact" */
    @PostJson(value = "/events/", produces = APPLICATION_JSON_VALUE)
    public String createEventNote(@RequestBody ClientEvent event) {
        if (ClientEvent.NoteType.Complaint.equals(event.getNoteType())) {
            return connector.createComplaint(event);
        }
        return connector.createContact(event);
    }

    /** For QL - handle alerts/impairments */
    @PostJson(value = "/events/flags/", produces = APPLICATION_JSON_VALUE)
    public Boolean createOrUpdateEventFlags(@RequestBody FlagsDefinitionCommandDto cmd) {

        // knowing the limited number of bus keys for alerts helps know the rest
        var allAlertBusKeys = allAlertBusKeys();
        //log.debug("alert business keys - {}", allAlertBusKeys.toString());

        var alerts = new HashSet<>(queryClientAlertsOn(cmd.externalClientRef));
        var impairs = new HashSet<>(connector.getFlagImpairmentsOn(cmd.externalClientRef));
        //log.debug("alerts on in QL - {}", alerts);
        //log.debug("impairments on in QL - {}", impairs);

        // NB use sets, in case we have multiple flags to one ql flag
        Set<String> addAlerts = new HashSet<>();
        Set<String> removeAlerts = new HashSet<>();
        Set<String> addImpairs = new HashSet<>();
        Set<String> removeImpairs = new HashSet<>();

        // NB we ASSUME that the business keys of alerts and impairs are different
        // we also assume that we can't add and remove in the same operation
        if (cmd.flagNames.added != null) {
            addAlerts = cmd.flagNames.added.stream()
                    .filter(f -> allAlertBusKeys.contains(f))
                    .filter(f -> !alerts.contains(f))
                    .collect(Collectors.toSet());
            addImpairs = cmd.flagNames.added.stream()
                    .filter(f -> !allAlertBusKeys.contains(f))
                    .filter(f -> !impairs.contains(f))
                    .collect(Collectors.toSet());
        }
        if (cmd.flagNames.removed != null) {
            removeAlerts = cmd.flagNames.removed.stream()
                    .filter(allAlertBusKeys::contains)
                    .filter(alerts::contains)
                    .collect(Collectors.toSet());
            removeImpairs = cmd.flagNames.removed.stream()
                    .filter(f -> !allAlertBusKeys.contains(f))
                    .filter(impairs::contains)
                    .collect(Collectors.toSet());
        }
        //log.debug("alerts to ADD - {}", addAlerts);
        //log.debug("alerts to REMOVE - {}", removeAlerts);
        //log.debug("impairments to ADD - {}", addImpairs);
        //log.debug("impairments to REMOVE - {}", removeImpairs);

        var alertChanges = FlagAlertDefinition.BuilderFactory.create();
        alertChanges.externalClientRef(cmd.externalClientRef);
        buildAlertChanges(alertChanges, addAlerts, Boolean.TRUE);
        buildAlertChanges(alertChanges, removeAlerts, Boolean.FALSE);
        String alertUniqueId = connector.createOrUpdateFlagAlert(alertChanges.build())
                .getUniqueId();

        for (String impair : addImpairs) {
            String value = getMappedReferenceData(ECCO_DOMAIN + "." + FLAG_IMPAIR_DOMAIN, impair);

            boolean success = connector.createFlagImpairment(cmd.externalClientRef, value);
            // we'll actually fail before we get here, but in case of refactoring we keep it
            if (!success) {
                log.error("FAILED: Adding impair '" + impair + "' on: " + cmd.externalClientRef);
            }
        }
        for (String impair : removeImpairs) {
            String value = getMappedReferenceData(ECCO_DOMAIN + "." + FLAG_IMPAIR_DOMAIN, impair);
            boolean success = connector.deleteFlagImpairment(cmd.externalClientRef, value);
            // we'll actually fail before we get here, but in case of refactoring we keep it
            if (!success) {
                log.error("FAILED: Removing impair '" + impair + "' on: " + cmd.externalClientRef);
            }
        }

        //log.debug("Finished alert/impairments");

        return true;
    }

    private void buildAlertChanges(FlagAlertDefinition.Builder alertChanges, Set<String> alerts, Boolean value) {
        for (String alert : alerts) {
            var v = getMappedReferenceData(ECCO_DOMAIN + "." + FLAG_ALERT_DOMAIN, alert);
            if (v.equals("1")) {
                alertChanges.alert1(value);
            }
            if (v.equals("2")) {
                alertChanges.alert2(value);
            }
            if (v.equals("3")) {
                alertChanges.alert3(value);
            }
            if (v.equals("4")) {
                alertChanges.alert4(value);
            }
            if (v.equals("5")) {
                alertChanges.alert5(value);
            }
            if (v.equals("6")) {
                alertChanges.alert6(value);
            }
            if (v.equals("7")) {
                alertChanges.alert7(value);
            }
            if (v.equals("8")) {
                alertChanges.alert8(value);
            }
            if (v.equals("9")) {
                alertChanges.alert9(value);
            }
        }
    }

    private Set<String> allAlertBusKeys() {
        var alertKeys = new HashSet<String>();
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".1"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".2"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".3"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".4"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".5"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".6"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".7"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".8"));
        alertKeys.add(referenceDataMapping.getProperty(FLAG_ALERT_DOMAIN + ".9"));
        return alertKeys.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * Return the response uniqueId, if needed for debugging
     */
    @PostJson(value = "/client/update", produces = APPLICATION_JSON_VALUE)
    public String updateClient(@RequestBody ClientDefinitionCommandDto cmd) {
        if (!hasText(cmd.externalClientRef)) {
            throw new IllegalArgumentException("externalClientRef is null");
        }
        return connector.updateClient(cmd);
    }

    /**
     * Return the externalClientRef
     */
    @PostJson(value = "/clients/", produces = APPLICATION_JSON_VALUE)
    public ClientDefinition createClient(@RequestBody ClientDefinition clientDef) {
        // a better check would be to check the database, because we could be creating a client with a ref already?
        if (hasText(clientDef.getExternalClientRef())) {
            throw new IllegalArgumentException("externalClientRef is not null");
        }
        var response = connector.createOrUpdateClient(clientDef);
        return ClientDefinition.BuilderFactory.create()
                .externalClientRef(String.valueOf(response.getClientNo()))
                .build();
    }

    @GetJson(value = "/client/{externalClientRef}/flags/")
    public FlagsDefinition queryClientFlags(@PathVariable String externalClientRef) {

        var alerts = queryClientAlertsOn(externalClientRef);
        var impairments = connector.getFlagImpairmentsOn(externalClientRef);
        //log.debug("alerts: {}", alerts);
        //log.debug("impairments: {}", impairments);
        List<String> flags = new ArrayList<>();
        if (alerts != null) {
            flags.addAll(alerts);
        }
        if (impairments != null) {
            flags.addAll(impairments);
        }

        return new FlagsDefinition(new HashSet<>(flags));
    }

    public List<String> queryClientAlertsOn(@PathVariable String externalClientRef) {
        // connector.getClient uses IHSGClientDetails, which is really heavy-weight:
        // so just stick with the query - I doubt there is much extra locking etc on the WebService
        var alertsAll = queryClientAlerts(externalClientRef);
        return Arrays.stream(alertsAll).filter(Objects::nonNull).toList();
    }

    /**
     * We reuse the sql to get the alerts, because they are on an existing table for the hgmclent table.
     * Probably should be using 'GetClientDetails' in the wsdl.
     * @return array of 9 alerts for - null for off, flag name for on
     */
    public String[] queryClientAlerts(@PathVariable String externalClientRef) {

        final Map<String, Object> queryParams = new HashMap<>(1);
        queryParams.put(REF_PARAM, externalClientRef.toLowerCase());

        String[] alerts = new String[9];

        var results = jdbcTemplate.query("select " + FLAGS_FIELDS_CLAUSE + FLAGS_BASE_QUERY, queryParams,
                (rs, rowNum) -> {
                    boolean a = rs.getBoolean(ALERT1_ALIAS);
                    alerts[0] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "1") : null;
                    a = rs.getBoolean(ALERT2_ALIAS);
                    alerts[1] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "2") : null;
                    a = rs.getBoolean(ALERT3_ALIAS);
                    alerts[2] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "3") : null;
                    a = rs.getBoolean(ALERT4_ALIAS);
                    alerts[3] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "4") : null;
                    a = rs.getBoolean(ALERT5_ALIAS);
                    alerts[4] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "5") : null;
                    a = rs.getBoolean(ALERT6_ALIAS);
                    alerts[5] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "6") : null;
                    a = rs.getBoolean(ALERT7_ALIAS);
                    alerts[6] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "7") : null;
                    a = rs.getBoolean(ALERT8_ALIAS);
                    alerts[7] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "8") : null;
                    a = rs.getBoolean(ALERT9_ALIAS);
                    alerts[8] = a ? getMappedReferenceData(FLAG_ALERT_DOMAIN, "9") : null;

                    // we don't really need a return - just to ensure the count is 1
                    return "-";
                });

        // client should exist, whether the flags are empty
        // and we collect data assuming that there is one row - so enforce
        assert results.size() == 1;
        return alerts;
    }

    /**
     * ClientDefinition here comes from ClientController -> ClientDefinitionFromViewModel which gets the business keys
     */
    @PostJson(value = "/clients/query", produces = APPLICATION_JSON_VALUE)
    @ResponseStatus(OK)
    public Collection<ClientDefinition> queryClientsByExample(@RequestBody final ClientDefinition exemplar) {

        final StringBuilder query = new StringBuilder(CLIENT_BASE_QUERY);
        final Map<String, Object> queryParams = new HashMap<>(4);

        if (!hasText(exemplar.getExternalClientRef())) {
            Assert.state(hasText(exemplar.getLastName()), "Either externalClientRef or lastName must be specified");
            query.append(CLIENT_QUERY_LASTNAME_CLAUSE);
            queryParams.put(LASTNAME_PARAM, exemplar.getLastName().toLowerCase() + "%");
        } else {
            query.append(CLIENT_QUERY_EXTERNALREF_CLAUSE);
            queryParams.put(REF_PARAM, exemplar.getExternalClientRef().toLowerCase());
        }

        if (hasText(exemplar.getFirstName())) {
            query.append(CLIENT_QUERY_FIRSTNAME_CLAUSE);
            queryParams.put(FIRSTNAME_PARAM, exemplar.getFirstName().toLowerCase() + "%");
        }
        if (exemplar.getGenderKey() != null) {
            query.append(CLIENT_QUERY_GENDER_CLAUSE);
            // Originally this was Gender.class.getName() - see 55e52eca
            //      referenceDataMapping.getProperty(Gender.class.getName() + '.' + exemplar.getGender().name());
            // It was then moved to "com.ecco.config.dom.ListDefinitionEntry." by myself as a business key refactor.
            // However, it is obtaining the QL value for this business key and should therefore refer to the QL domain.
            String mappedValue = referenceDataMapping.getProperty(ECCO_DOMAIN + "." + GENDER_DOMAIN + "." + exemplar.getGenderKey());
            queryParams.put(GENDER_PARAM, mappedValue);
        }
        if (exemplar.getBirthDate() != null) {
            query.append(CLIENT_QUERY_BIRTHDATE_CLAUSE);
            queryParams.put(BIRTHDATE_PARAM, exemplar.getBirthDate().toDate());
        }

        Integer count = jdbcTemplate.queryForObject("select count(*) " + query, queryParams, Integer.class);
        // log.debug("Matches - found {}", count);
        if (count != null && count > maxResults) {
            throw new TooManyResultsException();
        }

        // populate from external source for ecco
        // SEE constructAddressArray FOR DATA FLOW
        return jdbcTemplate.query("select " + CLIENT_FIELDS_CLAUSE + query, queryParams,
                (rs, rowNum) -> {
                    final ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create()
                    .externalClientRef(rs.getString(REF_ALIAS))
                    .externalClientSource(exemplar.getExternalClientSource())
                    .firstName(rs.getString(FORENAME_ALIAS))
                    .lastName(rs.getString(SURNAME_ALIAS))
                    .genderKey(getMappedReferenceData(GENDER_DOMAIN, rs.getString(GENDER_ALIAS)))
                    // TODO if wanted
                    //  .genderAtBirthKey
                    .birthDate(new LocalDate(rs.getDate(DOB_ALIAS)))
                    .code(rs.getString(CODE_ALIAS)) // beware, may clash with records from other data sources
                    .ni(rs.getString(NI_ALIAS))
                    .firstLanguageKey(getMappedReferenceData(LANGUAGE_DOMAIN, rs.getString(LANGUAGE_ALIAS)))
                    .ethnicOriginKey(getMappedReferenceData(ETHNICITY_DOMAIN, rs.getString(ETHNICITY_ALIAS)))
                    // TODO if wanted, these will also need to be loaded from ql (QL has mar_stat and nationality (API) and likely the same SQL fields)
                    //  .nationalityKey(getMappedReferenceData(NATIONALITY_DOMAIN, rs.getString(NATIONALITY_ALIAS))
                    //  .maritalStatusKey(getMappedReferenceData(MARITALSTATUS_DOMAIN, rs.getString(MARITALSTATUS_ALIAS)))
                    .religionKey(getMappedReferenceData(RELIGION_DOMAIN, rs.getString(RELIGION_ALIAS)))
                    .disabilityKey(getMappedReferenceData(FLAG_IMPAIR_DOMAIN, rs.getString(DISABILITY_ALIAS)))
                    .sexualOrientationKey(getMappedReferenceData(SEXUALITY_DOMAIN, rs.getString(SEXUALITY_ALIAS)))
                    .address(constructAddressArray(rs))
                    // TODO if wanted
                    //  .town()
                    //  .county()
                    .postCode(rs.getString(POST_CODE_ALIAS))
                    .phoneNumber(rs.getString(PHONE_ALIAS))
                    .mobileNumber(rs.getString(MOBILE_ALIAS))
                    .email(rs.getString(EMAIL_ALIAS))
                    ;

                    //log.debug("Matches - built code {} ", rs.getString(CODE_ALIAS));

                    return builder.build();
                });
    }

    /** Attempt to resolve the returned value using the supplied prefix and returned value as the suffix */
    private String getMappedReferenceData( String prefix, String key) {
        return Utils.getMappedReferenceData(referenceDataMapping, prefix, key);
    }

    /**
     * Iterates through address values (excluding post code), rejecting null and empty values, and returns them as a String array.
     * This is called from ClientController.queryAllClientsByExample which is converted by ClientDefinitionToViewModel
     * then put straight into SearchAndImport.tsx queryForResults as ClientSecretFields (potentially with all 5 lines if wanted).
     * Clicking 'import' then causes ClientList.tsx prepareNewResult to popup the detail in ClientDetailNew which then
     * shows in AddressLocation and will go through straight to saving the client on handleSaveClick, posting to ClientController
     * which takes a ClientFromViewModel and an AddressFromViewModel - only absorbing the first 3 lines anyway.
     * We just accept the first two fields - the user may need to concatenate depending on 'flat 1' etc and if buildings are used.
     * NB ClientDetail.toClientDefinition updates QL with line1, line2 and postCode to match
     *
     * @param rs The ResultSet to be checked
     * @return Non null address values, as a String array
     */
    private String[] constructAddressArray(ResultSet rs) throws SQLException {
        List<String> outputList = new ArrayList<>();

        String[] results = {
                rs.getString(ADDRESS_LINE_1_ALIAS),
                rs.getString(ADDRESS_LINE_2_ALIAS),
                rs.getString(ADDRESS_LINE_3_ALIAS),
                rs.getString(ADDRESS_LINE_4_ALIAS),
                rs.getString(ADDRESS_LINE_5_ALIAS)
                };

        // NB ignore after the first 2 lines, as these will be town/county which we don't need
        int lines = 0;
        for (String addressLine : results) {
            if (addressLine != null
                    && !addressLine.isEmpty()
                    && lines < 2) {
                outputList.add(addressLine);
                lines++;
            }
        }

        return outputList.toArray(new String[0]);
    }

}
