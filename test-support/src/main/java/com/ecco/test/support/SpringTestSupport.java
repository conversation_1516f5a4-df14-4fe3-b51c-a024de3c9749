package com.ecco.test.support;

import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.mock.env.MockPropertySource;


public class SpringTestSupport {

    public static MockPropertySource configureMockPropertySource(ConfigurableEnvironment environment) {
        MockPropertySource mockEnvVars = new MockPropertySource();
        environment.getPropertySources().replace(StandardEnvironment.SYSTEM_ENVIRONMENT_PROPERTY_SOURCE_NAME, mockEnvVars);
        return mockEnvVars;

    }


}
