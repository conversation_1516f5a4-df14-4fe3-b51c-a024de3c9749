package com.ecco.test.support;

import org.junit.rules.TestWatcher;
import org.junit.runner.Description;

import com.ecco.infrastructure.util.StackTraceSanitizer;

public final class StackTraceSanitizingRule extends TestWatcher {

    static private final StackTraceSanitizer sanitiser = new StackTraceSanitizer();

    @Override
    protected void failed(Throwable e, Description description) {
        sanitiser.sanitizeException(e);
        super.failed(e, description);
    }
}