package com.ecco.test.support;

import static org.junit.Assert.fail;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.junit.rules.TestWatcher;
import org.junit.runner.Description;
import org.junit.runners.model.Statement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ecco.infrastructure.config.root.StatementStatsInterceptor;

public class StatementStatsRule extends TestWatcher {

    /**
     * If an test package, class or method are annotated with this, a test should fail if
     * more than {@link #value()} statements are executed during the test.
     */
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD, ElementType.TYPE, ElementType.PACKAGE})
    public @interface MaxSQLStatements {

        int value();
    }

    private Logger log = LoggerFactory.getLogger(getClass());

    private StatementStatsInterceptor statementStats;

    public StatementStatsRule(StatementStatsInterceptor statementStatsInterceptor) {
        this.statementStats = statementStatsInterceptor;
    }

    @Override
    protected void starting(Description description) {
        statementStats.enableSQLCapture();
        statementStats.resetStats();
    }

    @Override
    public Statement apply(final Statement base, Description description) {
        MaxSQLStatements annotation = AnnotationUtils.findMostSpecificAnnotation(description, MaxSQLStatements.class);

        if (annotation == null) {
            return base;
        }

        return new Statement() {
            @Override
            public void evaluate() throws Throwable {
                base.evaluate();
                if (statementStats.getStatementCount() > annotation.value()) {
                    fail("Too many statements executed for this test. Max = " + annotation.value()
                    + " but there were " + statementStats.getStatementCount());
                }
            }
        };
    }

    @Override
    protected void failed(Throwable e, Description description) {
        log.warn("{} SQL statements during failed test:", statementStats.getStatementCount() );
        statementStats.getCapturedSQL().iterator().forEachRemaining( sql -> log.warn(sql) );
    }

    @Override
    protected void succeeded(Description description) {
        log.info(description.getDisplayName() + " executed {} SQL statements", statementStats.getStatementCount());
    }

}
