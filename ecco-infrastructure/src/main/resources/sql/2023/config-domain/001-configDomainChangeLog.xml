<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2023/config-domain"
    >

    <!-- HANDLES: (based on search for <createTable)
     - see configDomainChangeLog.xml
    -->

    <changeSet id="DEV-2423-contacts-scope" author="adamjhamer">
        <insert tableName="setting">
            <column name="id" valueNumeric="63"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="AgencyScope"/>
            <column name="namespace" value="com.ecco.contacts"/>
            <column name="consumedOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="GLOBAL"/>
            <column name="description" value="The scope of agency contacts"/>
            <column name="type" value="ENUM"/>
            <column name="enumType" value="com.ecco.service.AgencyScope"/>
        </insert>
    </changeSet>

    <changeSet id="rename-setting" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="setting"/>
        </preConditions>
        <renameTable oldTableName="setting" newTableName="cfg_settings"/>
    </changeSet>
    <changeSet id="DEV-2322-hr-event-categories" author="nealeu">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="197"/>
            <column name="businessKey" valueNumeric="197"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="eventCategory"/>
            <column name="name" value="annual leave"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="198"/>
            <column name="businessKey" valueNumeric="198"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="eventCategory"/>
            <column name="name" value="sick"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2464-cfg_feature-visit-client-file" author="nealeu">
        <insert tableName="cfg_feature">
            <column name="name" value="app.care.visit.allowClientFileAccess"/>
            <column name="description" value="Show icon to pop up client file from care visit"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2456-feature-services-rota" author="adamjhamer">
        <!-- show building rota link as service rota links -->
        <insert tableName="cfg_feature">
            <column name="name" value="rota.services"/>
            <column name="description" value="Service based rota"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2494-careapp-days" author="adamjhamer">
        <insert tableName="cfg_settings">
            <column name="id" valueNumeric="64"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="careapp.days"/>
            <column name="namespace" value="com.ecco.rota"/>
            <column name="consumedOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="1"/>
            <column name="description" value="The number of days for the app to load (1=today)"/>
            <column name="type" value="NUMBER"/>
            <!--<column name="enumType" value="null"/>-->
        </insert>
    </changeSet>

    <changeSet id="DEV-2494-care.scheduler-days" author="adamjhamer">
        <update tableName="cfg_settings">
            <column name="keyname" value="care.scheduler.days"/>
            <where>id=64</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2494-careapp-stopOptionsAlways" author="adamjhamer">
        <!-- show building rota link as service rota links -->
        <insert tableName="cfg_feature">
            <column name="name" value="careapp.stopOptions.always"/>
            <column name="description" value="Whether 'stop' always shows the options modal"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2379-welcome-notice-feature" author="adamjhamer">
        <!-- show building rota link as service rota links -->
        <insert tableName="cfg_feature">
            <column name="name" value="welcome.notice"/>
            <column name="description" value="Show the notice on the welcome page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2495-feature-scheduler-rota" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="rota.scheduler"/>
            <column name="description" value="Scheduler list-based rota"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2493-feature-reports-date" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="reports.breakdown.iso"/>
            <column name="description" value="Report breakdown uses iso dates"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2493-feature-reports-mui" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="reports.breakdown.mui"/>
            <column name="description" value="Report breakdown uses modern controls"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2522-charges-tab" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralView.charges"/>
            <column name="description" value="Enable service charges"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-838-menu-qr" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.qr"/>
            <column name="description" value="Enable qr scanner on main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1965-menu-guidance" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.guidance"/>
            <column name="description" value="Enable guidance on main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2455-taskChange-template" author="adamjhamer">
        <insert tableName="templates">
            <column name="name" value="TaskNotify"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="TASK_EVENT"/> <!-- MUST exist in Template.rootEntity -->
            <column name="body" type="CLOB"><![CDATA[Task notification.

Task name: {taskNameDisplay} on
[Click here to open their file](http://localhost:8080/ecco-war/nav/sr/{serviceRecipientId}/tasks/{taskName})]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2565-menu-calendar-hide" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.calendar.hide"/>
            <column name="description" value="Hides the calendar on main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2520-menu-grp-support" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.groupsupport"/>
            <column name="description" value="Shows the group comms on main menu"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- there may be someone with it disabled, so obey it -->
    <changeSet id="DEV-2520-menu-grp-support-disabled" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from cfg_module where name='community'</sqlCheck>
        </preConditions>
        <update tableName="cfg_module">
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
            <where>name='menu.groupsupport'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2520-tidy-modules" author="adamjhamer">
        <delete tableName="cfg_menu_cfg_menuitem">
            <where>menuItems_id in (select id from cfg_menuitem where module_name in ('client-centric', 'community', 'contact-centric', 'core', 'dev-only', 'help', 'hr'))</where>
        </delete>
        <delete tableName="cfg_menuitem">
            <where>module_name in ('client-centric', 'community', 'contact-centric', 'core', 'dev-only', 'help', 'hr')</where>
        </delete>
        <delete tableName="cfg_module">
            <where>name in ('client-centric', 'community', 'contact-centric', 'core', 'dev-only', 'help', 'hr')</where>
        </delete>
    </changeSet>

    <changeSet id="DEV-2520-menu-grp-comms" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.groupcomms"/>
            <column name="description" value="Shows the group comms on main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2593-tasks.edit.new.triggerLetter-feature" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.edit.new.triggerLetter"/>
            <column name="description" value="Trigger letter on various tasks"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2520-menu-grp-auxiliary" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.groupauxiliary"/>
            <column name="description" value="Shows the group auxiliary on main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2520-menu-grp-incidents" author="adamjhamer">
        <update tableName="cfg_feature">
            <column name="name" value="menu.incidents"/>
            <where>name = 'menu.groupauxiliary'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2524-history-showdetail" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.history.filter.actionsDetail.defaultOn"/>
            <column name="description" value="'show detail' on by default'"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- we set the description as a reminder only - the code simply picks up whatever the values are to match the code -->
    <!-- add genderAtBirthId -->
    <changeSet id="DEV-2612-update-clientDetail-required" author="adamjhamer">
        <update tableName="cfg_settings">
            <column name="description" value="supported fields (birthDate, firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, genderAtBirthId, sexualOrientationId, maritalStatusId, ni, housingBenefit, nhs)"/>
            <where>id=30</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2617-feature-gs-addclient">
        <delete tableName="cfg_feature">
            <where>name='groupsupport.addclient'</where>
        </delete>
        <delete tableName="cfg_feature">
            <where>name='groupsupport.addOrCreateClient'</where>
        </delete>
    </changeSet>

    <changeSet id="DEV-2631-feature-events-rota" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="rota.events"/>
            <column name="description" value="Events on a visits menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2667-listdef-order" author="adamjhamer">
        <validCheckSum>8:8f581188986b77262cf92bfb37258c64</validCheckSum>
        <addColumn tableName="cfg_list_definitions">
            <column name="orderBy" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2670-createIncident-template" author="adamjhamer">
        <insert tableName="templates">
            <column name="name" value="IncidentCreated"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="INCIDENT_CREATED_EVENT"/> <!-- MUST exist in Template.rootEntity -->
            <column name="body" type="CLOB"><![CDATA[A new incident has been received.

[Click here to open their file](http://localhost:8080/ecco-war/nav/sr/{serviceRecipientId})]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2670-createRepair-template" author="adamjhamer">
        <insert tableName="templates">
            <column name="name" value="RepairCreated"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="REPAIR_CREATED_EVENT"/> <!-- MUST exist in Template.rootEntity -->
            <column name="body" type="CLOB"><![CDATA[A new repair has been received.

[Click here to open their file](http://localhost:8080/ecco-war/nav/sr/{serviceRecipientId})]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2656-repair.communication-feature" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="repair.communication"/>
            <column name="description" value="Show communication tab"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- matches the env variable ecco.email.enabled -->
    <!-- NB BOTH settings need to be enabled, whereas sms is based on user-produced sms events, so no feature currently exists -->
    <changeSet author="adamjhamer" id="DEV-2661-triggerEmail-feature-rename">
        <update tableName="cfg_feature">
            <column name="name" value="ecco.email"/>
            <where>name='tasks.edit.new.triggerEmail'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2633-menu-buildings" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.buildings"/>
            <column name="description" value="Shows the buildings main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2656-workerassigned-generic" author="adamjhamer">
        <update tableName="templates">
            <column name="body" valueComputed="REPLACE(body, 'new client', 'new file')" />
            <where>name='WorkerAssigned'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2656-menu-repairs" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.repairs"/>
            <column name="description" value="Shows the repairs main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="OPS-117-workersLimitedToFilePermission" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="decideFinal.useAccessToFile"/>
            <column name="description" value="Limit the staff list to those who can see the file"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2719-referralDecision-template" author="adamjhamer">
        <insert tableName="templates">
            <column name="name" value="ReferralDecision"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="REFERRAL_DECISION_EVENT"/> <!-- MUST exist in Template.rootEntity -->
            <column name="body" type="CLOB"><![CDATA[A decision has been made on your referral. The referral was {decision}.
            ]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2633-menu-buildings-occupancy" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.buildings.occupancy"/>
            <column name="description" value="Shows the buildings occupancy main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2686-menu-managedvoids" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.managedvoids"/>
            <column name="description" value="Shows the managed voids main menu"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2519-menu-gs-svcCat" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="groupsupport.list.column.svcCat"/>
            <column name="description" value="Enables the service cat column on group support list"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

</databaseChangeLog>