insert into repairs_rates (id, code, description, ref, unit, rate) values (1,'BRI-0100-C','AIRBRICKS & VENTS,                                 INSTALLING TUMBLE DRIER VENT KIT','(HS1005100)','nr',74.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (2,'BRI-00150-C','AIRBRICKS & VENTS,                                    REBEDDING LOOSE AIR BRICK','(HS1005105)','nr',15.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (3,'BRI-0200-C','AIRBRICKS & VENTS,                                   RENEWING EXISTING AIRBRICK IN CAVITY WALL','(HS1005110)','nr',57.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (4,'BRI-0250-C','AIRBRICKS & VENTS,                                      RENEWING EXISTING VENT WITH HIT & MISS VENT','(HS1005115)','nr',17.99);
insert into repairs_rates (id, code, description, ref, unit, rate) values (5,'BRI-0300-C','BLOCKWORK                                                 RENEWING EXISTING COMMON BLOCKWORK WALL, 100 THICK','(HS1008100)','m2',147.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (6,'BRI-0310-C','BLOCKWORK                                                        RENEWING EXISTING SINGLE COMMON BLOCKS','(HS1008105)','nr',7.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (7,'BRI-0350-C','BUILDERSWORK IN CONNECTION WITH SERVICES, MAKING GOOD HOLE IN BRICKWORK AFTER REMOVAL OF PIPE OR THE LIKE','(HS1010100)','nr',27.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (8,'BRI-0400-C','CAVITY TRAYS,                                           INSTALLING NEW CAVITY TRAY DAMP PROOF COURSE','(HS1012100)','m',21.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (9,'BRI-0450-C','CHIMNEY BREASTS,                                   RENEWING EXISTING COMMON BRICK CHIMNEY BREAST, 1 STOREY HIGH, n/e 1.20M WIDE','(HS1016100)','nr',1700.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (10,'BRI-00500-C','CHIMNEY BREASTS,                                   RENEWING EXISTING FACING BRICK CHIMNEY BREAST, 1 STOREY HIGH, n/e 1.20M WIDE','(HS1016105)','nr',1682.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (11,'BRI-0550-C','CHIMNEY COPES,                                         RENEWING EXISTING PRECAST CONCRETE CHIMNEY COPE, SMALL (ne 0.25M2 PLAN AREA)','(HS1018110)','nr',376.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (12,'BRI-0600-C','CHIMNEY COPES,                                         RENEWING EXISTING PRECAST CONCRETE CHIMNEY COPE, MEDIUM (0.25-0.50M2 PLAN AREA)','(HS1018115)','nr',402.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (13,'BRI-0650-C','CHIMNEY COPES,                                         RENEWING EXISTING PRECAST CONCRETE CHIMNEY COPE, LARGE (0.50M-0.75M2 PLAN AREA)','(HS1018120)','nr',530.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (14,'BRI-0700-C','CHIMNEY COPES,                                        RENEWING EXISTING PRECAST CONCRETE CHIMNEY COPE, EXTRA LARGE (0.75-1.00M2 PLAN AREA)','(HS1018125)','nr',558.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (15,'BRI-0750-C','CHIMNEY POTS & FLUE TERMINALS,         INSTALLING TERR COTTA VENTED CAP TO CHIMNEY POT ','(HS1020130)','nr',40.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (16,'BRI-00800-C','CHIMNEY POTS & FLUE TERMINALS,        REBEDDING EXISTING CHIMNEY POT ','(HS1020135)','nr',57.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (17,'BRI-0850-C','CHIMNEY POTS & FLUE TERMINALS,         REMOVING CHIMNEY POT, SEALING FLUE ','(HS1020140)','nr',366.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (18,'BRI-0900-C','CHIMNEY POTS & FLUE TERMINALS,        RENEWING EXISTING CHIMNEY POT','(HS1020145)','nr',124.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (19,'BRI-1000-C','CHIMNEY POTS & FLUE TERMINALS,          RENEWING EXISTING CHIMNEY COWL','(HS1020150)','nr',230.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (20,'BRI-1050-C','CHIMNEY POTS & FLUE TERMINALS,          REPLACING CHIMNEY POT WITH METAL FLUE TERMINAL','(HS1020155)','nr',117.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (21,'BRI-1100-C','CHIMNEY STACKS,                                      RENEWING EXISTING CHIMNEY STACK n/e 5 COURSES SMALL (n/e 0.25M PLAN AREA)','(HS1022160)','nr',730.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (22,'BRI-1150-C','CHIMNEY STACKS,                                      RENEWING EXISTING CHIMNEY STACK n/e 5 COURSES MEDIUM (0.25-0.50M2 PLAN AREA)','(HS1022165)','nr',964.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (23,'BRI-1200-C','CHIMNEY STACKS,                                      RENEWING EXISTING CHIMNEY STACK n/e 5 COURSES LARGE (0.50-0.75M2 PLAN AREA)','(HS1022170)','nr',1370.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (24,'BRI-1250-C','CHIMNEY STACKS,                                       RENEWING EXISTING CHIMNEY STACK n/e 5 COURSES EXTRA LARGE (0.75 -1.00M2 PLAN AREA)','(HS1022175)','nr',1630.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (25,'BRI-1300-C','CHIMNEY STACKS,                                      RENEWING EXISTING SINGLE COMMON BRICKS IN CHIMNEY STACK','(HS1022180)','nr',346.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (26,'BRI-1350-C','CHIMNEY STACKS,                                         RENEWING EXISTING COMMON BRICKWORK IN CHIMNEY STACK, SMALL AREAS (n/e 0.50M2)','(HS1022185)','nr',423.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (27,'BRI-1400-C','CHIMNEY STACKS,                                      RENEWING EXISTING SINGLE FACING BRICKS IN CHIMNEY STACK','(HS1022190)','nr',346.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (28,'BRI-1450-C','CHIMNEY STACKS,                                       RENEWING EXISTING FACING BRICKWORK IN CHIMNEY STACK, SMALL AREAS (n/e 0.50M2)','(HS1022195','nr',424.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (29,'BRI-1500-C','CHIMNEY STACKS,                                       REPOINTING BRICKSWORK IN CHIMNEY STACK, SMALL (n/e 0.25M2 PLAN AREA)','(HS1022200)','nr',494.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (30,'BRI-1550-C','CHIMNEY STACKS,                                     REPOINTING BRICKWORK IN CHIMNEY STACK, MEDIUM  (0.25-0.50M2 PLAN AREA)','(HS1022205)','nr',625.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (31,'BRI-1600-C','CHIMNEY STACKS,                                    REPOINTING BRICKWORK IN CHIMNEY STACK, LARGE (0.50-0.75M2 PLAN AREA)','(HS1022210)','nr',860.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (32,'BRI-1650-C','CHIMNEY STACKS,                                     REPOINTING BRICKSWORK IN CHIMNEY STACK, EXTRA LARGE (0.75M-1.00M2 PLAN AREA)','(HS1022215)','nr',958.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (33,'BRI-1700-C ','CHIMNEY SUNDRIES,                                     CLEARING OBSTRUCTION IN CHIMNEY','(HS1026220)','nr',20.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (34,'BRI-1750-C','CHIMNEY SUNDRIES,                                         SMOKE TESTING CHIMNEY','(HS1026225)','nr',7.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (35,'BRI-1800-C','CHIMNEY SUNDRIES,                                     SWEEPING CHIMNEY FLUE, 1 STOREY','(HS1026230)','nr',18.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (36,'BRI-1850-C','CHIMNEY SUNDRIES,                                      SWEEPING CHIMNEY FLUE, 2 STOREY','(HS1026235)','nr',30.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (37,'BRI-1900-C','CHIMNEY SUNDRIES,                                    SWEEPING CHIMNEY FLUE, 3 STOREY','(HS1026240)','nr',43.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (38,'BRI-1950-C','COMMON BRICKWORK,                              RENEWING EXISTING COMMON BRICK WALL, 100 THICK','(HS1030100)','m2',90.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (39,'BRI-2000-C','COMMON BRICKWORK,                                 RENEWING EXISTING COMMON BRICK WALL, 215 THICK','(HS1030105)','m2',184.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (40,'BRI-2050-C','COMMON BRICKWORK,                                RENEWING EXISTING COMMON BRICK CAVITY WALL, n/e 300 THICK','(HS1030110)','m2',193.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (41,'BRI-2100-C','COMMON BRICKWORK,                               RENEWING EXISTING SINGLE COMMON BRICKS','(HS1030115)','nr',16.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (42,'BRI-2150-C','COMMON BRICKWORK,                               RENEWING EXISTING COMMON BRICK PIER, 215 WIDE, STANDARD (n/e 2.50m HIGH)','(HS1030120)','nr',70.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (43,'BRI-2200-C','COMMON BRICKWORK,                              RENEWING EXISTING COMMON BRICK PIER, 215 WIDE,NON-STANDARD (exc. 2.50m HIGH)','(HS1030125)','m',63.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (44,'BRI-2250-C','COPINGS,                                                      RENEWING EXISTING PRECAST CONCRETE COPING n/e 300 WIDE, SHORT (n/e 1.00m LONG)','(HS1035100)','nr',90.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (45,'BRI-2300-C','COPINGS,                                                     RENEWING EXISTING PRECAST CONCRETE COPING n/e 300 WIDE MEDIUM (1.00-2.00mLONG)','(HS1035105)','nr',180.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (46,'BRI-2350-C','COPINGS,                                                     RENEWING EXISTING PRECAST CONCRETE COPING n/e 300 WIDE, LONG (2.00-3.00m LONG)','(HS1035110)','nr',271.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (47,'BRI-2400-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK WALL, 100 THICK','(HS1040100)','m2',104.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (48,'BRI-2450-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK CAVITY WALL, n/e 300 THICK','(HS1040105)','m2',208.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (49,'BRI-2500-C','FACING BRICKWORK,                                    RENEWING EXISTING SINGLE FACING BRICKS','(HS1040110)','nr',16.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (50,'BRI-2550-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK PIER, 215 WIDE, STANDARD (n/e 2.50m HIGH)','(HS1040115)','nr',87.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (51,'BRI-2600-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK PIER, 215 WIDE, NON-STANDARD (exc. 2.50m HIGH)','(HS1040120)','m',139.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (52,'BRI-2650-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK-ON-EDGE CILL, SHORT (n/e 1.00m LONG)','(HS1040125)','nr',121.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (53,'BRI-2700-C','FACING BRICKWORK,                                    RENEWING EXISTING FACING BRICK-ON-EDGE CILL, MEDIUM (1.00-2.00m LONG)','(HS1040130)','nr',172.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (54,'BRI-2750-C','FACING BRICKWORK,                                  RENEWING EXISTING FACING BRICK-ON-EDGE CILL, LONG (2.00-3.00m LONG)','(HS1040135)','nr',242.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (55,'BRI-2800-C','FACING BRICKWORK,                                 RENEWING EXISTING FACING BRICK-ON-EDGE COPING, SHORT (n/e 1.00m LONG)','(HS1040140)','nr',44.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (56,'BRI-2850-C','FACING BRICKWORK,                                    RENEWING EXISTING FACING BRICK-ON-EDGE COPING, MEDIUM (1.00-2.00m LONG)','(HS1040145)','nr',84.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (57,'BRI-2900-C','FACING BRICKWORK,                                   RENEWING EXISTING FACING BRICK-ON-EDGE COPING, LONG (2.00-3.00m LONG)','(HS1040150)','nr',126.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (58,'BRI-3000-C','FACING BRICKWORK,                                    RENEWING EXISTING FACING BRICK-ON-EDGE ARCH, SHORT (n/e 1.00m)','(HS1040155)','nr',77.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (59,'BRI-3050-C','FACING BRICKWORK,                                    RENEWING EXISTING FACING BRICK-ON-EDGE ARCH, MEDIUM (1.00-2.00m LONG)','(HS1040160)','nr',154.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (60,'BRI-3100-C','FACING BRICKWORK,                                    RENEWING EXISTING FACING BRICK-ON-EDGE ARCH, LONG (2.00-3.00mLONG)','(HS1040165)','nr',231.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (61,'BRI-3150-C','FACING BRICKWORK,                                REPOINTING EXISTING BRICKWORK JOINTS, SMALL AREAS (n/e 3.00m2)','(HS1040170)','nr',124.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (62,'BRI-3200-C','FACING BRICKWORK,                                REPOINTING EXISTING BRICKWORK JOINTS, LARGE AREAS (exec 3.00m2)','(HS1040175)','m2',41.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (63,'BRI-3250-C','FIREPLACES                                                       RENEWING ASH PIT ','(HS1044100)','nr',15.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (64,'BRI-3300-C','FIREPLACES                                                  RENEWING EXISTING CONCRETE FIREPLACE LINTEL','(HS1044105)','nr',432.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (65,'BRI-3350-C','FIREPLACES                                                  RENEWING EXISTING TILED FIRE SURROUND & HEARTH','(HS1044110)','nr',598.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (66,'BRI-3450-C','FIREPLACES                                                  RENEWING FIREBRICK LININGS TO CHIMNEYS','(HS1044115)','nr',162.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (67,'BRI-3500-C','FIREPLACES                                                  RENEWING NIGHT BURNER GRATE','(HS1044120)','nr',29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (68,'BRI-3550-C','LINTELS, CILLS & THRESHOLDS,                       REFACING EXISTING PRECAST CONCRETE CILL/THRESHOLD, SMALL (n/e 1.00m LONG)','(HS1048100)','nr',80.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (69,'BRI-3600-C','LINTELS, CILLS & THRESHOLDS,                     REFACING EXISTING PRECAST CONCRETE CILL/THRESHOLD, MEDIUM (1.00-2.00m LONG)','(HS1048105)','nr',137.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (70,'BRI-3650-C','LINTELS, CILLS & THRESHOLDS,                    REFACING EXISTING PRECAST CONCRETE CILL/THRESHOLD, LARGE (2.00-3.00m LONG)','(HS1048110)','nr',201.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (71,'BRI-3700-C','LINTELS, CILLS & THRESHOLDS,                   RENEWING EXISTING PRECAST CONCRETE CILL/THRESHOLD, SMALL (n/e 1.00m LONG)','(HS1048115)','nr',82.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (72,'BRI-3750-C','LINTELS, CILLS & THRESHOLDS,                     RENEWNG EXISTING PRECAST CONCRETE CILL/THRESHOLD, MEDIUM (1.00-2.00mLONG)','(HS1048120)','nr',165.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (73,'BRI-3800-C','LINTELS, CILLS & THRESHOLDS,                     RENEWNG EXISTING PRECAST CONCRETE CILL/THRESHOLD, LONG (2.00-3.00m LONG)','(HS1048125)','nr',248.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (74,'BRI-3850-C','LINTELS, CILLS & THRESHOLDS,                    REPAIRING DAMAGED CONCRETE CILL/THRESHOLD, ANY SIZE)','(HS1048130)','nr',32.99);
insert into repairs_rates (id, code, description, ref, unit, rate) values (75,'BRI-3900-C','LINTELS, CILLS & THRESHOLDS,                   RENEWING EXISTING PRECAST CONCRETE LINTEL, SMALL (n/e 1.00m LONG), TO 100 WIDE WALL','(HS1048135)','nr',230.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (76,'BRI-3950-C','LINTELS, CILLS & THRESHOLDS,                  RENEWING EXISTING PRECAST CONCRETE LINTEL, MEDIUM (1.00-2.00m LONG), TO 100 WIDE WALL','(HS1048140)','nr',307.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (77,'BRI-4000-C','LINTELS, CILLS & THRESHOLDS,                    RENEWING EXISTING PRECAST CONCRETE LINTEL, LARGE (2.00-3.00m LONG), TO 100 WIDE WALL','(HS1048145)','nr',400.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (78,'BRI-4050-C','LINTELS, CILLS & THRESHOLDS,                  RENEWING EXISTING TILE CILL, SMALL (n/e 1.00m LONG)','(HS1048150)','nr',68.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (79,'BRI-4100-C','LINTELS, CILLS & THRESHOLDS,                 RENEWING EXISTING TILE CILL, MEDIUM (1.00-2.00m LONG)','(HS1048155)','nr',130.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (80,'BRI-4150-C','LINTELS, CILLS & THRESHOLDS,                    RENEWING EXISTING TILE CILL, LARGE (2.00-3.00m LONG)','(HS1048160)','nr',196.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (81,'BRI-4200-C','LINTELS, CILLS & THRESHOLDS,                   REPLACING EXISTING CONCRETE LINTEL WITH STEEL LINTEL, SMALL (n/e 1.00m LONG), TO CAVITY WALL','(HS1048165)','nr',266.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (82,'BRI-4250-C','LINTELS, CILLS & THRESHOLDS,                  REPLACING EXISTING CONCRETE LINTEL WITH STEEL LINTEL, MEDIUM (1.00-2.00m LONG), TO CAVITY WALL','(HS1048170)','nr',420.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (83,'BRI-4300-C','LINTELS, CILLS & THRESHOLDS,                  REPLACING EXISTING CONCRETE LINTEL WITH STEEL LINTEL, LARGE (2.00-3.00m LONG), TO CAVITY WALL','(HS1048175)','nr',673.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (84,'BRI-4400-C','POINTING,                                                REPOINTING WITH SILICONE MASTIC, SHORT (n/e 5.00m LONG)','(HS1050100)','nr',41.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (85,'BRI-4450-C','POINTING,                                                 REPOINTING WITH SILICONE MASTIC, MEDIUM (5.00-10.00m LONG)','(HS1050105)','nr',82.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (86,'BRI-4500-C','POINTING,                                                  REPOINTING WITH SILICONE MASTIC, LONG (exc. 10.0m LONG)','(HS1050110)','nr',8.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (87,'BRI-4550-C','TELL TALES                                                      BEDDING GLASS TELL TALE IN BRICKWORK','(HS1052100)','nr',4.43);
update repairs_rates set area='BRICKWORK & BLOCKWORK' where id <= 87;

insert into repairs_rates (id, code, description, ref, unit, rate) values (88,'CER-0050-C','FIREPLACES,         RENEWING EXISTING SINGLE TILE AT FIREPLACE','(HS1405100)','nr',11.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (89,'CER-0100-C','FIREPLACES,                    RENEWING TILES AT FIREPLACE COMPLETE (n/e 2.00m2)','(HS1405105)','nr',208.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (90,'CER-0150-C','FIREPLACES,                 REPOINTING FIRECLAY TILES AT FIREPLACE COMPLETE (n/e 2.00m2)','(HS1405110)','nr',145.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (91,'CER-0200-C','CILLS,                   RENEWING EXISTING CERAMIC CILL TILES, SHORT (n/e 1.00m LONG)','(HS1410100)','nr',49.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (92,'CER-0250-C','CILLS,                  RENEWING EXISTING CERAMIC CILL TILES, MEDIUM (1.00-2.00m LONG)','(HS1410105)','nr',98.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (93,'CER-0300-C','CILLS,                  RENEWING EXISTING CERAMIC CILL TILES, LONG (2.00-3.00M LONG)','(HS1410110)','nr',147.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (94,'CER-0350-C','SHOWERS,          RENEWING EXISTING TILED AREA AROUND SHOWER (ABOVE BATH)','(HS1415100)','nr',425.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (95,'CER-0400-C','SHOWERS,          RENEWING EXISTING TILED AREA AROUND SHOWER (WALK-IN)','(HS1415105)','nr',383.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (96,'CER-0450-C','SPLASHBACKS RENEWING EXISTING TILED SPLASHBACK TO BATH ','(HS1420100)','nr',145.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (97,'CER-0500-C','SPLASHBACKS RENEWING EXISTING TILED SPLASHBACK TO KITCHEN SINK','(HS1420105)','nr',91.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (98,'CER-0550-C','SPLASHBACKS RENEWING EXISTING TILED SPLASHBACK TO WASH HAND BASIN','(HS1420110)','nr',59.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (99,'CER-0600-C','WALLS,                RENEWING EXISTING CERAMIC TILES TO WALLS, SMALL AREAS (n/e 1.00m2)','(HS1425100)','nr',115.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (100,'CER-0650-C','WALLS,                RENEWING EXISTING CERAMIC TILES TO WALLS, LARGE AREAS (exc 1.00m2)','(HS1425105)','m2',104.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (101,'CER-0700-C','WALLS,               RENEWING EXISTING CERAMIC TILES TO WALLS','(HS1425110)','nr',11.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (102,'CER-0750-C','WALLS REMOVING/RAKING OUT GROUT TO EXISTING TILES, PREPARING & GROUTING TO MATCH EXISTING','(HS1425115)','m2',31.56);
update repairs_rates set area='CERAMIC TILING' where id <= 102 and id > 87;

insert into repairs_rates (id, code, description, ref, unit, rate) values (103,'CON-0050-C','FLOOR SLABS RENEWING EXISTING CONCRETE FLOOR SLAB, 150 THICK, SMALL AREAS (n/e 1.00m2','(HS1805100)','nr',111.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (104,'CON-0100-C','FLOOR SLABS RENEWING EXISTING CONCRETE FLOOR SLAB, 150 THICK, MEDIUM AREAS (1.00-5.00m2)','(HS1805105)','nr',547.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (105,'CON-0150-C','FLOOR SLABS RENEWING EXISTING CONCRETE FLOOR SLAB, 150 THICK, LARGE AREAS (5.00-10.00m2)','(HS1805110)','nr',1044.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (106,'CON-0200-C','FLOOR SLABS REPAIRING EXISTING CONCRETE FLOOR WITH GRANOLITHIC, SMALL AREAS (n/e 1.00m2)','(HS1805115','nr',61.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (107,'CON-0250-C','FLOOR SLABS REPAIRING EXISTING CONCRETE FLOOR WITH GRANOLITHIC, LARGE AREAS (exc. 1.00m2)','(HS1805120)','m2',43.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (108,'CON-0300-C','FLOOR SLABS        EXTRA OVER FLOOR SLAB FOR NEW HARDCORE & SAND BLINDING','(HS1805200)','m2',37.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (109,'CON-0410-C','FLOOR SLABS,       EXTRA OVER FLOOR SLAB FOR NEW 50mm INSULATION','(HS1805205)','m2',11.38);
-- insert into repairs_rates (id, code, description, ref, unit, rate) values (110,'CON-0300-C','UNDERPINNING, INSTALLING UNDERPINNINGI N CONCRETE & ENGINEERING BRICKWORK, n/e 1.00m DEEP, SHORT (n/e 1.00m LONG)','(HS1810100)','nr',1099.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (111,'CON-0350-C','UNDERPINNING  INSTALLING UNDERPINNING IN CONCRETE & ENGINEERING BRICKWORK, n/e 1.00m DEEP, MEDIUM (1.00-2.00m LONG)','(HS1810105)','nr',2139.40);
insert into repairs_rates (id, code, description, ref, unit, rate) values (112,'CON-0400-C','UNDERPINNING, INSTALLING UNDERPINNING IN CONCRETE & ENGINEERING BRICKWORK, n/e 1.00m DEEP, LONG (2.00-3.00m LONG)','(HS1810110)','nr',3179.19);
update repairs_rates set area='CONCRETE WORK' where id <= 112 and id > 102;

insert into repairs_rates (id, code, description, ref, unit, rate) values (113,'DEM-0050-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK BLOCKWORK, SMALL (n/e 2.00m2)','(HS2205100)','nr',120.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (114,'DEM-0100-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK BLOCKWORK, MEDIUM (2.00-4.00m2)','(HS2205105)','nr',240.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (115,'DEM-0150-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK BLOCKWORK, LARGE (4.00-600M2)','(HS2205110)','nr',362.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (116,'DEM-0200-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK COMMON BRICKWORK, SMALL (n/e 2.00m2)','(HS2205115)','nr',309.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (117,'DEM-0250-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK COMMON BRICKWORK, MEDIUM (2.00-4.00m2)','(HS2205120)','nr',600.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (118,'DEM-0300-C','BUILDING UP OPENINGS, BUILDING UP OPENING WITH 100 THICK COMMON BRICKWORK, LARGE (4.00-600m2)','(HS2205125)','nr',905.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (119,'DEM-0350-C','CHIMNEY STACKS,              TAKING DOWN CHIMNEY STACK, SMALL (n/e 0.25m2 PLAN AREA)','(HS2210100)','nr',254.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (120,'DEM-0400-C','CHIMNEY STACKS,              TAKING DOWN CHIMNEY STACK, MEDIUM (0.25-0.50m2 PLAN AREA)','(HS2210105)','nr',440.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (121,'DEM-0450-C','CHIMNEY STACKS,              TAKING DOWN CHIMNEY STACK, LARGE (0.50-0.75m2 PLAN AREA)','(HS2210110)','nr',605.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (122,'DEM-0500-C','CHIMNEY STACKS,              TAKING DOWN CHIMNEY STACK, EXTRA LARGE (0.75-1.00m2 PLAN AREA)','(HS2210115)','nr',830.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (123,'DEM-0550-C','COAL BOXES,                REMOVING EXISTING BRICK/CONCRETE COAL BOX','(HS2215100)','nr',24.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (124,'DEM-0600-C','CUTTING OPENINGS          CUTTING OPENING IN 100 THICK BRICK WALL,SMALL (n/e 2.00m2)','(HS2220100)','nr',131.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (125,'DEM-0650-C','CUTTING OPENINGS         CUTTING OPENING IN 100 THICK BRICK WALL, MEDIUM (2.00-4.00m2)','(HS2220105)','nr',262.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (126,'DEM-0700-C','CUTTING OPENINGS        CUTTING OPENING IN 100 THICK BRICK WALL, LARGE (4.00-6.00M2)','(HS2220110)','nr',394.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (127,'DEM-0750-C','CUTTING OPENINGS        CUTTING OPENING IN 215 THICK BRICK WALL, SMALL (n/e 2.00m2)','(HS2220115)','nr',163.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (128,'DEM-0800-C','CUTTING OPENINGS        CUTTING OPENING IN 215 THICK BRICK WALL, MEDIUM (2.00-4.00m2)','(HS2220120)','nr',326.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (129,'DEM-0850-C','CUTTING OPENINGS        CUTTING OPENING IN 215 THICK BRICK WALL, LARGE (4.00-6.00M2)','(HS2220125)','nr',490.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (130,'DEM-0900-C','CUTTING OPENINGS        CUTTING OPENING IN CAVITY BRICK WALL, n/e 300 THICK  SMALL (n/e 2.00m2)','(HS2220130)','nr',164.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (131,'DEM-0950-C','CUTTING OPENINGS        CUTTING OPENING IN CAVITY BRICK WALL, n/e 300 THICK  MEDIUM (2.00-4.00m2)','(HS2220135)','nr',329.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (132,'DEM-1000-C','CUTTING OPENINGS        CUTTING OPENING IN CAVITY BRICK WALL, n/e 300 THICK  LARGE (4.00-6.00m2)','(HS2220140)','nr',494.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (133,'DEM-1050-C','FENCING,                      REMOVING FENCE, ANY TYPE SHORT (n/e 5.00M LONG)','(HS2225100)','nr',52.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (134,'DEM-1100-C','FENCING,                      REMOVING FENCE, ANY TYPE MEDIUM (5.00-10.00M LONG)','(HS2225105)','nr',105.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (135,'DEM-1150-C','FENCING,                      REMOVING FENCE, ANY TYPE LONG (exc 10.00M LONG)','(HS2225110)','m',10.59);
insert into repairs_rates (id, code, description, ref, unit, rate) values (136,'DEM-1200-C','FIREPLACES,                   REMOVING EXISTING TILED FIRE SURROUND & HEARTH, SEALING OPENING','(HS2230100)','nr',184.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (137,'DEM-1250-C','STAIRS,                          REMOVING EXTERNAL STAIR COMPLETE, SMALL (TOTAL RISE n/e 1.50m)','(HS2235100)','nr',147.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (138,'DEM-1300-C','STAIRS,                         REMOVING EXTERNAL STAIR COMPLETE, LARGE (TOTAL RISE 1.50-3.00m)','(HS2235105)','nr',294.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (139,'DEM-1350-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, 100 THICK, SMALL (n/e 2.00m2)','(HS2240100)','nr',40.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (140,'DEM-1400-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, 100 THICK, MEDIUM (2.00-4.00m2)','(HS2240105)','nr',80.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (141,'DEM-1450-C','TAKING DOWN WALLS,       TAKING DOWN BRICK WALL, 100 THICK, LARGE (4.00-6.00m2)','(HS2240110)','nr',120.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (142,'DEM-1500-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, 100 THICK, EXTRA LARGE (exc. 6.00m2)','(HS2240115)','m2',20.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (143,'DEM-1550-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, 215 THICK, SMALL (n/e 2.00m2)','(HS2240120)','nr',80.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (144,'DEM-1600-C','TAKING DOWN WALLS,     TAKING DOWN BRICK WALL, 215 THICK, MEDIUM (2.00-4.00m2)','(HS2240125)','nr',160.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (145,'DEM-1650-C','TAKING DOWN WALLS,   TAKING DOWN BRICK WALL, 215 THICK, LARGE (4.00-6.00m2)','(HS2240130)','nr',240.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (146,'DEM-1700-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, 215 THICK, EXTRA LARGE (exc. 6.00m2)','(HS2240135)','m2',40.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (147,'DEM-1750-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, CAVITY WALL n/e 300 THICK, SMALL (n/e 2.00m2)','(HS2240140)','nr',60.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (148,'DEM-1800-C','TAKING DOWN WALLS,     TAKING DOWN BRICK WALL, CAVITY WALL n/e 300 THICK, MEDIUM (2.00-4.00m2)','(HS2240145)','nr',120.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (149,'DEM-1850-C','TAKING DOWN WALLS,   TAKING DOWN BRICK WALL, CAVITY WALL n/e 300 THICK, LARGE (4.00-600m2)','(HS2240150)','nr',180.18);
insert into repairs_rates (id, code, description, ref, unit, rate) values (150,'DEM-1900-C','TAKING DOWN WALLS,      TAKING DOWN BRICK WALL, CAVITY WALL n/e 300 THICK, EXTRA LARGE (exc. 600m2)','(HS2240155)','m2',30.03);
update repairs_rates set area='DEMOLITIONS/ALTERATIONS' where id <= 150 and id > 112;

insert into repairs_rates (id, code, description, ref, unit, rate) values (151,'DRA-0050-C','CHANNELS,        REBEDDING EXISTING CONCRETE CHANNEL, SHORT (n/e. 3.00m)','(HS2605100)','nr',16.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (152,'DRA-0100-C','CHANNELS,       REBEDDING EXISTING CONCRETE CHANNEL, LONG (EXC. 3.00m)','(HS2605105)','m',13.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (153,'DRA-0150-C','CHANNELS,    RENEWING EXISTING CONCRETE CHANNEL, STANDARD (255 X 125mm), SHORT (n/e 3.00m)','(HS2605110)','nr',183.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (154,'DRA-0200-C','CHANNELS,        RENEWING EXISTING CONCRETE CHANNEL, STANDARD (255 X 125mm), LONG  (exc 3.00m)','(HS2605115)','m',61.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (155,'DRA-0250-C','GULLIES,            RENEWING EXISTING CLAY GULLY','(HS2610100)','nr',280.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (156,'DRA-0300-C','GULLIES,            RENEWING EXISTING PVC GULLY','(HS2610105)','nr',274.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (157,'DRA-0350-C','GULLIES,             RENEWING EXISTING GULLY GRATING','(HS2610110)','nr',23.18);
insert into repairs_rates (id, code, description, ref, unit, rate) values (158,'DRA-0450-C','LAND DRAINAGE, CONSTRUCTING NEW BRICK SILT TRAP, SHALLOW (n/e 1.50m DEEP)','(HS2615105)','nr',3100.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (159,'DRA-0500-C','LAND DRAINAGE, CONSTRUCTING NEW CONCRETE RING SILT TRAP, SHALLOW (n/e 1.50m DEEP)','(HS2615110)','nr',1949.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (160,'DRA-0550-C','LAND DRAINAGE, INSTALLING PVC LAND DRAIN n/e 100m DIAMETER)','(HS2615100)','m',139.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (161,'DRA-0600-C','MANHOLES,          REBEDDING EXISTING MANHOLE COVER & FRAME','(HS2620100)','nr',22.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (162,'DRA-0650-C','MANHOLES,           RENEWING EXISTING CONCRETE MANHOLE COVER SLAB','(HS2620105)','nr',512.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (163,'DRA-0700-C','MANHOLES,       RENEWING EXISTING MANHOLE WITH BRICK MANHOLE, SHALLOW (n/e 1.50m DEEP)','(HS2620110)','nr',3062.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (164,'DRA-0750-C','MANHOLES,      RENEWING EXISTING MANHOLE WITH BRICK MANHOLE, DEEP (1.50-3.00m DEEP)','(HS2620115)','nr',5582.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (165,'DRA-0800-C','MANHOLES,       RENEWING EXISTING MANHOLE WITH CONCRETE RING MANHOLE, SHALLOW (n/e 1.50m DEEP)','(HS2620120)','nr',1924.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (166,'DRA-0850-C','MANHOLES,      RENEWING EXISTING MANHOLE WITH CONCRETE RING MANHOLE, DEEP (n/e 1.50-3.00m DEEP)','(HS2620125)','nr',3031.37);
insert into repairs_rates (id, code, description, ref, unit, rate) values (167,'DRA-0900-C','MANHOLES,      RENEWING EXISTING WITH POLYPROPYLENE INSPECTION CHAMBER','(HS2620130)','nr',768.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (168,'DRA-0950-C','MANHOLES,      RENEWING MANHOLE COVER & FRAME, 450 X 600, LIGHT DUTY ','(HS2620135)','nr',122.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (169,'DRA-1000-C','MANHOLES,       RENEWING MANHOLE COVER & FRAME, 450 X 600, MEDIUM DUTY ','(HS2620140)','nr',182.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (170,'DRA-1050-C','MANHOLES,      RENEWING MANHOLE COVER & FRAME, 450 X 600, HEAVY DUTY ','(HS2620145)','nr',228.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (171,'DRA-1100-C','PIPEWORK,       RENEWING EXISTING CLAY DRAIN PIPE, n/e 150mm DIAMETER, SHORT (n/e 1.00m LONG)','(HS2625100)','nr',431.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (172,'DRA-1150-C','PIPEWORK,        RENEWING EXISTING CLAY DRAIN PIPE, n/e 150mm DIAMETER, MEDIUM (1.00-2.00M LONG)','(HS2625105)','nr',731.13);
insert into repairs_rates (id, code, description, ref, unit, rate) values (173,'DRA-1200-C','PIPEWORK,        RENEWING EXISTING CLAY DRAIN PIPE, n/e 150mm DIAMETER, LONG (2.00-3.00M LONG)','(HS2625110)','nr',1030.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (174,'DRA-1250-C','PIPEWORK,        RENEWING EXISTING CLAY DRAIN PIPE, n/e 150mm DIAMETER, EXTRA LONG (exc.3.00m LONG)','(HS2625115)','m',367.91);
insert into repairs_rates (id, code, description, ref, unit, rate) values (175,'DRA-1300-C','PIPEWORK,         RENEWING EXISTING PVC DRAIN PIPE, n/e 150mm DIAMETER, SHORT (n/e 1.00m LONG)','(HS2625130)','nr',341.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (176,'DRA-1350-C','PIPEWORK,        RENEWING EXISTING PVC DRAIN PIPE, n/e 150mm DIAMETER, MEDIUM (1.00-2.00m LONG)','(HS2625135)','nr',573.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (177,'DRA-1400-C','PIPEWORK,           RENEWING EXISTING PVC DRAIN PIPE, n/e 150mm DIAMETER, LONG (2.00-3.00m LONG)','(HS2625140)','nr',805.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (178,'DRA-1450-C','PIPEWORK,        RENEWING EXISTING PVC DRAIN PIPE, n/e 150mm DIAMETER, EXTRA LONG (exc.3.00mLONG)','(HS2625145)','m',335.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (179,'DRA-1550-C','SUNDRIES            CLEANING OUT EXISTING MANHOLE','(HS2630100)','nr',11.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (180,'DRA-1600-C','SUNDRIES            CLEANING OUT GULLY','(HS2630105)','nr',10.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (181,'DRA-1650-C','SUNDRIES            PRESSURE JETTING  GULLY','(HS2630110)','nr',16.91);
insert into repairs_rates (id, code, description, ref, unit, rate) values (182,'DRA-1700-C','SUNDRIES         DRAINAGE CCTV SURVEY','(HS2630115)','nr',159.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (183,'DRA-1750-C','SUNDRIES         DRAINAGE PRESSURE TEST','(HS2630120)','nr',48.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (184,'DRA-1800-C','SUNDRIES            DRAINAGE SMOKE TEST','(HS2630125)','nr',10.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (185,'DRA-1850-C','SUNDRIES,         FINISHING DRAIN RUNS, SHORT (n/e 30m)','(HS2630130)','nr',16.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (186,'DRA-1950-C','SUNDRIES,          FINISHING DRAIN RUNS, LONG (exc. 30m)','(HS2630135)','m',0.50);
insert into repairs_rates (id, code, description, ref, unit, rate) values (187,'DRA-2000-C','SUNDRIES           PRESSURE JETTING DRAIN RUNS, SHORT (n/e 30m)','(HS2630140)','nr',101.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (188,'DRA-2100-C','SUNDRIES           PRESSURE JETTING  DRAIN RUNS, LONG  (exc 30m)','(HS2630145)','m',3.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (189,'DRA-2150-C','SUNDRIES,            RODDING DRAIN RUNS, SHORT (n/e 30m)','(HS2630150)','nr',72.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (190,'DRA-2250-C','SUNDRIES             RODDING DRAIN RUNS, LONG (exc. 30m)','(HS2630155)','m',2.17);
update repairs_rates set area='DRAINAGE' where id <= 190 and id > 150;

insert into repairs_rates (id, code, description, ref, unit, rate) values (191,'ELE-0050-C','CONSUMER UNITS & DISTRIBUTION, RENEWING CARTRIDGE FUSE','(HS3005100)','nr',3.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (192,'ELE-0100-C','CONSUMER UNITS & DISTRIBUTION, RENEWING EXISTING CONSUMER CONTROL UNIT','(HS3005105)','nr',199.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (193,'ELE-0150-C','CONSUMER UNITS & DISTRIBUTION, RENEWING MINIATURE CIRCUIT BREAKERS','(HS3005110)','nr',17.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (194,'ELE-0250-C','CONSUMER UNITS & DISTRIBUTION, INSTALLING RESIDUAL CURRENT DEVICE','(HS3005120)','nr',104.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (195,'ELE-0300-C','COOKERS,                          CONNECTING ELECTRIC COOKER','(HS3010100)','nr',127.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (196,'ELE-0400-C','COOKERS,                      OVERHAULING EXISTING ELECTRIC COOKER CONTROL UNIT','(HS3010105)','nr',14.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (197,'ELE-0500-C','COOKERS,                             RENEWING EXISTING COOKER CONTROL UNIT','(HS3010110)','nr',63.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (198,'ELE-0600-C','COOKERS,                            RESECURING COOKER CONTROL UNIT','(HS3010115)','nr',10.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (199,'ELE-0900-C','DOOR ENTRY SYSTEMS,        RENEWING EXISTING DOOR ENTRY SYSTEM COMPLETE, n/e 4 FLATS','(HS3015100)','nr',553.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (200,'ELE-0950-C','DOOR ENTRY SYSTEMS,         RENEWING EXISTING DOOR ENTRY SYSTEM COMPLETE, 5-8 FLATS','(HS3015105)','nr',1091.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (201,'ELE-1000-C','DOOR ENTRY SYSTEMS,       RENEWING EXISTING DOOR ENTRY SYSTEM COMPLETE, 9-12 FLATS','(HS3015110)','nr',1616.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (202,'ELE-1050-C','DOOR ENTRY SYSTEMS,       RENEWING EXISTING ELECTRIC DOOR LATCH FOR DOOR ENTRY SYSTEM','(HS3015115)','nr',33.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (203,'ELE-1100-C','DOOR ENTRY SYSTEMS,      RENEWING EXISTING HANDSET FOR DOOR ENTRY SYSTEM','(HS3015120)','nr',40.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (204,'ELE-1150-C','EARTH BONDING,               INSTALLING EARTH BONDING SYSTEM COMPLETE','(HS3020100)','nr',175.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (205,'ELE-1250-C','FANS,                               OVERHAULING EXISTING EXTRACT FAN, ANY TAPE','(HS3025100)','nr',11.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (206,'ELE-1300-C','FANS,                                    RENEWING EXISTING EXTRACT FAN, KITCHEN','(HS3025105)','nr',364.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (207,'ELE-1350-C','FANS,                                    RENEWING EXISTING EXTRACT FAN, TOILET/BATHROOM','(HS3025110)','nr',245.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (208,'ELE-1400-C','HEATING APPLIANCES, OVERHAULING EXISTING HEATING APPLIANCE ANY TYPE','(HS3030100)','nr',8.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (209,'ELE-1450-C','HEATING APPLIANCES,         RENEWING EXISTING ELECTRIC CONVECTOR HEATER','(HS3030105)','nr',101.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (210,'ELE-1600-C','HEATING APPLIANCES          RENEWING EXISTING ELECTRIC FIRE','(HS3030110)','nr',302.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (211,'ELE-1650-C','HEATING APPLIANCES,         RENEWING EXISTING ELECTRIC PANEL HEATER','(HS3030115)','nr',262.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (212,'ELE-1800-C','HEATING APPLIANCES,            RENEWING EXISTING ELECTRIC STORAGE HEATER','(HS3030120)','nr',561.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (213,'ELE-1950-C','HEATING CONTROLS,     OVERHAULING CENTRAL HEATING CONTROL, ANY TYPE','(HS3032125)','nr',14.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (214,'ELE-2000-C','HEATING CONTROLS,          RENEWING EXISTING CYLINDER THERMOSTAT','(HS3032130)','nr',38.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (215,'ELE-2050-C','HEATING CONTROLS,           RENEWING EXISTING ROOM THERMOSTAT','(HS3032135)','nr',40.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (216,'ELE-2100-C','HEATING CONTROLS,            RENEWING HEATING PROGRAMMER','(HS3032140)','nr',98.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (217,'ELE-2120-C','IMMERSION HEATERS OVERHAULING EXISTING IMMERSION HEATER SWITCH','(HS3035100)','nr',14.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (218,'ELE-2150-C','IMMERSION HEATERS,        RENEWING EXISTING IMMERSION HEATER','(HS3035105)','nr',108.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (219,'ELE-2200-C','IMMERSION HEATERS,       RENEWING EXISTING IMMERSION HEATER CONTROLLER','(HS3035110)','nr',110.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (220,'ELE-2250-C','LIGHTING,                       OVERHAULING EXISTING LIGHT FITTING, ANY TYPE','(HS3040100)','nr',7.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (221,'ELE-2300-C','LIGHTING,                              RENEWNG EXISTING BATTEN LIGHT FITTING','(HS3040105)','nr',23.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (222,'ELE-2350-C','LIGHTING,                               RENEWNG EXISTING BULKHEAD LIGHT FITTING','(HS3040110)','nr',86.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (223,'ELE-2400-C','LIGHTING,                              RENEWNG EXISTING CEILING PULL CORD SWITCH','(HS3040115)','nr',20.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (224,'ELE-2450-C','LIGHTING,                              RENEWNG EXISTING DOUBLE FLUORESCENT DIFFUSER','(HS3040120)','nr',16.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (225,'ELE-2500-C','LIGHTING,                               RENEWNG EXISTING DOUBLE FLUORESCENT LIGHT FITTING','(HS3040125)','nr',151.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (226,'ELE-2550-C','LIGHTING,                              RENEWNG EXISTING ONE WAY LIGHT SWITCH','(HS3040130)','nr',14.1);
insert into repairs_rates (id, code, description, ref, unit, rate) values (227,'ELE-2600-C','LIGHTING,                                RENEWNG EXISTING PENDANT LIGHT FITTING','(HS3040135)','nr',27.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (228,'ELE-2650-C','LIGHTING,                               RENEWNG EXISTING SINGLE FLUORESCENT DIFFUSER','(HS3040140)','nr',12.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (229,'ELE-2700-C','LIGHTING,                              RENEWNG EXISTING SINGLE FLUORESCENT LIGHT FITTING','(HS3040145)','nr',130.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (230,'ELE-2750- C','LIGHTING,                                RENEWNG EXISTING TWO WAY LIGHT SWITCH','(HS3040150)','nr',15.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (231,'ELE-2800-C','LIGHTING                    , REPOSITIONING EXISTING LIGHT FITTING, ANY TYPE','(HS3040155)','nr',113.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (232,'ELE-2850-C','LIGHTING,                      REPOSITIONING EXISTING LIGHT SWITCH, ANY TYPE','(HS3040160)','nr',94.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (233,'ELE-2900-C','LIGHTING,                           RESECURING LOOSE LIGHT FITTING, ANY TYPE','(HS3040165)','nr',16.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (234,'ELE-2905-C','LIGHTING,                             REMOVING EXISTING LAMP & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE G53 FITTING','(HS3040200)','nr',26.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (235,'ELE-2910-C','LIGHTING,                             REMOVING EXISTING LAMP & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE GU10 FITTING','(*********)','nr',8.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (236,'ELE-2915-C','LIGHTING,                            REMOVING EXISTING LAMP & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE E27 (GLS SHAPE) FITTING','(*********)','nr',10.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (237,'ELE-2920-C','LIGHTING,                               REMOVING EXISTING LAMP & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE E14 (CANDLE SHAPE) FITTING','(*********)','nr',8.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (238,'ELE-2930-C','LIGHTING,                                   REMOVE EXISTING FLUORESCENT TUBE & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE 600mm T8','(*********)','nr',11.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (239,'ELE-2935-C','LIGHTING,                                 REMOVE EXISTING FLUORESCENT TUBE & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE 900mm T8','(*********)','nr',12.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (240,'ELE-2940-C','LIGHTING,                                 REMOVE EXISTING FLUORESCENT TUBE & SUPPLY & FIT LED LAMP TO EXISTING LUMINAIRE 1200mm T8','(*********)','nr',21.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (241,'ELE-2950-C','POWER,                                RENEWING EXISTING SINGLE POWER SOCKET','(*********)','nr',34.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (242,'ELE-3000-C','POWER,                                RENEWING EXISTING TWIN POWER SOCKET','(HS3045105)','nr',38.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (243,'ELE-3050-C','POWER,                          REPOSITIONING EXISTING POWER SOCKET','(HS3045110)','nr',84.07);
insert into repairs_rates (id, code, description, ref, unit, rate) values (244,'ELE-3100-C','POWER,                               RESECURING LOOSE POWER SOCKET','(HS3045115)','nr',8.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (245,'ELE-3150-C','REWIRING COMPLETE,                          REWIRING ELECTRICAL INSTALLATION COMPLETE, 1 BEDROOM PROPERTY','(HS3050100)','nr',3147.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (246,'ELE-3200-C','REWIRING COMPLETE,           REWIRING ELECTRICAL INSTALLATION COMPLETE, 2 BEDROOM PROPERTY','(HS3050105)','nr',3539.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (247,'ELE-3300-C','REWIRING COMPLETE,             REWIRING ELECTRICAL INSTALLATION COMPLETE, 3 BEDROOM PROPERTY','(HS3050110)','nr',3805.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (248,'ELE-3400-C','REWIRING COMPLETE,           REWIRING ELECTRICAL INSTALLATION COMPLETE, 4 BEDROOM PROPERTY','(HS3050115)','nr',4451.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (249,'ELE-3450-C','SHOWERS,                      OVERHAULING EXISTING ELECTRIC SHOWER','(HS3055100)','nr',17.40);
insert into repairs_rates (id, code, description, ref, unit, rate) values (250,'ELE-3500-C','SHOWERS,                            RENEWING EXISTING ELECTRIC SHOWER','(HS3055105','nr',227.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (251,'ELE-3550-C','SHOWERS,                             RENEWING EXISTING ISOLATING SWITCH FOR SHOWER','(HS3050110)','nr',76.70);
insert into repairs_rates (id, code, description, ref, unit, rate) values (252,'ELE-3600-C','SMOKE DETECTORS,          OVERHAULING EXISTING SMOKE DETECTOR','(HS3060100)','nr',17.40);
insert into repairs_rates (id, code, description, ref, unit, rate) values (253,'ELE-3650-C','SMOKE DETECTORS,              RENEWING EXISTING BATTERY OPERATED SMOKE DETECTOR','(HS3060105)','nr',42.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (254,'ELE-3700-C','SMOKE DETECTORS,              RENEWING EXISTING MAINS OPERATED SMOKE DETECTOR','(HS3060110)','nr',68.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (255,'ELE-3750-C','TEMPORARY WORKS,          PROVIDING TEMPORARY LIGHTING (PER ROOM)','(HS3065100)','Wk',16.00);
insert into repairs_rates (id, code, description, ref, unit, rate) values (256,'ELE-3800-C','TEMPORARY WORKS,           PROVIDING TEMPORARY HEATING, 1 BEDROOM PROPERTY','(HS3065105)','Wk',150.00);
insert into repairs_rates (id, code, description, ref, unit, rate) values (257,'ELE-3850-C','TEMPORARY WORKS,          PROVIDING TEMPORARY HEATING, 2 BEDROOM PROPERTY','(HS3065110)','Wk',180.00);
insert into repairs_rates (id, code, description, ref, unit, rate) values (258,'ELE-3900-C','TEMPORARY WORKS,           PROVIDING TEMPORARY HEATING, 3 BEDROOM PROPERTY','(HS3065115)','Wk',210.00);
insert into repairs_rates (id, code, description, ref, unit, rate) values (259,'ELE-3950-C','TEMPORARY WORKS,           PROVIDING TEMPORARY HEATING, 4 BEDROOM PROPERTY','(HS3065120)','Wk',240.00);
insert into repairs_rates (id, code, description, ref, unit, rate) values (260,'ELE-4000-C','TESTING                                      TESTING ELECTRICAL INSTALLATION COMPLETE (PER PROPERTY)','(HS3070100)','nr',603.20);
insert into repairs_rates (id, code, description, ref, unit, rate) values (261,'ELE-4050-C','TV AERIALS,                     OVERHAULING EXISTING TV AERIAL','(HS3075100)','nr',40.60);
insert into repairs_rates (id, code, description, ref, unit, rate) values (262,'ELE-4100-C','TV AERIALS,                          RESECURING EXISTING TV AERIAL ON CHIMNEY','(HS3075105)','nr',36.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (263,'ELE-4150-C','TV AERIALS,                           RENEWING EXISTING TV AERIAL COMPLETE','(HS3075110)','nr',335.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (264,'ELE-4200-C','TV AERIALS,                          RENEWING EXISTING TV AERIAL OUTLET','(HS3075115)','nr',41.50);
insert into repairs_rates (id, code, description, ref, unit, rate) values (265,'ELE-5000-C','SOLAR PANELS,          POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 1.50kWp; POTRAIT APPLICATION','(HS3080100)','each',1621.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (266,'ELE-5005-C','SOLAR PANELS,            POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 2.00kWp; POTRAIT APPLICATION','(HS3080105)','each',1809.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (267,'ELE-5010-C','SOLAR PANELS,           POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 2.50kWp; POTRAIT APPLICATION','(HS3080110)','each',1946.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (268,'ELE-5015-C','SOLAR PANELS,             POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 3.00kWp; POTRAIT APPLICATION','(HS3080115)','each',2115.20);
insert into repairs_rates (id, code, description, ref, unit, rate) values (269,'ELE-5020-C','SOLAR PANELS,            POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 3.50kWp; POTRAIT APPLICATION','(HS3080120)','each',2371.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (270,'ELE-5030-C','SOLAR PANELS,          POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 1.50kWp; LANDSCAPE APPLICATION','(HS3080130)','each',1786.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (271,'ELE-50355-C','SOLAR PANELS,          POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 2.00kWp; LANDSCAPE APPLICATION','(HS3080135)','each',1951.91);
insert into repairs_rates (id, code, description, ref, unit, rate) values (272,'ELE-5040-C','SOLAR PANELS,          POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 2.50kWp; LANDSCAPE APPLICATION','(HS3080140)','each',2120.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (273,'ELE-5045-C','SOLAR PANELS,          POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 3.00kWp; LANDSCAPE APPLICATION','(HS3080145)','each',2236.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (274,'ELE-5050-C','SOLAR PANELS,         POLYCRYSTALLINE PV SYSTEM COMPRISING PV MODULES, PITCHED ROOF MOUNTING SYSTEM, SMA INVERTER, GENERATION METER, ISOLATORS & ASSOCIATED CABLES, 3.50kWp; LANDSCAPE APPLICATION','(HS3080150)','each',2490.47);
insert into repairs_rates (id, code, description, ref, unit, rate) values (275,'ELE-5100-C','SOLAR BATTERY PACKS, RECHARGABLE SOLAR BATTERIES, INSTALLED IN CABINETS, ON PURPOSE MAKE BRACKETS OR RACKING (MEASURED SEPARATELY); INCL CABLES, SETTING UP & CONNECTING TO INVERTER 2.4KW LITHIUM-ION BATTERY','(HS3082100)','each',515.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (276,'ELE-5105-C','SOLAR BATTERY PACKS, RECHARGABLE SOLAR BATTERIES, INSTALLED IN CABINETS, ON PURPOSE MAKE BRACKETS OR RACKING (MEASURED SEPARATELY); INCL CABLES, SETTING UP & CONNECTING TO INVERTER 3.5KW LITHIUM-ION BATTERY','(HS3082105)','each',917.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (277,'ELE-5110-C','SOLAR BATTERY PACKS, RECHARGABLE SOLAR BATTERIES, INSTALLED IN CABINETS, ON PURPOSE MAKE BRACKETS OR RACKING (MEASURED SEPARATELY); INCL CABLES, SETTING UP & CONNECTING TO INVERTER 7.10KW LITHIUM-ION PHOSPHATE STORAGE SYSTEM, INC BATTERY & CONTROL MODULE','(HS3082110)','each',2406.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (278,'ELE-5120-C','SOLAR BATTERY PACKS,           VERTICAL MOUNT WALL BRACKET FOR SINGLE BATTERY','(HS3082120)','each',34.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (279,'ELE-5130-C','SOLAR BATTERY PACKS,         LOCKABLE BATTERY CABINET 6U (550MM X 550MM X 325MM)','(HS3082130)','each',131.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (280,'ELE-5135-C','SOLAR BATTERY PACKS,          LOCKABLE BATTERY CABINET 9U (550MM X 550MM X 455MM)','(HS3082135)','each',153.80);
insert into repairs_rates (id, code, description, ref, unit, rate) values (281,'ELE-6015-C','EV CHARGING POINTS,                    EV WALL UNIT, TYPE 2 SOCKET, SINGLE PHASE, 7KW. FIXED TO WALL SURFACE, CONNECTING TO CONSUMER UNIT (MEASURED ELSEWHERE)','(HS3084115)','each',673.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (282,'ELE-6020-C','EV CHARGING POINTS,                     EV WALL UNIT, TYPE 2 SOCKET WITH TETHERED LEAD, SINGLE PHASE, 7KW. FIXED TO WALL SURFACE, CONNECTING TO CONSUMER UNIT (MEASURED ELSEWHERE)','(HS3084120)','each',691.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (283,'ELE-6200-C','EV CHARGING POINTS,                        EV DOMESTIC CONNECTION CENTRE -SINGLE PHASE 32A, WIRED OUT CONNECTION. 1 WAY','(HS3084200)','each',126.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (284,'ELE-6205-C','EV CHARGING POINTS,                        EV DOMESTIC CONNECTION CENTRE - SINGLE PHASE 32A, 30MA C CURVE A TYPE RCBO (10KA) 1 WAY','(HS3084205)','each',163.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (285,'ELE-6210-C','EV CHARGING POINTS,                       EV DOMESTIC CONNECTION CENTRE - SINGLE PHASE 32A, 30MA C CURVE DP A TYPE RCBO (10KA) & CURRENT LIMITING 1 WAY','(HS3084210)','each',173.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (286,'ELE-62220-C','EV CHARGING POINTS,                 EXTRA OVER FOR ECO SMART ENERGY DIVERTER (FOR USE WITH SOLAR PV MICROGENERATION) (NOT INCLUDING WIRING)','(HS3084220)','each',374.10);
update repairs_rates set area='ELECTRICAL INSTALLATIONS' where id <= 286 and id > 190;

insert into repairs_rates (id, code, description, ref, unit, rate) values (287,'EXT-0050-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING FLAT PLATE MILD STEEL HANDRAIL, WALL FIXED, SHORT (n/e 2.00m LONG)','(HS3405100)','nr',153.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (288,'EXT-0100-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING FLAT PLATE MILD STEEL HANDRAIL, WALL FIXED, MEDIUM (2.00-4.00m LONG)','(HS3405105)','nr',307.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (289,'EXT-0150-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING FLAT PLATE MILD STEEL HANDRAIL, WALL FIXED, LONG (4.00-6.00m LONG)','(HS3405110)','nr',461.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (290,'EXT-0200-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING TUBULAR MILD STEEL HANDRAIL, SHORT (n/e 2.00m LONG)','(HS3405115)','nr',153.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (291,'EXT-0220-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING TUBULAR MILD STEEL HANDRAIL, MEDIUM (2.00-4.00m LONG)','(HS3405120)','nr',272.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (292,'EXT-0250-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING TUBULAR MILD STEEL HANDRAIL, LONG (4.00-6.00m LONG)','(HS3405125)','nr',391.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (293,'EXT-00350-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING MILD STEEL BALUSTRADE n/e 1200 HIGH, SHORT (n/e 2.00m LONG)','(HS3405130)','nr',601.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (294,'EXT-0400-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING MILD STEEL BALUSTRADE n/e 1200 HIGH, MEDIUM (2.00-4.00m LONG)','(HS3405135)','nr',1190.40);
insert into repairs_rates (id, code, description, ref, unit, rate) values (295,'EXT-0450-C','BALUSTRADES & HANDRAILS, RENEWING EXISTING TUBULAR MILD STEEL BALUSTRADE, LONG (4.00-6.00m LONG)','(HS3405140)','nr',1779.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (296,'EXT-0650-C','BALUSTRADES & HANDRAILS, RESECURING BALUSTRADE, ANY TYPE','(HS3405145)','nr',3.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (297,'EXT-0700-C','BALUSTRADES & HANDRAILS, RESECURING HANDRAIL, ANY TYPE','(HS3405150)','nr',13.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (298,'EXT-0750-C','BOLLARDS,                 RENEWING EXISTING CONCRETE BOLLARD','(HS3410100)','nr',130.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (299,'EXT-0800-C','BOLLARDS,                 RENEWING EXISTING STEEL BOLLARD','(HS3410105)','nr',160.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (300,'EXT-0850-C','BOLLARDS,                 RENEWING EXISTING TIMBER BOLLARD','(HS3410110)','nr',95.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (301,'EXT-0900-C','CLOTHES POLES & DRYERS, RELOCATING EXISTING CLOTHES POLE','(HS3415100)','nr',36.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (302,'EXT-0950-C','CLOTHES POLES & DRYERS, RELOCATING EXISTING ROTARY CLOTHES DRIER','(HS3415105)','nr',16.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (303,'EXT-01100-C','CLOTHES POLES & DRYERS, RENEWING EXISTING ROTARY CLOTHES DRIER','(HS3415110)','nr',95.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (304,'EXT-1150-C','CLOTHES POLES & DRYERS, RENEWING EXISTING STEEL CLOTHES POLE','(HS3415115)','nr',83.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (305,'EXT-1300-C','EXCAVATIONS,            BREAKING UP CONCRETE PATH, FILLING WITH SELECTED EXCAVATED MATERIAL','(HS3420100)','m2',46.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (306,'EXT-1350-C','EXCAVATIONS,            BREAKING UP  TARMAC PAVING, FILLING WITH SELECTED EXCAVATED MATERIAL','(HS3420105)','m2',49.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (307,'EXT-1400-C','EXCAVATIONS,           REMOVING EXISTING CONCRETE PAVING SLABS, FILLING WITH SELECTED EXCAVATED MATERIAL','(HS3420110)','m2',35.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (308,'EXT-1450-C','EXCAVATIONS,            REDUCING GARDEN LEVELS n/e 250 DEEP','(HS3420115)','m2',25.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (309,'EXT-1500-C','FENCING,                    RENEWING CHAIN LINK FENCE ON EXISTING POSTS, n/e 1.20m HIGH, SHORT (n/e 5.00m LONG)','(HS3425100)','nr',131.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (310,'EXT-1550-C','FENCING,                    RENEWING CHAIN LINK FENCE ON EXISTING POSTS, n/e 1.20m HIGH, MEDIUM (n/e 5.00-10.00m LONG)','(HS3425105)','nr',301.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (311,'EXT-1600-C','FENCING,                      RENEWING CHAIN LINK FENCE ON EXISTING POSTS, n/e 1.20m HIGH, LONG  (EXC.10.00m LONG)','(HS3425110)','m',37.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (312,'EXT-1650-C','FENCING,                   RENEWING CHAIN LINK FENCE ON EXISTING POSTS, 1.20-1.80m HIGH, SHORT (n/e 5.00m LONG)','(HS3425115)','nr',198.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (313,'EXT-1700-C','FENCING,                    RENEWING CHAIN LINK FENCE ON EXISTING POSTS, 1.20-1.80m HIGH, MEDIUM  (5.00-10.00m LONG)','(HS3425120)','nr',453.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (314,'EXT-1750-C','FENCING,                   RENEWING CHAIN LINK FENCE ON EXISTING POSTS, 1.20-1.80m HIGH, LONG  (exc.10.00m LONG)','(HS3425125)','m',56.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (315,'EXT-1800-C','FENCING,                    RENEWING CHESTNUT PALE FENCE, n/e 1.20m HIGH, SHORT (n/e 5.00m LONG)','(HS3425130)','nr',174.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (316,'EXT-1850-C','FENCING,                   RENEWING CHESTNUT PALE FENCE, n/e 1.20m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425135)','nr',398.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (317,'EXT-1900-C','FENCING,                   RENEWING CHESTNUT PALE FENCE, n/e 1.20m HIGH, LONG (exc.10.00m LONG)','(HS3425140)','m',49.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (318,'EXT-2100-C','FENCING,                   RENEWING EXISTING GARDEN RAIL FENCE, n/e 0.60m HIGH, SHORT (n/e 5.00m LONG)','(HS3425145)','nr',246);
insert into repairs_rates (id, code, description, ref, unit, rate) values (319,'EXT-2150-C','FENCING,                    RENEWING EXISTING GARDEN RAIL FENCE, n/e 0.60m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425150)','nr',562.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (320,'EXT-2200-C','FENCING,                   RENEWING EXISTING GARDEN RAIL FENCE, n/e 0.60m HIGH, LONG (exc 10.00m LONG)','(HS3425155)','m',70.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (321,'EXT-2250-C','FENCING,                   RENEWING EXISTING PALLSADE FENCE, n/e 1.20m HIGH, SHORT (n/e 5.00m LONG)','(HS3425160)','nr',366.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (322,'EXT-2300-C','FENCING,                   RENEWING EXISTING PALLSADE FENCE, n/e 1.20m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425165)','nr',837.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (323,'EXT-2350-C','FENCING,                    RENEWING EXISTING PALLSADE FENCE, n/e 1.20m HIGH, LONG (exc.10.00m LONG)','(HS3425170)','m',104.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (324,'EXT-2400-C','FENCING,                   RENEWING EXISTING PALLSADE FENCE, 1.20-1.80m HIGH, SHORT (n/e 5.00MmLONG)','(HS3425175)','nr',477.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (325,'EXT-2450-C','FENCING,                   RENEWING EXISTING PALLSADE FENCE, 1.20-1.80m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425180)','nr',1091.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (326,'EXT-2500-C','FENCING,                  RENEWING EXISTING PALLSADE FENCE, 1.20-1.80m HIGH, LONG (exc 10.00m LONG)','(HS3425185)','m',136.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (327,'EXT-2550-C','FENCING,                   RENEWING EXISTING POST & WIRE FENCE n/e 1.20m HIGH, SHORT  (n/e 5.00m LONG)','(HS3425190)','nr',74.33);
insert into repairs_rates (id, code, description, ref, unit, rate) values (328,'EXT-2600-C','FENCING,                     RENEWING EXISTING POST & WIRE FENCE n/e 1.20m HIGH, MEDIUM  (5.00-10.00m LONG)','(HS3425195)','nr',169.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (329,'EXT-2650-C','FENCING,                     RENEWING EXISTING POST & WIRE FENCE n/e 1.20M HIGH, LONG (exc 10.00m LONG)','(HS3425200)','m',21.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (330,'EXT-2700-C','FENCING,                     RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, n/e 1.20m SHORT (n/e 5.000m LONG)','(HS3425205)','nr',402.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (331,'EXT-2750-C','FENCING,                        RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, n/e 1.20m MEDIUM (5.000-10.00m LONG)','(HS3425210)','nr',918.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (332,'EXT-2800-C','FENCING,                    RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, n/e 1.20m LONG (exc.10.00m LONG)','(HS3425215)','m',114.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (333,'EXT-2850-C','FENCING,                   RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, 1.20-1.80m SHORT (n/e 5.00m LONG)','(HS3425220)','nr',507.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (334,'EXT-2900-C','FENCING,                   RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, 1.20-1.80m MEDIUM (5.00-10.00m LONG)','(HS3425225)','nr',1159.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (335,'EXT-2950-C','FENCING,                    RENEWING EXISTING TIMBER CLOSE BOARDED FENCE, 1.20-1.80m LONG (exc. 10.00m LONG)','(HS3425230)','m',144.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (336,'EXT-3000-C','FENCING,                     RENEWING INTERWOVEN LAP PANEL FENCE, n/e 1.20m HIGH, SHORT (n/e 5.00m LONG)','(HS3425235)','nr',303.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (337,'EXT-3010-C','FENCING,                    RENEWING INTERWOVEN LAP PANEL FENCE, n/e 1.20m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425240)','nr',694.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (338,'EXT-3020-C','FENCING,                    RENEWING INTERWOVEN LAP PANEL FENCE, n/e 1.20m HIGH, LONG (exc. 10.00m LONG)','(HS3425245)','m',86.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (339,'EXT-3050-C','FENCING,                    RENEWING INTERWOVEN LAP PANEL FENCE, 1.20-1.80m HIGH, SHORT (n/e 5.00m LONG)','(HS3425250)','nr',344.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (340,'EXT-3060-C','FENCING,                    RENEWING INTERWOVEN LAP PANEL FENCE, 1.20-1.80m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425255)','nr',787.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (341,'EXT-3070-C','FENCING,                    RENEWING INTERWOVEN LAP PANEL FENCE, 1.20-1.80m HIGH, LONG (exc.10.00m LONG)','(HS3425260)','m',98.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (342,'EXT-3100-C','FENCING,                    RENEWING PALES & WIRING ON EXISTING POSTS, n/e 1.20m HIGH, SHORT (n/e 5.00m LONG)','(HS3425265)','nr',100.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (343,'EXT-3150-C','FENCING,                  RENEWING PALES & WIRING ON EXISTING POSTS, n/e 1.20m HIGH, MEDIUM (5.00-10.00m LONG)','(HS3425270)','nr',200.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (344,'EXT-3200-C','FENCING,                  RENEWING PALES & WIRING ON EXISTING POSTS, n/e 1.20m HIGH, LONG (exc.10.00M LONG)','(HS3425275)','m',20.37);
insert into repairs_rates (id, code, description, ref, unit, rate) values (345,'EXT-3550-C','FENCING,                   RENEWING SUPPORT WIRE TO CHESTNUT PALE FENCING, SHORT (n/e 5.00m LONG)','(HS3425280)','nr',31.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (346,'EXT-3600-C','FENCING,                   RENEWING SUPPORT WIRE TO CHESTNUT PALE FENCING, MEDIUM (5.00-10.00m LONG)','(HS3425285)','nr',63.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (347,'EXT-3650-C','FENCING,                  RENEWING SUPPORT WIRE TO CHESTNUT PALE FENCING, LONG (exc.10.00M LONG)','(HS3425290)','m',6.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (348,'EXT-3660-C','FENCING,                  RENEWING CONCRETE POST (ANY TYPE) IN EXISTING FENCING, n/e 1.80m HIGH, ANY FENCING','(HS3425295)','nr',147.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (349,'EXT-3700-C','FENCING,                   RENEWING TIMBER PALE/BOARD (ANY TYPE) IN EXISTING FENCING, n/e 1.80m HIGH, ANY FENCING','(HS3425300)','nr',11.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (350,'EXT-3750-C','FENCING,                   RENEWING TIMBER POST (ANY TYPE) IN EXISTING FENCING, n/e 1.80m HIGH, ANY FENCING','(HS3425305)','nr',40.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (351,'EXT-3900-C','FENCING,                  RESECURING FENCE, (ANY TYPE) SHORT, (n/e 5.00m LONG)','(HS3425310)','nr',39.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (352,'EXT-3950-C','FENCING,                RESECURING FENCE, (ANY TYPE) MEDIUM, (5.00-10.00m LONG','(HS3425315)','nr',78.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (353,'EXT-4000-C','FENCING,                  RESECURING FENCE, (ANY TYPE) LONG, (exc. 10.00m LONG','(HS3425320)','m',7.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (354,'EXT-4050-C','FENCING,                RESECURING LOOSE POST IN FENCING, ANY TYPE','(HS3425325)','nr',36.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (355,'EXT-4100-C','FENCING,                RESECURING TIMBER PALE/BOARD ANY TYPE','(HS3425330)','nr',5.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (356,'EXT-4120-C','GATES,            EASING/ADJUSTING GATE, ANY TYPE','(HS3430100)','nr',14.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (357,'EXT-4130-C','GATES,                       RENEWING EXISTING METAL GATE, SMALL (n/e 1.00m2)','(HS3430105)','nr',135.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (358,'EXT-4150-C','GATES,                      RENEWING EXISTING METAL GATE, LARGE (1.00-2.00m2)','(HS3430110)','nr',215.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (359,'EXT-4200-C','GATES,                       RENEWING EXISTING TIMBER GATE, SMALL (n/e 1.00m2)','(HS3430115)','nr',152.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (360,'EXT-4250-C','GATES,                       RENEWING EXISTING TIMBER GATE, LARGE (1.00-2.00m2)','(HS3430120)','nr',219.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (361,'EXT-4300-C','GATES,                       RENEWING EXISTING TIMBER GATE STOP, n/e 2.00m HIGH','(HS3430125)','nr',21.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (362,'EXT-4350-C','GATES,                       RENEWING GATE POST, n/e 2.00M HIGH, ANY TYPE','(HS3430130)','nr',84.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (363,'EXT-4400-C','GATES,                         RENEWING GATE FITTING, ANY TYPE','(HS3430135)','nr',36.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (364,'EXT-4500-C','GATES,                        REPAIRING DAMAGED METAL GATE','(HS3430140)','nr',72.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (365,'EXT-4550-C','GATES,                        REPAIRING DAMAGED TIMBER GATE','(HS3430145)','nr',58.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (366,'EXT-4650-C','KERBS & EDGINGS, REBEDDING EXISTING CONCRETE KERB/EDGING, SHORT (n/e 1.00m)','(HS3435100)','nr',29.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (367,'EXT-4700-C','KERBS & EDGINGS, REBEDDING EXISTING CONCRETE KERB/EDGING, MEDIUM (1.00-2.00m)','(HS3435105)','nr',53.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (368,'EXT-4750-C','KERBS & EDGINGS, REBEDDING EXISTING CONCRETE KERB/EDGING, LONG (2.00-3.00m)','(HS3435110)','nr',77.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (369,'EXT-4800-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE EDGING, SHORT (n/e 1.00m)','(HS3435115)','nr',50.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (370,'EXT-4850-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE EDGING, MEDIUM (1.00-2.00m)','(HS3435120)','nr',101.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (371,'EXT-4900-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE EDGING, LONG (2.00-3.00m)','(HS3435125)','nr',151.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (372,'EXT-4950-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE KERB, SHORT (n/e 1.00m LONG)','(HS3435130)','nr',70.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (373,'EXT-5000-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE KERB, MEDIUM (1.00-2.00m LONG)','(HS3435135)','nr',140.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (374,'EXT-5050-C','KERBS & EDGINGS, RENEWING EXISTING CONCRETE KERB, LONG (2.00-3.00m LONG)','(HS3435140)','nr',210.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (375,'EXT-5250-C','KERBS & EDGINGS, RENEWING EXISTING TIMBER EDGING, n/e 150 HIGH, SHORT (n/e 1.00m LONG)','(HS3435145)','nr',15.48);
insert into repairs_rates (id, code, description, ref, unit, rate) values (376,'EXT-5300-C','KERBS & EDGINGS, RENEWING EXISTING TIMBER EDGING, n/e 150 HIGH, MEDIUM (1.00-2.00m LONG)','(HS3435150)','nr',28.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (377,'EXT-5350-C','KERBS & EDGINGS, RENEWING EXISTING TIMBER EDGING, n/e 150 HIGH, LONG (2.00-3.00m LONG)','(HS3435155)','nr',38.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (378,'EXT-5400-C','MAINS SERVICES, RENEWING EXISTING WATER MAINS PIPEWORK WITH PLASTIC PIPE, SHORT (n/e 5.00m LONG)','(HS3440100)','nr',226.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (379,'EXT-5450-C','MAINS SERVICES, RENEWING EXISTING WATER MAINS PIPEWORK WITH PLASTIC PIPE, MEDIUM (5.00-10.00m LONG)','(HS3440105)','nr',401.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (380,'EXT-5500-C','MAINS SERVICES, RENEWING EXISTING WATER MAINS PIPEWORK WITH PLASTIC PIPE, LONG (10.00-15.00m LONG)','(HS3440110)','nr',575.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (381,'EXT-5550-C','PAVINGS,                          LIFTING & RELAYING EXISTING BRICK PAVIORS, SMALL AREAS (n/e 2.00m2)','(HS3445100)','nr',173.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (382,'EXT-5600-C','PAVINGS,                           LIFTING & RELAYING EXISTING BRICK PAVIORS, LARGE AREAS (exc 2.00m2)','(HS3445105)','m2',70.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (383,'EXT-5650-C','PAVINGS,                         LIFTING & RELAYING EXISTING CONCRETE PAVING SLABS, SMALL AREAS (n/e 2.00m2)','(HS3445110)','nr',96.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (384,'EXT-5700-C','PAVINGS,                          LIFTING & RELAYING EXISTING CONCRETE PAVING SLABS, LARGE AREAS (exc 2.00m2)','(HS3445115)','m2',40.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (385,'EXT-5750-C','PAVINGS,                         LIFTING & RELAYING EXISTING SINGLE CONCRETE PAVING SLAB','(HS3445120)','nr',24.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (386,'EXT-5800-C','PAVINGS,                    RENEWING EXISTING BRICK PAVIORS, SMALL AREAS (n/e 2.00m2)','(HS3445125)','nr',273.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (387,'EXT-5850-C','PAVINGS,                    RENEWING EXISTING BRICK PAVIORS, LARGE AREAS (n/e 2.00m2)','(HS3445130)','m2',110.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (388,'EXT-5900-C','PAVINGS,                    RENEWING EXISTING CONCRETE PATH, 100 THICK SMALL AREAS (n/e 2.00m2)','(HS3445135)','nr',111.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (389,'EXT-5950-C','PAVINGS,                   RENEWING EXISTING CONCRETE PATH, 100 THICK LARGE AREAS (exc 2.00m2)','(HS3445140)','m2',55.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (390,'EXT-6000-C','PAVINGS,                   RENEWING EXISTING CONCRETE PAVING SLABS, 600 X 600 SMALL AREAS (n/e 2.00m2)','(HS3445145)','nr',170.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (391,'EXT-6050-C','PAVINGS,                   RENEWING EXISTING CONCRETE PAVING SLABS, 600 X 600 LARGE AREAS (exc 2.00m2)','(HS3445150)','m2',69.07);
insert into repairs_rates (id, code, description, ref, unit, rate) values (392,'EXT-6100-C','PAVINGS,                    RENEWING EXISTING CONCRETE PAVING SLABS, 600 X 900 SMALL AREAS (n/e 2.00m2)','(HS3445155)','nr',149.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (393,'EXT-6150-C','PAVINGS,                   RENEWING EXISTING CONCRETE PAVING SLABS, 600 X 900 LARGE AREAS (exc 2.00m2)','(HS3445160)','m2',60.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (394,'EXT-6200-C','PAVINGS,                   RENEWING EXISTING GRAVEL PAVING, SMALL AREAS (n/e 2.00m2)','(HS3445165)','nr',42.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (395,'EXT-6250-C','PAVINGS,                   RENEWING EXISTING GRAVEL PAVING, LARGE AREAS (exc 2.00m2)','(HS3445170)','m2',19.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (396,'EXT-6300-C','PAVINGS,                   RENEWING EXISTING SINGLE CONCRETE PAVING SLABS, 600 X 600','(HS3445175)','nr',30.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (397,'EXT-6350-C','PAVINGS,                   RENEWING EXISTING SINGLE CONCRETE PAVING SLABS, 600 X 900','(HS3445180)','nr',40.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (398,'EXT-6400-C','PAVINGS,                   RENEWING EXISTING TARMAC PAVING, SMALL AREAS (n/e 2.00m2)','(HS3445185)','nr',146.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (399,'EXT-6450-C','PAVINGS,                  RENEWING EXISTING TARMAC PAVING, LARGE AREAS (exc 2.00m2)','(HS3445190)','m2',52.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (400,'EXT-6500-C','PAVINGS,                 REPOINTING JOINTS IN PRECAST CONCRETE PAVING SLABS, SMALL AREAS (n/e 2.00m2)','(HS3445195)','nr',22.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (401,'EXT-6550-C','PAVINGS,                 REPOINTING JOINTS IN PRECAST CONCRETE PAVING SLABS, LARGE AREAS (exc 2.00m2)','(HS3445200)','m2',10.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (402,'EXT-6600-C','PAVINGS,                 TEMPORARY CONCRETE PAVING REPAIR n/e 1.00m2','(HS3445205)','nr',33.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (403,'EXT-6650-C','PLATTS,                      RENEWING EXISTING CONCRETE ENTRANCE PLATT, SMALL (n/e 1.00m2)','(HS3450100)','nr',395.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (404,'EXT-6700-C','PLATTS,                      RENEWING EXISTING CONCRETE ENTRANCE PLATT, MEDIUM (1.00-2.00m2)','(HS3450105)','nr',678.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (405,'EXT-6750-C','PLATTS,                      RENEWING EXISTING CONCRETE ENTRANCE PLATT, LARGE (2.00-3.00m2)','(HS3450110)','nr',960.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (406,'EXT-6800-C','SIGNS,                          ERECTING NEW SIGN SUPPLIED BY EMPLOYER','(HS3455100)','nr',10.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (407,'EXT-6850-C','SIGNS,                          ERECTING NEW STANDARD WARNING SIGN (e.g. "No BALL GAMES") ','(HS3455105)','nr',124.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (408,'EXT-6900-C','STEPS,                       REBEDDING EXISTING PRECAST CONCRETE STEP, ANY SIZE','(HS3460100)','nr',24.99);
insert into repairs_rates (id, code, description, ref, unit, rate) values (409,'EXT-6950-C','STEPS,                        RENEWING EXISTING BRICK-ON-EDGE STEP, SHORT (n/e 1.00m LONG)','(HS3460105)','nr',44.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (410,'EXT-7000-C','STEPS,                          RENEWING EXISTING BRICK-ON-EDGE STEP, LONG (1.00-2.00m LONG)','(HS3460110)','nr',84.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (411,'EXT-7050-C','STEPS,                        RENEWING EXISTING PRECAST CONCRETE STEP, SHORT (n/e 1.00m LONG)','(HS3460115)','nr',62.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (412,'EXT-7100-C','STEPS,                       RENEWING EXISTING PRECAST CONCRETE STEP, LONG (1.00-2.00m LONG)','(HS3460120)','nr',94.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (413,'EXT-7150-C','STEPS,                         REPAIRING DAMAGED CONCRETE STEP','(HS3460125)','nr',32.99);
update repairs_rates set area='EXTERNAL WORKS' where id <= 413 and id > 286;

insert into repairs_rates (id, code, description, ref, unit, rate) values (414,'FLO-0050-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET TILES, SMALL AREAS (n/e 1.00m2)','(HS3805100)','nr',61.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (415,'FLO-0100-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET TILES, LARGE AREAS (exc.1.00m2)','(HS3805105)','m2',39.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (416,'FLO-0150-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET, SMALL ROOM (n/e 5.00m2)','(HS3805110)','nr',157.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (417,'FLO-0200-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET, MEDIUM ROOM (5.00-10.00m2)','(HS3805115)','nr',271.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (418,'FLO-0250-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET, LARGE ROOM (10.00-15.00m2)','(HS3805120)','nr',406.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (419,'FLO-0300-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET, EXTRA LARGE ROOM (15.00-20.00m2)','(HS3805125)','nr',542.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (420,'FLO-0350-C','CARPETS & CARPET TILES, RENEWING EXISTING CARPET TILES, TO STAIR TREADS & RISERS','(HS3805130)','m',13.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (421,'FLO-0400-C','QUARRY TILES, RENEWING EXISTING QUARRY TILES, SMALL AREAS (n/e 1.00m2)','(HS3810100)','nr',106.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (422,'FLO-0450-C','QUARRY TILES, RENEWING EXISTING QUARRY TILES, LARGE AREAS (exc. 1.00m2)','(HS3810105)','m2',97.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (423,'FLO-0500-C','QUARRY TILES, RENEWING EXISTING SINGLE QUARRY TILES','(HS3810110)','nr',11.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (424,'FLO-0550-C','QUARRY TILES, RENEWING EXISTING SKIRTING QUARRY TILES, SHORT (n/e 1.00m LONG)','(HS3810115)','nr',57.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (425,'FLO-0600-C','QUARRY TILES, RENEWING EXISTING SKIRTING QUARRY TILES, LONG (exc.1.00m LONG)','(HS3810120)','m',56.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (426,'FLO-0650-C','QUARRY TILES, RENEWING EXISTING  QUARRY TILES, TO STAIR TREADS & RISERS','(HS3810130)','m',78.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (427,'FLO-0700-C','SCREEDS,                    LAYING SELF LEVELLING SCREED, SMALL AREAS (n/e 1.00m2)','(HS3815100)','nr',12.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (428,'FLO-0750-C','SCREEDS,                    LAYING SELF LEVELLING SCREED, LARGE AREAS (exc.1.00m2)','(HS3815105)','m2',11.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (429,'FLO-0800-C','SCREEDS,             RENEWING EXISTING 50 THICK FLOOR SCREED, SMALL AREAS (n/e 1.00m2)','(HS3815110)','nr',80.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (430,'FLO-0850-C','SCREEDS,              RENEWING EXISTING 50 THICK FLOOR SCREED, LARGE AREAS (exc.1.00m2)','(HS3815115)','m2',61.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (431,'FLO-0900-C','SCREEDS,               REPAIRING CRACK IN FLOOR SCREED','(HS3815120)','nr',28.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (432,'FLO-0950-C','SCREEDS,                    LAYING SELF LEVELLING SCREED, IN STAIRCASE AREAS','(HS3815130)','m2',14.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (433,'FLO-0960-C','SCREEDS,                RENEWING EXISTING 50 THICK FLOOR SCREED, IN STAIRCASE AREAS','(HS3815135)','m2',90.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (434,'FLO-1000-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH PROPRIETARY SAFETY FLOOR SHEETING, SMALL ROOM (n/e 5.00m2)','(HS3820100)','nr',443.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (435,'FLO-1050-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH PROPRIETARY SAFETY FLOOR SHEETING, MEDIUM ROOM (5.00-10.00m2)','(HS3820105)','nr',803.1);
insert into repairs_rates (id, code, description, ref, unit, rate) values (436,'FLO-1100-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH PROPRIETARY SAFETY FLOOR SHEETING, LARGE ROOM (10.00-15.00m2)','(HS3820110)','nr',1179.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (437,'FLO-1150-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH PROPRIETARY SAFETY FLOOR SHEETING, EXTRA LARGE ROOM (15.00-20.00m2)','(HS3820115)','nr',1556.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (438,'FLO-1160-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH PROPRIETARY SAFETY FLOOR SHEETING, TO STAIR TREADS OR RISERS','(HS3820118)','m',25.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (439,'FLO-1200-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH VINYL FLOOR SHEETING, SMALL ROOM (n/e 5.00m2)','(HS3820120)','nr',216.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (440,'FLO-1250-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH VINYL FLOOR SHEETING, MEDIUM ROOM (5.00-10.00m2)','(HS3820125)','nr',367.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (441,'FLO-1300-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH VINYL FLOOR SHEETING, LARGE ROOM (10.00-15.00m2)','(HS3820130)','nr',535.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (442,'FLO-1350-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH VINYL FLOOR SHEETING, EXTRA LARGE ROOM (15.00-20.00m2)','(HS3820135)','nr',703.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (443,'FLO-1360-C','VINYL SHEETING & TILES, RENEWING EXISTING FINISH WITH VINYL FLOOR SHEETING, TO STAIR TREADS OR RISERS','(HS3820137)','m',12.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (444,'FLO-1400-C','VINYL SHEETING & TILES, RENEWING EXISTING SINGLE VINYL FLOOR TILES','(HS3820140)','nr',9.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (445,'FLO-1450-C','VINYL SHEETING & TILES, RENEWING EXISTING SINGLE VINYL FLOOR TILES, SMALL AREAS (n/e 1.00m2)','(HS3820145)','nr',40.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (446,'FLO-1500-C','VINYL SHEETING & TILES, RENEWING EXISTING SINGLE VINYL FLOOR TILES, LARGE AREAS (exc. 1.00m2)','(HS3820150)','m2',36.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (447,'FLO-1550-C','VINYL SHEETING & TILES, RENEWING EXISTING VINYL SKIRTINGS, SHORT (n/e 1.00m LONG)','(HS3820155)','nr',9.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (448,'FLO-1600-C','VINYL SHEETING & TILES, RENEWING EXISTING VINYL SKIRTINGS, LONG (exc.1.00m LONG)','(HS3820160)','m',9.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (449,'FLO-1650-C','VINYL SHEETING & TILES, RENEWING EXISTING SINGLE VINYL FLOOR TILES, TO STAIR TREADS OR RISERS ','(HS3820165)','m',15.3);
update repairs_rates set area='' where id <= 449 and id > 413;

insert into repairs_rates (id, code, description, ref, unit, rate) values (450,'FUR-0050-C','BATHROOM FITTINGS, RENEWING EXISTING BATHROOM CABINET','(HS4005100)','nr',83.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (451,'FUR-0100-C','BATHROOM FITTINGS, RENEWING EXISTING BATHROOM GLASS SHELF','(HS4005105)','nr',34.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (452,'FUR-0150-C','BATHROOM FITTINGS, RENEWING EXISTING MIRRORS, SMALL (n/e 0.50m2)','(HS4005110)','nr',30.47);
insert into repairs_rates (id, code, description, ref, unit, rate) values (453,'FUR-0200-C','BATHROOM FITTINGS, RENEWING EXISTING MIRRORS LARGE (0.50-1.00m2)','(HS4005115)','nr',64.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (454,'FUR-0250-C','BATHROOM FITTINGS, RENEWING EXISTING SHOWER RAIL & CURTAIN','(HS4005120)','nr',109.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (455,'FUR-0300-C','BATHROOM FITTINGS, RENEWING EXISTING SHOWER SCREEN (ABOVE BATH)','(HS4005125)','nr',128.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (456,'FUR-0350-C','BATHROOM FITTINGS, RENEWING EXISTING SHOWER SCREEN (TO SHOWER TRAY)','(HS4005130)','nr',193.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (457,'FUR-0400-C','BATHROOM FITTINGS, RENEWING EXISTING SOAP DISH','(HS4005135)','nr',34.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (458,'FUR-0450-C','BATHROOM FITTINGS, RENEWING EXISTING STAINLESS STEEL SINK TOP & BASE UNIT','(HS4005140)','nr',597.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (459,'FUR-0500-C','BATHROOM FITTINGS, RENEWING EXISTING TOILET ROLL HOLDER','(HS4005145)','nr',33.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (460,'FUR-0550-C','BATHROOM FITTINGS, RENEWING EXISTING TOOTHBRUSH HOLDER','(HS4005150)','nr',49.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (461,'FUR-0600-C','BATHROOM FITTINGS, RENEWING EXISTING TOWEL RAIL','(HS4005155)','nr',59.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (462,'FUR-0650-C','BATHROOM FITTINGS, RESECURING BATHROOM FITTING, ANY TYPE','(HS4005160)','nr',21.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (463,'FUR-0700-C','BATHROOM FITTINGS, RESECURING EXISTING SHOWER SCREEN','(HS4005165)','nr',28.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (464,'FUR-0750-C','CLOTHES PULLEYS, RENEWING EXISTING CLOTHES PULLEY','(HS4010100)','nr',38.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (465,'FUR-0800-C','KITCHEN FITMENTS, EASING/ADJUSTING CUPBOARD DOOR','(HS4015100)','nr',6.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (466,'FUR-0850-C','KITCHEN FITMENTS, EASING/ADJUSTING DRAWER','(HS4015105)','nr',6.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (467,'FUR-0900-C','KITCHEN FITMENTS, RENEWING EXISTING CUPBOARD DOOR','(HS4015110)','nr',56.33);
insert into repairs_rates (id, code, description, ref, unit, rate) values (468,'FUR-0950-C','KITCHEN FITMENTS, RENEWING EXISTING DRAWER TO KITCHEN UNIT','(HS4015115)','nr',33.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (469,'FUR-1000-C','KITCHEN FITMENTS, RENEWING EXISTING HARDWOOD KITCHEN WORKTOP UPSTAND, SHORT (n/e 1.00m LONG)','(HS4015120)','nr',17.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (470,'FUR-1050-C','KITCHEN FITMENTS, RENEWING EXISTING HARDWOOD KITCHEN WORKTOP UPSTAND, MEDIUM (1.00-2.00m LONG)','(HS4015125)','nr',34.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (471,'FUR-1100-C','KITCHEN FITMENTS, RENEWING EXISTING HARDWOOD KITCHEN WORKTOP UPSTAND, LONG (2.00-3.00m LONG)','(HS4015130)','nr',51.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (472,'FUR-1150-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN CORNER UNIT','(HS4015135)','nr',186.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (473,'FUR-1200-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN CORNER WALL UNIT','(HS4015140)','nr',153.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (474,'FUR-1250-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN DOUBLE BASE UNIT','(HS4015145)','nr',179.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (475,'FUR-1300-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN DOUBLE WALL UNIT','(HS4015150)','nr',175.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (476,'FUR-1350-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN HAFFIT','(HS4015155)','nr',39.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (477,'FUR-1400-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN SINGLE BASE UNIT','(HS4015160)','nr',145.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (478,'FUR-1450-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN SINGLE WALL UNIT','(HS4015165)','nr',142.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (479,'FUR-1500-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN TALL FLOOR UNIT','(HS4015170)','nr',251.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (480,'FUR-1550-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN UNIT HINGES','(HS4015175)','nr',9.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (481,'FUR-1600-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN WORKTOP, SHORT (n/e 1.00m LONG)','(HS4015180)','nr',65.13);
insert into repairs_rates (id, code, description, ref, unit, rate) values (482,'FUR-1650-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN WORKTOP, MEDIUM (1.00-2.00m LONG)','(HS4015185)','nr',114.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (483,'FUR-1700-C','KITCHEN FITMENTS, RENEWING EXISTING KITCHEN WORKTOP, LONG (2.00-3.00m LONG)','(HS4015190)','nr',165.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (484,'FUR-1750-C','KITCHEN FITMENTS, REPAIRING EXISTING DRAWER TO KITCHEN UNIT','(HS4015195)','nr',18.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (485,'FUR-1800-C','KITCHEN FITMENTS, RESECURING CUPBOARD DOOR','(HS4015200)','nr',10.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (486,'FUR-1850-C','KITCHEN FITMENTS, RESECURING EXISTING KITCHEN UNIT, ANY TYPE','(HS4015205)','nr',44.59);
insert into repairs_rates (id, code, description, ref, unit, rate) values (487,'FUR-1900-C','KITCHEN FITMENTS, RESECURING WORKTOP, ANY SIZE','(HS4015210)','nr',8.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (488,'FUR-1950-C','REFUSE CHUTES, CLEARING BLOCKAGE IN WASTE CHUTE','(HS4020100)','nr',15.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (489,'FUR-2000-C','REFUSE CHUTES, OVERHAULING WASTE CHUTE HOPPER','(HS4020105)','nr',22.82);
update repairs_rates set area='FURNITURE & EQUIPMENT' where id <= 489 and id > 449;

insert into repairs_rates (id, code, description, ref, unit, rate) values (490,'GLA-0050-C','CLEAR FLOAT GLASS (BEADED),       RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, SMALL PANES (n/e 0.15m2)','(HS4405100)','nr',33.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (491,'GLA-0070-C','CLEAR FLOAT GLASS (BEADED),       RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, MEDIUM PANES (0.15-0.50m2)','(HS4405105)','nr',67.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (492,'GLA-0090-C','CLEAR FLOAT GLASS (BEADED),       RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, LARGE PANES (0.50-1.00m2)','(HS4405110)','nr',127.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (493,'GLA-0100-C','CLEAR FLOAT GLASS (BEADED),        RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, EXTRA LARGE PANES (exc. 1.00m2)','(HS4405115)','m2',119.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (494,'GLA-0120-C','CLEAR FLOAT GLASS (PUTTY POINTED),                            RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, SMALL PANES (n/e 0.15m2)','(HS4407100)','nr',24.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (495,'GLA-0140-C','CLEAR FLOAT GLASS (PUTTY POINTED),                           RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, MEDIUM PANES (0.15-0.50m2)','(HS4407105)','nr',50.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (496,'GLA-0160-C','CLEAR FLOAT GLASS (PUTTY POINTED),                         RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, LARGE PANES (0.50-1.00m2)','(HS4407110)','nr',96.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (497,'GLA-0180-C','CLEAR FLOAT GLASS (PUTTY POINTED),                         RENEWING EXISTING GLAZING WITH CLEAR FLOAT GLAZING, EXTRA LARGE PANES (exc. 1.00m2)','(HS4407115)','m2',91.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (498,'GLA-0250-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED CLEAR FLOAT GLASS UNIT, SMALL PANES (n/e 0.15m2)','(HS4410100)','nr',56.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (499,'GLA-0260-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED CLEAR FLOAT GLASS UNIT, MEDIUM PANES (0/15-0.50m2)','(HS4410105)','nr',114.2);
insert into repairs_rates (id, code, description, ref, unit, rate) values (500,'GLA-0270-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED CLEAR FLOAT GLASS UNIT, LARGE PANES (0.50-1.00m2)','(HS4410110)','nr',238.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (501,'GLA-0280-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED CLEAR FLOAT GLASS UNIT, EXTRA LARGE PANES (exc.1.00m2)','(HS4410115)','m2',270.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (502,'GLA-0290-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED SAFETY GLASS UNIT, SMALL PANES (n/e 0.15m2)','(HS4410120)','nr',59.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (503,'GLA-0300-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED SAFETY GLASS UNIT, MEDIUM PANES (0.15-0.50m2)','(HS4410125)','nr',120.13);
insert into repairs_rates (id, code, description, ref, unit, rate) values (504,'GLA-0310-C','DOUBLE GLAZED UNITS,                 RENEWING EXISTING GLAZING WITH DOUBLE GLAZED SAFETY GLASS UNIT, LARGE PANES (0.50-1.00m2)','(HS4410130)','nr',255.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (505,'GLA-0320-C','DOUBLE GLAZED UNITS,                   RENEWING EXISTING GLAZING WITH DOUBLE GLAZED SAFETY GLASS UNIT, EXTRA LARGE PANES (exc.1.00m2)','(HS4410135)','m2',291.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (506,'GLA-0350-C','TRIPLE GLAZING,                               RENEWING EXISTING GLAZING WITH TRIPLE GLAZED CLEAR FLOAT GLASS UNIT, SMALL PANES (n/e 0.15m2)','(HS4412100)','nr',134.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (507,'GLA-0360-C','TRIPLE GLAZING,                               RENEWING EXISTING GLAZING WITH TRIPLE GLAZED CLEAR FLOAT GLASS UNIT, LARGE PANES (0.50-1.00m2)','(HS4412105)','nr',158.37);
insert into repairs_rates (id, code, description, ref, unit, rate) values (508,'GLA-0370-C','TRIPLE GLAZING,                               RENEWING EXISTING GLAZING WITH TRIPLE GLAZED CLEAR FLOAT GLASS UNIT, LARGE PANES (0.50-1.00m2)','(HS4412110)','nr',330.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (509,'GLA-0380-C','TRIPLE GLAZING,                               RENEWING EXISTING GLAZING WITH TRIPLE GLAZED CLEAR FLOAT GLASS UNIT, EXTRA LARGE PANES (exc.1.00m2)','(HS4412115)','m2',381.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (510,'GLA-0610-C','SECONDARY GLAZING,                              WROT SOFTWOOD SECONDARY GLAZING UNIT, FIXED TO INSIDE OF EXISTING FRAME, SMALL n.e. 1.00m2','(HS4414100)','nr',77.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (511,'GLA-0612-C','SECONDARY GLAZING,                              WROT SOFTWOOD SECONDARY GLAZING UNIT, FIXED TO INSIDE OF EXISTING FRAME, MEDIUM  n.e. 1.00 - 2.00m2','(HS4414102)','nr',109.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (512,'GLA-0614-C','SECONDARY GLAZING,                             WROT SOFTWOOD SECONDARY GLAZING UNIT, FIXED TO INSIDE OF EXISTING FRAME, LARGE n.e. 2.00 - 3.00m2','(HS4414104)','nr',130.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (513,'GLA-0620-C','SECONDARY GLAZING,                         ACRYLIC OR POLYCARBONATE SECONDARY GLAZING, FIXED WITH MAGNETIC STRIPS','(HS4414110)','m2',95.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (514,'GLA-0650-C','GEORGIAN WIRED GLASS (BEADED),                  RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, SMALL PANES (n/e 0.15m2)','(HS4415100)','nr',39.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (515,'GLA-0670-C','GEORGIAN WIRED GLASS (BEADED),              RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, MEDIUM PANES (0.15-0.50m2)','(HS4415105)','nr',89.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (516,'GLA-0690-C','GEORGIAN WIRED GLASS (BEADED),           RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, LARGE PANES (0.50-1.00m2)','(HS4415110)','nr',172.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (517,'GLA-0700-C','GEORGIAN WIRED GLASS (BEADED) RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, EXTRA LARGE PANES (exc.1.00m2)','(HS4415115)','m2',164.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (518,'GLA-0750-C','GEORGIAN WIRED GLASS (PUTTY POINTED), RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, SMALL PANES (n/e 0.15m2)','(HS4417100)','nr',32.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (519,'GLA-0770-C','GEORGIAN WIRED GLASS (PUTTY POINTED), RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, MEDIUM PANES (0.15-0.50m2)','(HS4417105)','nr',77.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (520,'GLA-0790-C','GEORGIAN WIRED GLASS (PUTTY POINTED), RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, LARGE PANES (0.50-1.00m2)','(HS4417110)','nr',150.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (521,'GLA-0800-C','GEORGIAN WIRED GLASS (PUTTY POINTED), RENEWING EXISTING GLAZING WITH GEORGIAN WIRED GLAZING, EXTRA LARGE PANES (exc.1.00m2)','(HS4417115)','m2',145.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (522,'GLA-0850-C','LAMINATED GLASS,                            RENEWING EXISTING GLAZING WITH LAMINATED GLASS, SMALL PANES (n/e 0.15m2)','(HS4420100)','nr',47.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (523,'GLA-0900-C','LAMINATED GLASS,                           RENEWING EXISTING GLAZING WITH LAMINATED GLASS, MEDIUM PANES (0.15-0.50m2)','(HS4420105)','nr',111.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (524,'GLA-0950-C','LAMINATED GLASS,                           RENEWING EXISTING GLAZING WITH LAMINATED GLASS, LARGE PANES (0.50-1.00m2)','(HS4420110)','nr',216.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (525,'GLA-1000-C','LAMINATED GLASS,                            RENEWING EXISTING GLAZING WITH LAMINATED GLASS, EXTRA LARGE PANES (exc. 1.00m2)','(HS4425115)','m2',208.82);
insert into repairs_rates (id, code, description, ref, unit, rate) values (526,'GLA-1050-C','SAFETY FILM,                                        APPLYING SAFETY FILM TO GLASS, SMALL PANES (n/e 0.15m2)','(HS4425100)','nr',14.25);
insert into repairs_rates (id, code, description, ref, unit, rate) values (527,'GLA-1100-C','SAFETY FILM,                                      APPLYING SAFETY FILM TO GLASS, MEDIUM PANES (0.15-0.50m2)','(HS4425105)','nr',36.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (528,'GLA-1150-C','SAFETY FILM,                                       APPLYING SAFETY FILM TO GLASS, LARGE PANES (0.50-1.00m2)','(HS4425110)','nr',55.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (529,'GLA-1200-C','SAFETY FILM,                                       APPLYING SAFETY FILM TO GLASS, EXTRA LARGE PANES (exc.1.00m2)','(HS4430115)','m2',48.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (530,'GLA-1250-C','TOUGHENED GLASS,                          RENEWING EXISTING GLAZING WITH TOUGHENED GLASS, SMALL PANES (n/e 0.15m2)','(HS4430100)','nr',33.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (531,'GLA-1300-C','TOUGHENED GLASS,                         RENEWING EXISTING GLAZING WITH TOUGHENED GLASS, MEDIUM PANES (0.15-0.50m2)','(HS4430105)','nr',68.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (532,'GLA-1350-C','TOUGHENED GLASS,                         RENEWING EXISTING GLAZING WITH TOUGHENED GLASS, LARGE PANES (0.50-1.00m2)','(HS4430110)','nr',130.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (533,'GLA-1400-C','TOUGHENED GLASS,                          RENEWING EXISTING GLAZING WITH TOUGHENED GLASS, EXTRA LARGE PANES (exc.1.00m2)','(HS4435115)','m2',123);
insert into repairs_rates (id, code, description, ref, unit, rate) values (534,'GLA-1500-C','WINDOW VENTS,                              RENEWING EXISTING WINDOW VENT, BATHROOM','(HS4435100)','nr',217.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (535,'GLA-1550-C','WINDOW VENTS,                              RENEWING EXISTING WINDOW VENT, KITCHEN','(HS4435105)','nr',343.26);
update repairs_rates set area='GLAZING' where id <= 535 and id > 489;

insert into repairs_rates (id, code, description, ref, unit, rate) values (536,'INS-0050-C','LOFTS,                                            RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 100 THICK, SMALL (n/e 50m2)','(HS4805100)','nr',576.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (537,'INS-0100-C','LOFTS,                                             RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 100 THICK, MEDIUM (50-75m2)','(HS4805105)','nr',864.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (538,'INS-0150-C','LOFTS,                                              RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 100 THICK, LARGE (75-100m2)','(HS4805110)','nr',1153.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (539,'INS-0200-C','LOFTS,                                             RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 150 THICK, SMALL (n/e 50m2)','(HS4805115)','nr',798.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (540,'INS-0250-C','LOFTS,                                           RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 150 THICK, MEDIUM (50-75m2)','(HS4805120)','nr',1197.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (541,'INS-0300-C','LOFTS,                                            RENEWING EXISTING INSULATION QUILT IN LOFT SPACE, 150 THICK, LARGE (75-100m2)','(HS4805125)','nr',1596.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (542,'INS-0400-C','INTERNAL WALL INSULATION,          INSTALL INTERNAL WALL INSULATION SYSTEM ONTO SOLID WALL TO MEET CURRENT BUILDING REGULATION U VALUE, AS GROSS WALL AREA; INCLUDES REPOSITIONING OF ALL SERVICES MOUNTED ON THE WALL ALTERED TO SUIT. ALL WINDOW REVEALS & SOFFITS INSULATED WHERE REQUIRED, & NEW CILL','(HS4810100)','m2',162.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (543,'INS-0500-C','EXTERNAL WALL INSULATION       APPROVED SYSTEM TO CURRENT BUILDING REGULATIONS U VALUE, RENDER FINISH AS GROSS WALL AREA; INCLUDES POSITIONI OF ALL DOWNPIPES, DRAINAGE &  SERVICES MOUNTED ON THE WALL ALTERED TO SUIT. ALL WINDOW REVEALS & SOFFITS INSULATED WHERE REQUIRED, ALL CILL DETAILS AS PER SYSTEM DESIGNER. RENDER FINISH SUPPLIED BY SYSTEM DESIGNER','(HS4815100)','m2',163.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (544,'INS-0510-C','EXTERNAL WALL INSULATION,          APPLIED BELOW DPC LEVEL, APPROVED SYSTEM TO CURRENT BUILDING REGULATIONS U VALUE, RENDER FINISH, MEASURED AS GROSS WALL AREA','(HS4815105)','m',45.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (545,'INS-0520-C','EXTERNAL WALL INSULATION,            EXTRA OVER FOR BRICK SLIP FINISH TO EXTERNAL WALL INSULATION','(HS4815110)','m2',137.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (546,'INS-0540-C','EXTERNAL WALL INSULATION,            EXTRA OVER FOR LEAVING SERVICES IN POSITION & INSULATIING BEHIND DRAINAGE OR FOUL PIPES USING HIGH PERFORMANCE (AEROGEL) INSULATION & RENDER','(HS4815115)','m',23.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (547,'INS-0550-C','EXTERNAL WALL INSULATION,            EXTRA OVER FOR INSULATE WINDOW REVEAL USING HIGH PERFORMANCE (AEROGEL) INSULATION & RENDER','(HS4815120)','m',15.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (548,'INS-0600-C','SUSPENDED TIMBER FLOORS,                 LIFT FLOORBOARDS & INSTALL INSULATION TO SUSPENDED TIMBER FLOOR WITH THERMAL CONDUCTIVITY TO MINIMUM 0.037 W/mk, REPLACE FLOORBOARDS ON COMPLETION, FILL ALL GAPS TO PREVENT DRAUGHTS','(HS4820100)','m2',50.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (549,'INS-0610-C','SUSPENDED TIMBER FLOORS,                 LIFT FLOORBOARDS & INSTALL INSULATION TO SUSPENDED TIMBER FLOOR WITH THERMAL CONDUCTIVITY TO MINIMUM 0.026 W/mk, REPLACE FLOORBOARDS ON COMPLETION, FILL ALL GAPS TO PREVENT DRAUGHTS','(HS4820110)','m2',53.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (550,'INS-0700-C','SUSPENDED TIMBER BASEMENT CEILING,           INSTALL INSULATION TO SUSPENDED TIMBER BASEMENT CEILING WITH THERMAL CONDUCTIVITY TO MINIMUM 0.037 W/mk SUPPORT FROM FROM BENEATH WITH GEOTEXTILE & TAPE ALL JOINTS','(HS4825100)','m2',17.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (551,'INS-0710-C','SUSPENDED TIMBER BASEMENT CEILING, CUT TO FIT & INSTALL INSULATION BOARD TO 100mm BETWEEN FLOOR JOISTS. THERMAL CONDUCTIVITY MINIMUM 0.025W/mk FILL ALL GAPS WITH EXPANDING FOAM','(HS4825110)','m2',21.57);
insert into repairs_rates (id, code, description, ref, unit, rate) values (552,'INS-0720-C','SUSPENDED TIMBER BASEMENT CEILING, CUT TO FIT & INSTALL INSULATION BOARD TO 50mm BETWEEN FLOOR JOISTS. THERMAL CONDUCTIVITY MINIMUM 0.025W/mk FILL ALL GAPS WITH EXPANDING FOAM','(HS4825120)','m2',14.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (553,'INS-0800-C','PIPEWORK INSULATION,              INSULATION TO 15mm DIA WATER PIPE','(HS4830100)','m',11.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (554,'INS-0810-C','PIPEWORK INSULATION,             INSULATION TO 22MM DIA WATER PIPE','(HS4830110)','m',12.40);
update repairs_rates set area='INSULATION WORK' where id <= 554 and id > 535;

insert into repairs_rates (id, code, description, ref, unit, rate) values (555,'MEC-0050-C','BOILERS & FIRES,             OVERHAULING EXISTING BOILER & FIRE, ANY TYPE','(HS5605100)','nr',82.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (556,'MEC-0100-C','BOILERS & FIRES,                   RENEWING EXISTING BOILER WITH COMBINATION BOILER','(HS5605105)','nr',1874.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (557,'MEC-0150-C','BOILERS & FIRES,                  RENEWING EXISTING BOILER WITH FLOOR/WALL MOUNTED GAS BOILER','(HS5605110)','nr',1780.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (558,'MEC-0200-C','BOILERS & FIRES,                   RENEWING EXISTING GAS BACK BOILER','(HS5605115)','nr',1610.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (559,'MEC-0250-C','BOILERS & FIRES,                    RENEWING EXISTING GAS FIRE','(HS5605120)','nr',512.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (560,'MEC-0300-C','BOILERS & FIRES,                  RENEWING EXISTING SOLID FUEL BACK BOILER','(HS5605125)','nr',2130.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (561,'MEC-0350-C','BOILERS & FIRES,                   RENEWING EXISTING SOLID FUEL FIRE (ENCLOSED APPLIANCE)','(HS5605130)','nr',581.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (562,'MEC-0400-C','BOILERS & FIRES,                  RENEWING EXISTING SOLID FUEL FIRE (OPEN APPLIANCE)','(HS5605135)','nr',381.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (563,'MEC-0450-C','BOILERS & FIRES,               RESECURING LOOSE FIRE, ANY TYPE','(HS5605140)','nr',13.50);
insert into repairs_rates (id, code, description, ref, unit, rate) values (564,'MEC-0500-C','CENTRAL HEATING CONTROLS, OVERHAULING EXISTING HEATING PUMP','(HS5610100)','nr',19.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (565,'MEC-0550-C','CENTRAL HEATING CONTROLS, RENEWING EXISTING HEATING PUMP','(HS5610105)','nr',136.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (566,'MEC-0600-C','CENTRAL HEATING CONTROLS, RENEWING THERMOSTAT TO EXISTING BACK BOILER, ANY TYPE','(HS5610110)','nr',41.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (567,'MEC-0650-C','COLD WATER TANKS,            RENEWING EXISTING COLD WATER TANK FOR HEATING SYSTEM','(HS5615100)','nr',209.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (568,'MEC-0700-C','COLD WATER TANKS,            RENEWING INSULATION TO COLD WATER TANK FOR HEATING SYSTEM','(HS5615105)','nr',49.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (569,'MEC-0750-C','COOKERS,                           CONNECTING GAS COOKER','(HS5620100)','nr',31.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (570,'MEC-0800-C','DRAINING DOWN & REFILLING, DRAINING DOWN HEATING SYSTEM, REFILLING','(HS5625100)','nr',46.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (571,'MEC-0900-C','FLUES,                                              NEW STAINLESS STEEL FLUE LINER COMPLETE, 1 STOREY','(HS5630100)','nr',306.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (572,'MEC-0950-C','FLUES,                                              NEW STAINLESS STEEL FLUE LINER COMPLETE, 2 STOREYS','(HS5630105)','nr',471.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (573,'MEC-1000-C','HEATING INSTALLATIONS COMPLETE, INSTALLING GAS CENTRAL HEATING COMPLETE, 1 BEDROOM PROPERTY','(HS5635100)','nr',3742.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (574,'MEC-1050-C','HEATING INSTALLATIONS COMPLETE, INSTALLING GAS CENTRAL HEATING COMPLETE, 2 BEDROOM PROPERTY','(HS5635105)','nr',4035.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (575,'MEC-1150-C','HEATING INSTALLATIONS COMPLETE,  INSTALLING GAS CENTRAL HEATING COMPLETE, 3 BEDROOM PROPERTY','(HS5635110)','nr',4398.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (576,'MEC-1250-C','HEATING INSTALLATIONS COMPLETE, INSTALLING GAS CENTRAL HEATING COMPLETE, 4 BEDROOM PROPERTY','(HS5635115)','nr',4621.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (577,'MEC-1450-C','RADIATORS & PIPEWORK, RENEWING EXISTING COPPER PIPEWORK, MICROBORE (n/e 10mm), SHORT (n/e 3.00m LONG)','(HS5640100)','nr',25.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (578,'MEC-1500-C','RADIATORS & PIPEWORK, RENEWING EXISTING COPPER PIPEWORK, MICROBORE (n/e 10mm), LONG (exc. 3.00m LONG)','(HS5640105)','m',6.80);
insert into repairs_rates (id, code, description, ref, unit, rate) values (579,'MEC-1550-C','RADIATORS & PIPEWORK, RENEWING EXISTING PIPEWORK MANIFOLD (n/e 8 OUTLETS)','(HS5640110)','nr',91.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (580,'MEC-1600-C','RADIATORS & PIPEWORK, REPAIRING LEAK IN HEATING PIPEWORK','(HS5640115)','nr',45.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (581,'MEC-1650-C','RADIATORS & PIPEWORK, OVERHAULING RADIATOR, ANY TYPE','(HS5640120)','nr',35.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (582,'MEC-1700-C','RADIATORS & PIPEWORK, RENEWING EXISTING LOCKSHIELD RADIATOR VALVE','(HS5640125)','nr',39.90);
insert into repairs_rates (id, code, description, ref, unit, rate) values (583,'MEC-1750-C','RADIATORS & PIPEWORK  RENEWING EXISTING VALVE WITH THERMOSTATIC RADIATOR VALVE','(HS5640130)','nr',50.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (584,'MEC-1800-C','RADIATORS & PIPEWORK, REPAIRING LEAK IN RADIATOR VALVE','(HS5640135)','nr',14.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (585,'MEC-1850-C','RADIATORS & PIPEWORK, RESECURING EXISTING RADIATOR','(HS5640140)','nr',23.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (586,'MEC-1900-C','RADIATORS & PIPEWORK,     VENTING RADIATORS & BALANCING HEATING SYSTEM (PER PROPERTY)','(HS5640145)','nr',46.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (587,'MEC-1950-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, SINGLE PANEL, SMALL (n/e 0.50m2)','(HS5640150)','nr',137.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (588,'MEC-2000-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, SNGLE PANEL, MEDIUM (0.50-1.00m2)','(HS5640155)','nr',201.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (589,'MEC-2050-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, SINGLE PANEL, LARGE (1.00-1.50m2)','(HS5640160)','nr',297.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (590,'MEC-2100-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, SINGLE PANEL, EXTRA LARGE (1.50-2.00m2)','(HS5640165)','nr',394.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (591,'MEC-2150-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (P+), SMALL (n/e 0.50m2)','(HS5640170)','nr',120.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (592,'MEC-2200-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (P+), MEDIUM (0.50-1.00m2)','(HS5640175)','nr',256.18);
insert into repairs_rates (id, code, description, ref, unit, rate) values (593,'MEC-2250-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (P+), LARGE (1.00-1.50m2)','(HS5640180)','nr',385.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (594,'MEC-2300-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (P+), EXTRA LARGE (1.50-2.00m2)','(HS5640185)','nr',335.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (595,'MEC-2350-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (K2), SMALL (n/e 0.50m2)','(HS5640190)','nr',184.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (596,'MEC-2400-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (K2), MEDIUM (0.50-1.00m2)','(HS5640195)','nr',281.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (597,'MEC-2450-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (K2), LARGE (1.00-1.50m2)','(HS5640200)','nr',426.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (598,'MEC-2500-C','RADIATORS & PIPEWORK, RENEWING EXISTING RADIATOR, DOUBLE PANEL (K2), EXTRA LARGE (1.50.2.00m2)','(HS5640205)','nr',669.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (599,'MEC-3000-C','AIR SOURCE HEAT PUMPS,         SUPPLY & INSTALL AIR SOURCE HEAT PUMP SYSTEM COMPLETE. (HEATING ONLY, exc HEAT DISTRIBUTION SYSTEM) 4Kw','(HS5650100)','each',8702.10);
insert into repairs_rates (id, code, description, ref, unit, rate) values (600,'MEC-3010-C','AIR SOURCE HEAT PUMPS,      SUPPLY & INSTALL AIR SOURCE HEAT PUMP SYSTEM COMPLETE. (HEATING ONLY, exc HEAT DISTRIBUTION SYSTEM) 6Kw','(HS5650105)','each',8770.10);
insert into repairs_rates (id, code, description, ref, unit, rate) values (601,'MEC-3020-C','AIR SOURCE HEAT PUMPS,           SUPPLY & INSTALL AIR SOURCE HEAT PUMP SYSTEM COMPLETE. (HEATING ONLY, exc HEAT DISTRIBUTION SYSTEM) 8Kw','(HS5650110)','each',9469.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (602,'MEC-3030-C','AIR SOURCE HEAT PUMPS,         SUPPLY & INSTALL AIR SOURCE HEAT PUMP SYSTEM COMPLETE. (HEATING & HOT WATER exc HEAT DISTRIBUTION SYSTEM) 8Kw','(HS5650115)','each',9618.01);
update repairs_rates set area='MECHANICAL INSTALLATIONS' where id <= 602 and id > 554;

insert into repairs_rates (id, code, description, ref, unit, rate) values (603,'PLU-0050-C','GUTTERS & DOWNPIPES,            CLEANING GUTTER, SINGLE STOREY HEIGHT, SHORT (n/e 10.00m LONG)','(HS6805100)','nr',20.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (604,'PLU-0100-C','GUTTERS & DOWNPIPES,            CLEANING GUTTER, SINGLE STOREY HEIGHT, MEDIUM (10.00-20.00m LONG)','(HS6805105)','nr',40.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (605,'PLU-0150-C','GUTTERS & DOWNPIPES,            CLEANING GUTTER, SINGLE STOREY HEIGHT, LONG 20.00-30.00m LONG)','(HS6805110)','nr',60.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (606,'PLU-0200-C','GUTTERS & DOWNPIPES,             CLEANING GUTTER, TWO STOREY HEIGHT, SHORT (n/e 10.00m LONG)','(HS6805115)','nr',171.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (607,'PLU-0250-C','GUTTERS & DOWNPIPES,             CLEANING GUTTER, TWO STOREY HEIGHT, MEDIUM (10.00-20.00m LONG)','(HS6805120)','nr',334.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (608,'PLU-0300-C','GUTTERS & DOWNPIPES,             CLEANING GUTTER, TWO STOREY HEIGHT, LONG 20.00-30.00m LONG)','(HS6805125)','nr',498.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (609,'PLU-0345-C','GUTTERS & DOWNPIPES,          REALIGINING EXISTING GUTTER, n/e 5.00m','(HS6805128)','nr',86.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (610,'PLU-0350-C','GUTTERS & DOWNPIPES,         REALIGINING EXISTING GUTTER, 5.00 -10.00m','(HS6805130)','nr',216.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (611,'PLU-0400-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC GUTTER, SHORT (n/e 3.00m LONG))','(HS6805135)','nr',117.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (612,'PLU-0450-C','GUTTERS & DOWNPIPES,              RENEWING EXISTING UPVC GUTTER, LONG (exc. 3.00m LONG)','(HS6805140)','m',35.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (613,'PLU-0500-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC GUTTER FITTINGS, ANGLE','(HS6805145)','nr',30.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (614,'PLU-0550-C','GUTTERS & DOWNPIPES,          RENEWING EXISTING UPVC GUTTER FITTINGS, OUTLET','(HS6805150)','nr',24.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (615,'PLU-0600-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC GUTTER FITTINGS, STOPPED END OUTLET','(HS6805155)','nr',28.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (616,'PLU-0610-C','GUTTERS & DOWNPIPES,              RENEWING EXISTING UPVC GUTTER FITTINGS, STOPPED END','(HS6805157)','nr',21.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (617,'PLU-0650-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC RAINWATER DOWNPIPE COMPLETE, 1 STOREY','(HS6805160)','nr',176.86);
insert into repairs_rates (id, code, description, ref, unit, rate) values (618,'PLU-0700-C','GUTTERS & DOWNPIPES,          RENEWING EXISTING UPVC RAINWATER DOWNPIPE COMPLETE, 2 STOREYS','(HS6805165)','nr',255.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (619,'PLU-0800-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC RAINWATER DOWNPIPE, SHORT (n/e 1.00m LONG)','(HS6805170)','nr',24.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (620,'PLU-0900-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC RAINWATER FITTING; BEND','(HS6805175)','nr',21.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (621,'PLU-0950-C','GUTTERS & DOWNPIPES,            RENEWING EXISTING UPVC RAINWATER FITTING; SHOE','(HS6805180)','nr',20.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (622,'PLU-1000-C','GUTTERS & DOWNPIPES,          RENEWING EXISTING UPVC RAINWATER PIPEWORK FITTING; BRANCH','(HS6805185)','nr',32.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (623,'PLU-1050-C','GUTTERS & DOWNPIPES,           RENEWING EXISTING UPVC RAINWATER PIPEWORK FITTING; HOPPERHEAD','(HS6805190)','nr',49.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (624,'PLU-1100-C','GUTTERS & DOWNPIPES,          RENEWING EXISTING UPVC RAINWATER PIPEWORK FITTING; OFFSET','(HS6805195)','nr',25.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (625,'PLU-1150-C','GUTTERS & DOWNPIPES,        RESECURING LOOSE GUTTER/DOWNPIPE','(HS6805200)','nr',9.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (626,'PLU-1250-C','LEADWORK,                                INSTALLING LEAD SLATE PIECE FOR PIPE','(HS6810100)','nr',63.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (627,'PLU-1300-C','PIPEWORK EQUIPMENT & ANCILLARIES, OVERHAULING EXISTING VALVE, ANY TYPE','(HS6815100)','nr',28.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (628,'PLU-1350-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING BALL VALVE, SMALL (n/e 25mm)','(HS6815105)','nr',115.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (629,'PLU-1400-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING BALLOFIX VALVE, SMALL (n/e 25mm)','(HS6815110)','nr',53.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (630,'PLU-1450-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING BOTTLE TRAP, SMALL (n/e 32mm)','(HS6815115)','nr',33.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (631,'PLU-1500-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING BOTTLE TRAP, LARGE (40mm)','(HS6815120)','nr',36.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (632,'PLU-1550-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING COLD WATER STORAGE TANK, SMALL (n/e 125 LITRES)','(HS6815125)','nr',362.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (633,'PLU-1600-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING COLD WATER STORAGE TANK, LARGE (125-250 LITRES)','(HS6815130)','nr',442.46);
insert into repairs_rates (id, code, description, ref, unit, rate) values (634,'PLU-1650-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING CONE CONNECTOR TO WC','(HS6815135)','nr',20.18);
insert into repairs_rates (id, code, description, ref, unit, rate) values (635,'PLU-1700-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING DRAIN COCK, SMALL (n/e 25mm DIAMETER)','(HS6815140)','nr',48.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (636,'PLU-1750-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING HOT & COLD WATER COMBINATION TANK (n/e 125 LITRES, n/e 25 LITRES COLD)','(HS6815145)','nr',1024.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (637,'PLU-1800-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING HOT WATER CYLINDER','(HS6815150)','nr',639.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (638,'PLU-1850-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING P OR S TRAP, SMALL (32MM)','(HS6815155)','nr',29.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (639,'PLU-1900-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING P OR S TRAP, LARGE (40mm)','(HS6815160)','nr',32.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (640,'PLU-1950-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING STOPCOCK, SMALL (n/e 25mm)','(HS6815165)','nr',198.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (641,'PLU-2000-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING STOPCOCK, LARGE (28mm)','(HS6815170)','nr',316.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (642,'PLU-2050-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING UNDERGROUND STOPCOCK','(HS6815175)','nr',164.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (643,'PLU-2100-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING WHEEL GATE VALVE, SMALL (n/e 25mm)','(HS6815180)','nr',104.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (644,'PLU-2150-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING EXISTING WHEEL GATE VALVE, LARGE (28mm)','(HS6815185)','nr',166.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (645,'PLU-2200-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING INSULATION TO EXISTING COLD WATER STORAGE TANK','(HS6815190)','nr',51.55);
insert into repairs_rates (id, code, description, ref, unit, rate) values (646,'PLU-2250-C','PIPEWORK EQUIPMENT & ANCILLARIES, RENEWING INSULATION TO EXISTING HOT WATER CYLINDER','(HS6815195)','nr',52.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (647,'PLU-22300-C','REAPAIRS/BLOCKAGES,                      CLEAR BLOCKAGE FROM SANITARY APPLIANCE WASTE PIPEWORK','(HS6820100)','nr',23.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (648,'PLU-2350-C','REAPAIRS/BLOCKAGES,                CLEARING AIR LOCK IN IN WATER SYSTEM','(HS6820105)','nr',46.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (649,'PLU-2400-C','REAPAIRS/BLOCKAGES,                CLEARING BLOCKAGE FROM GUTTER/DOWNPIPE','(HS6820110)','nr',15.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (650,'PLU-2450-C','REAPAIRS/BLOCKAGES,                CLEARING BLOCKAGE FROM SOIL VENT STACK, 1 STOREY','(HS6820115)','nr',71.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (651,'PLU-2500-C','REAPAIRS/BLOCKAGES,               CLEARING BLOCKAGE FROM SOIL VENT STACK, 2  STOREYS','(HS6820120)','nr',143.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (652,'PLU-2600-C','REPAIRS/BLOCKAGES,                REPAIRING LEAK IN COLD WATER STORAGE TANK','(HS6820125)','nr',23.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (653,'PLU-2650-C','REPAIRS/BLOCKAGES,                REPAIRING LEAK IN HOT WATER CYLINDER','(HS6820130)','nr',23.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (654,'PLU-2700-C','REPAIRS/BLOCKAGES,                REPAIRING LEAK IN RAINWATER GUTTER/DOWNPIPE','(HS6820135)','nr',8.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (655,'PLU-2750-C','REPAIRS/BLOCKAGES,                 REPAIRING LEAK IN WASTE PIPEWORK','(HS6820140)','nr',17.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (656,'PLU-2800-C ','REPAIRS/BLOCKAGES,                 REPAIRING LEAK IN WATER PIPEWORK','(HS6820145)','nr',22.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (657,'PLU-2850-C','REAPAIRS/BLOCKAGES,                           RE-WASHERING BALL VALVE','(HS6820150)','nr',9.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (658,'PLU-2900-C','REAPAIRS/BLOCKAGES,                          RE-WASHERING TAP','(HS6820155)','nr',11.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (659,'PLU-2950-C','SANITARY APPLIANCES,                APPLYING PVC TRIM TO BATH','(HS6825100)','nr',90.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (660,'PLU-3000-C','SANITARY APPLIANCES,              RENEWING EXISTING STANDARD BATH','(HS6825105)','nr',699.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (661,'PLU-3010-C','SANITARY APPLIANCES,                      EXTRA OVER FOR APPLYING CLASSI-SEAL FLEXIBLE UPSTAND','(HS6825110)','nr',37.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (662,'PLU-3100-C','SANITARY APPLIANCES,        OVERHAULING SHOWER MIXER VALVE','(HS6825115)','nr',24.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (663,'PLU-3150-C','SANITARY APPLIANCES,             RENEWING EXISTING SHOWER ASSEMBLY','(HS6825120)','nr',140.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (664,'PLU-3200-C','SANITARY APPLIANCES,             RENEWING EXISTING SHOWER CURTAIN & RAIL','(HS6825125)','nr',101.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (665,'PLU-3250-C','SANITARY APPLIANCES,             RENEWING EXISTING SHOWER HOSE & SPRAY HEAD','(HS6825130)','nr',235.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (666,'PLU-3300-C','SANITARY APPLIANCES,              RENEWING EXISTING STANDARD SHOWER HEAD','(HS6825135)','nr',67.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (667,'PLU-3350-C','SANITARY APPLIANCES,              RENEWING EXISTING STANDARD SHOWER TRAY','(HS6825140)','nr',406.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (668,'PLU-3360-C','SANITARY APPLIANCES,                     EXTRA OVER FOR APPLYING CLASSI-SEAL FLEXIBLE UPSTAND','(HS6825145)','nr',29.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (669,'PLU-3400-C','SANITARY APPLIANCES,              RENEWING EXISTING THERMOSTATIC SHOWER COMPLETE','(HS6825150)','nr',701.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (670,'PLU-3450-C','SANITARY APPLIANCES,                      OVERHAULING WC CISTERN','(HS6825155)','nr',34.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (671,'PLU-3500-C','SANITARY APPLIANCES,               RENEWING EXISTING CHAIN TO HIGH LEVEL WC CISTERN','(HS6825160)','nr',15.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (672,'PLU-3550-C','SANITARY APPLIANCES,             RENEWING EXISTING CHAIN TO LOW LEVEL WC CISTERN','(HS6825165)','nr',62.37);
insert into repairs_rates (id, code, description, ref, unit, rate) values (673,'PLU-3600-C','SANITARY APPLIANCES,              RENEWING EXISTING WC CISTERN','(HS6825170)','nr',204.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (674,'PLU-3650-C','SANITARY APPLIANCES,              RENEWING EXISTING WC OVERFLOW','(HS6825175)','nr',43.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (675,'PLU-3700-C','SANITARY APPLIANCES,              RENEWING EXISTING WC PAN','(HS6825180)','nr',196.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (676,'PLU-3750-C','SANITARY APPLIANCES,              RENEWING EXISTING WC SEAT & COVER','(HS6825185)','nr',33.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (677,'PLU-3800-C','SANITARY APPLIANCES,           RESECURING EXISTING WC SEAT','(HS6825190)','nr',7.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (678,'PLU-3850-C','SANITARY APPLIANCES,           RESECURING WC CISTERN','(HS6825195)','nr',31.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (679,'PLU-3900-C','SANITARY APPLIANCES,             RESECURING WC PAN','(HS6825200)','nr',29.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (680,'PLU-3950-C','SANITARY APPLIANCES,              RENEWING EXISTING STANDARD WASH HAND BASIN ','(HS6825205)','nr',346.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (681,'PLU-4000-C','SANITARY APPLIANCES,              RENEWING EXISTING STANDARD WASH HAND BASIN WITH PEDESTAL','(HS6825210)','nr',370.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (682,'PLU-4050-C','SANITARY APPLIANCES,          RESECURING WASH HAND BASIN ','(HS6825215)','nr',33.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (683,'PLU-4100-C','SANITARY APPLIANCES,              RENEWING EXISTING STAINLESS STEEL SINK','(HS6825220)','nr',417.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (684,'PLU-4250-C','SANITARY APPLIANCES,                          RE-WASHERING TAP, ANY TYPE','(HS6825225)','nr',11.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (685,'PLU-4300-C','SANITARY APPLIANCES,             RENEWING EXISTING BATH MIXER TAP','(HS6825230)','nr',156.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (686,'PLU-4350-C','SANITARY APPLIANCES,             RENEWING EXISTING BATH TAP','(HS6825235)','nr',30.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (687,'PLU-4400-C','SANITARY APPLIANCES,             RENEWING EXISTING SINK MIXER TAP','(HS6825240)','nr',119.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (688,'PLU-4450-C','SANITARY APPLIANCES,              RENEWING EXISTING SINK TAP','(HS6825245)','nr',26.07);
insert into repairs_rates (id, code, description, ref, unit, rate) values (689,'PLU-4500-C','SANITARY APPLIANCES,              RENEWING EXISTING WASH HAND BASIN TAP','(HS6825250)','nr',26.07);
insert into repairs_rates (id, code, description, ref, unit, rate) values (690,'PLU-4550-C','SANITARY APPLIANCES,            RESECURING LOOSE TAP, ANY TYPE','(HS6825255)','nr',18.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (691,'PLU-4600-C','SANITARY APPLIANCES,              RENEWING SILICONE MASTIC AROUND BATH/SHOWER (n/e 4.00M)','(HS6825260)','nr',26.38);
insert into repairs_rates (id, code, description, ref, unit, rate) values (692,'PLU-4650-C','SANITARY APPLIANCES,              RENEWING SILICONE MASTIC AROUND WHB (n/e 1.00m)','(HS6825265)','nr',6.6);
insert into repairs_rates (id, code, description, ref, unit, rate) values (693,'PLU-4700-C','SOIL VENT STACKS,                     RENEWING EXISTING UPVC SOIUL VENT STACK COMPLETE, 1 STOREY, EXTERNALLY','(HS6830100)','nr',543.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (694,'PLU-4750-C','SOIL VENT STACKS,                     RENEWING EXISTING UPVC SOIUL VENT STACK COMPLETE, 2 STOREYS, EXTERNALLY','(HS6830105)','nr',932.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (695,'PLU-4900-C','WASHING MACHINES,               INSTALLING ALL PIPEWORK & FITTINGS FOR AUTOMATIC WASHING MACHINE','(HS6835100)','Item',16.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (696,'PLU-4950-C','WASTE PIPEWORK,                     RENEWING EXISTING PLASTIC WASTE PIPEWORK, SMALL BORE (n/e 32mm), SHORT (n/e 3.00m LONG)','(HS6837105)','nr',83.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (697,'PLU-5000-C','WASTE PIPEWORK,                      RENEWING EXISTING PLASTIC WASTE PIPEWORK, SMALL BORE (n/e 32mm), LONG (exc. 3.00mLONG)','(HS6837110)','m',27.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (698,'PLU-5050-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, ONE END (e.g. BLANK CAP); SMALL BORE (n/e 32mm)','(HS6837115)','nr',23.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (699,'PLU-5100-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, TWO ENDS (e.g. ELBOW); SMALL BORE (n/e 32mm)','(HS6837120)','nr',23.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (700,'PLU-5150-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, THREE ENDS (e.g. TEE); SMALL BORE (n/e 32mm)','(HS6837125)','nr',27.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (701,'PLU-5200-C','WASTE PIPEWORK,                     RENEWING EXISTING PLASTIC WASTE PIPEWORK, LARGE BORE (40mm), SHORT (n/e 3.00m LONG)','(HS6837130)','nr',89.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (702,'PLU-5250-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, LARGE BORE (40mm), LONG (exc. 3.00M LONG)','(HS6837135)','m',29.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (703,'PLU-5300-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, ONE END (e.g. BLANK CAP); LARGE BORE (40mm)','(HS6837140)','nr',23.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (704,'PLU-5350-C','WASTE PIPEWORK,                     RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, TWO ENDS (e.g. ELBOW); LARGE BORE (40mm)','(HS6837145)','nr',24.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (705,'PLU-5400-C','WASTE PIPEWORK,                    RENEWING EXISTING PLASTIC WASTE PIPEWORK, FITTING, THREE ENDS (e.g. TEE); LARGE BORE (40mm)','(HS6837150)','nr',28.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (706,'PLU-5450-C','WASTE PIPEWORK,                    RENEWING EXISTING UPVC SOIL VENT PIPEWORK, SHORT (n/e 3.00m LONG)','(HS6837155)','nr',273.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (707,'PLU-5500-C','WASTE PIPEWORK,                     RENEWING EXISTING UPVC SOIL VENT PIPEWORK, LONG (exc. 3.00m LONG)','(HS6837160)','nr',91.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (708,'PLU-5550-C','WASTE PIPEWORK,                     RENEWING EXISTING UPVC SOIL VENT PIPEWORK FITTING; ACCESS BEND','(HS6837165','nr',188.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (709,'PLU-5600-C','WASTE PIPEWORK,                    RENEWING EXISTING UPVC SOIL VENT PIPEWORK FITTING; ACCESS PIPE','(HS6837170)','nr',116.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (710,'PLU-5650-C','WASTE PIPEWORK,                    RENEWING EXISTING UPVC SOIL VENT PIPEWORK FITTING; BEND','(HS6837175)','nr',98.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (711,'PLU-5700-C','WASTE PIPEWORK,                     RENEWING EXISTING UPVC SOIL VENT PIPEWORK FITTING; BRANCH','(HS6837180)','nr',142.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (712,'PLU-5800-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK, SMALL BORE (n/e 15mm), SHORT (n/e 3.00m LONG)','(HS6840100)','nr',84.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (713,'PLU-5850-C','WATER PIPEWORK,                   RENEWING EXISTING COPPER PIPEWORK, SMALL BORE (n/e 15mm), LONG (exc. 3.00m LONG)','(HS6840105)','m',28.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (714,'PLU-5900-C','WATER PIPEWORK,                    RENEWING EXISTING INSULATION TO COPPER PIPEWORK, SMALL BORE (n/e 15mm), SHORT (n/e 3.00m LONG)','(HS6840110)','nr',14.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (715,'PLU-5950-C','WATER PIPEWORK,                    RENEWING EXISTING INSULATION TO COPPER PIPEWORK, SMALL BORE (n/e 15mm), LONG (exc. 3.00m LONG)','(HS6840115)','m',4.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (716,'PLU-6000-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, ONE END (e.g  BLANK CAP); SMALL BORE (n/e 15mm)','(HS6840120)','nr',9.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (717,'PLU-6050-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, TWO ENDS (e.g  ELBOW); SMALL BORE (n/e 15mm)','(HS6840125)','nr',7.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (718,'PLU-6100-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, THREE ENDS (e.g  TEE); SMALL BORE (n/e 15mm)','(HS6840130)','nr',10.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (719,'PLU-6150-C','WATER PIPEWORK,                   RENEWING EXISTING COPPER PIPEWORK, MEDIUM BORE (22mm), SHORT (n/e 3.00m LONG)','(HS6840135)','nr',100.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (720,'PLU-6200-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK, MEDIUM BORE (22mm), LONG (exc. 3.00m LONG)','(HS6840140)','m',33.63);
insert into repairs_rates (id, code, description, ref, unit, rate) values (721,'PLU-6250-C','WATER PIPEWORK,                    RENEWING EXISTING INSULATION TO COPPER PIPEWORK, MEDIUM BORE (22mm), SHORT (n/e 3.00m LONG)','(HS6840145)','nr',16.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (722,'PLU-6300-C','WATER PIPEWORK,                    RENEWING EXISTING INSULATION TO COPPER PIPEWORK, MEDIUM BORE (22mm), LONG (exc. 3.00M LONG)','(HS6840150)','m',5.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (723,'PLU-6350-C','WATER PIPEWORK,                     RENEWING EXISTING COPPER PIPEWORK FITTING, ONE END (e.g  BLANK CAP); MEDIUM BORE (22mm)','(HS6840155)','nr',14.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (724,'PLU-6400-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, TWO ENDS (e.g  ELBOW); MEDIUM BORE (22mm)','(HS6840160)','nr',10.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (725,'PLU-6450-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, THREE ENDS (e.g  TEE); MEDIUM BORE (22mm)','(HS6840165)','nr',16.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (726,'PLU-6500-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK, LARGE BORE (28mm), SHORT (n/e 3.00m LONG)','(HS6840170)','nr',108.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (727,'PLU-6550-C','WATER PIPEWORK,                     RENEWING EXISTING COPPER PIPEWORK, LARGE BORE (28mm), LONG (exc. 3.00m LONG)','(HS6840175)','m',36.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (728,'PLU-6600-C','WATER PIPEWORK,                    RENEWING EXISTING INSULATION TO COPPER PIPEWORK, LARGE BORE (28mm), SHORT (n/e 3.00m LONG)','(HS6840180)','nr',17.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (729,'PLU-6650-C','WATER PIPEWORK,                     RENEWING EXISTING INSULATION TO COPPER PIPEWORK, LARGE BORE (28mm), LONG (exc. 3.00m LONG)','(HS6840185)','m',5.99);
insert into repairs_rates (id, code, description, ref, unit, rate) values (730,'PLU-6700-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, ONE END (e.g  BLANK CAP); LARGE BORE (28mm)','(HS6840190)','nr',22.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (731,'PLU-6750-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, TWO ENDS (e.g  ELBOW); LARGE BORE (28mm)','(HS6840195)','nr',15.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (732,'PLU-6800-C','WATER PIPEWORK,                    RENEWING EXISTING COPPER PIPEWORK FITTING, THREE ENDS (e.g  TEE); LARGE BORE (28mm)','(HS6840200)','nr',29.48);
update repairs_rates set area='PLUMBING INSTALLATIONS' where id <= 732 and id > 602;

insert into repairs_rates (id, code, description, ref, unit, rate) values (733,'PAI-0050-C','ANTI-VANDAL PAINT,                  ANTI-VANDAL PAINT TO EXTERNAL PIPEWORK, 1 STOREY','(HS6005100)','nr',14.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (734,'PAI-0100-C','ANTI-VANDAL PAINT,                    ANTI-VANDAL PAINT TO EXTERNAL PIPEWORK, 2 STOREY','(HS6005105)','nr',28.09);
insert into repairs_rates (id, code, description, ref, unit, rate) values (735,'PAI-0150-C','ANTI-VANDAL PAINT,                   ANTI-VANDAL PAINT TO EXTERNAL PIPEWORK, 3 STOREY','(HS6005110)','nr',42.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (736,'PAI-0200-C','EMULSION PAINT,                                2 COATS EMULSION TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2)','(HS6010100)','nr',8.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (737,'PAI-0250-C','EMULSION PAINT,                                2 COATS EMULSION TO EXISTING SURFACES, LARGE AREAS (exc. 0.50m2)','(HS6010105)','m2',9.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (738,'PAI-0300-C','EMULSION PAINT,                          EXTRA OVER FOR ADDITIONAL COAT EMULSION TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2)','(HS6010110)','nr',2.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (739,'PAI-0350-C','EMULSION PAINT,                              EXTRA OVER FOR ADDITIONAL COAT EMULSION TO EXISTING SURFACES, LARGE AREAS (exc. 0.50m2)','(HS6010115)','m2',3.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (740,'PAI-0400-C','GLOSS PAINT,                           GLOSS PAINT TO EXISTING FENCES, RAILINGS & GATES, SMALL AREAS (n/e 0.50m2)','(HS6015100)','nr',20.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (741,'PAI-0450-C','GLOSS PAINT,                            GLOSS PAINT TO EXISTING FENCES, RAILINGS & GATES, LARGE AREAS (exc. 0.50m2)','(HS6015105)','m2',23.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (742,'PAI-0500-C','GLOSS PAINT,                               GLOSS PAINT TO EXISTING GUTTERS, SHORT (n/e 10.00m LONG)','(HS6015110)','nr',84.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (743,'PAI-0550-C','GLOSS PAINT,                              GLOSS PAINT TO EXISTING GUTTERS, MEDIUM (10.00-20.00m LONG)','(HS6015115)','nr',169.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (744,'PAI-0600-C','GLOSS PAINT,                                GLOSS PAINT TO EXISTING GUTTERS, LONG (20.00-30.00m LONG)','(HS6015120)','nr',254.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (745,'PAI-0650-C','GLOSS PAINT,                         GLOSS PAINT TO EXISTING SERVICE PIPES, SHORT (n/e 3.00m LONG)','(HS6015125)','nr',25.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (746,'PAI-0700-C','GLOSS PAINT,                              GLOSS PAINT TO EXISTING SERVICE PIPES, MEDIUM (3.00-6.00m LONG)','(HS6015130)','nr',51.9);
insert into repairs_rates (id, code, description, ref, unit, rate) values (747,'PAI-0750-C','GLOSS PAINT,                                  GLOSS PAINT TO EXISTING SERVICE PIPES, LONG (6.00-9.00m LONG)','(HS6015135)','nr',77.85);
insert into repairs_rates (id, code, description, ref, unit, rate) values (748,'PAI-0800-C','GLOSS PAINT,                         GLOSS PAINT TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2)','(HS6015140)','nr',16.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (749,'PAI-0850-C','GLOSS PAINT,                          GLOSS PAINT TO EXISTING SURFACES, LARGE AREAS (exc. 0.50m2)','(HS6015145)','m2',17.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (750,'PAI-0900-C','GLOSS PAINT,                           GLOSS PAINT TO EXISTING SURFACES, WINDOWS/SCREENS, SMALL PANES (n/e 0.10m2)','(HS6015150)','m2',34.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (751,'PAI-0950-C','GLOSS PAINT,                         GLOSS PAINT TO EXISTING SURFACES, WINDOWS/SCREENS, MEDIUM PANES (0.10-0.50m2)','(HS6015155)','m2',29.94);
insert into repairs_rates (id, code, description, ref, unit, rate) values (752,'PAI-1000-C','GLOSS PAINT,                           GLOSS PAINT TO EXISTING SURFACES, WINDOWS/SCREENS, LARGE PANES (0.50-1.00m2)','(HS6015160)','m2',24.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (753,'PAI-1050-C','GLOSS PAINT,                              GLOSS PAINT TO EXISTING SURFACES, WINDOWS/SCREENS, EXTRA LARGE PANES (exc. 1.00m2)','(HS6015165)','m2',19.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (754,'PAI-1300-C','GRAFFITI,                              REMOVING GRAFFITI, SMALL AREAS (n/e 1.00m2)','(HS6020100)','nr',15.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (755,'PAI-1350-C','GRAFFITI,                                     REMOVING GRAFFITI, LARGE AREAS (exc. 1.00m2)','(HS6020105)','m2',13.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (756,'PAI-1400-C','MASONRY PAINT,           MASONRY PAINT TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2) EXTERNALLY','(HS6025100)','nr',17.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (757,'PAI-1450-C','MASONRY PAINT,         MASONRY PAINT TO EXISTING SURFACES, LARGE AREAS (exc.50m2) EXTERNALLY','(HS6025105)','m2',16.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (758,'PAI-1500-C','REDECORATION COMPLETE, PAINT HALL/LOBBY AREA COMPLETE','(HS6030100)','nr',659.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (759,'PAI-1550-C','REDECORATION COMPLETE, PAINT HALL/LOBBY/STAIR AREA COMPLETE, 2 STOREY','(HS6030105)','nr',1642.73);
insert into repairs_rates (id, code, description, ref, unit, rate) values (760,'PAI-1600-C','REDECORATION COMPLETE, PAINT HALL/LOBBY/STAIR AREA COMPLETE, 3 STOREY','(HS6030110)','nr',2652.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (761,'PAI-1650-C','REDECORATION COMPLETE, PAINT ROOM COMPLETE, SMALL (n/e 5.00m2 FLOOR AREA)','(HS6030115)','nr',391.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (762,'PAI-1700-C','REDECORATION COMPLETE, PAINT ROOM COMPLETE, MEDIUM (5.00-10.00m2 FLOOR AREA)','(HS6030120)','nr',613.91);
insert into repairs_rates (id, code, description, ref, unit, rate) values (763,'PAI-1750-C','REDECORATION COMPLETE, PAINT ROOM COMPLETE, LARGE (10.00-15.00m2 FLOOR AREA)','(HS6030125)','nr',734.59);
insert into repairs_rates (id, code, description, ref, unit, rate) values (764,'PAI-1800-C','REDECORATION COMPLETE, PAINT ROOM COMPLETE, EXTRA LARGE (15.00-20.00m2 FLOOR AREA)','(HS6030130)','nr',862.49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (765,'PAI-1850-C','REDECORATION COMPLETE, PAINTING PROPERTY INTENALLY COMPLETE; 1 BEDROOM PROPERTY','(HS6030135)','nr',3262.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (766,'PAI-1900-C','REDECORATION COMPLETE, PAINTING PROPERTY INTENALLY COMPLETE; 2 BEDROOM PROPERTY','(HS6030140)','nr',4025.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (767,'PAI-2000-C','REDECORATION COMPLETE, PAINTING PROPERTY INTENALLY COMPLETE; 3 BEDROOM PROPERTY','(HS6030145)','nr',4971.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (768,'PAI-2100-C','REDECORATION COMPLETE, PAINTING PROPERTY INTENALLY COMPLETE; 4 BEDROOM PROPERTY','(HS6030150)','nr',6440.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (769,'PAI-2150-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2)','(HS6035100)','nr',3.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (770,'PAI-2200-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, LARGE AREAS (exc.0.50m2)','(HS6035105)','m2',4.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (771,'PAI-2250-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, SMALL PANES (n/e 0.10m2)','(HS6035110)','m2',8.90);
insert into repairs_rates (id, code, description, ref, unit, rate) values (772,'PAI-2300-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, MEDIUM PANES (0.10-0.50m2)','(HS6035115)','m2',7.90);
insert into repairs_rates (id, code, description, ref, unit, rate) values (773,'PAI-2350-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, LARGE PANES (0.50-1.00m2)','(HS6035120)','m2',5.58);
insert into repairs_rates (id, code, description, ref, unit, rate) values (774,'PAI-2400-C','STAIN/VARNISH (INTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, EXTRA LARGE PANES (exc. 1.00m2)','(HS6035125)','m2',4.60);
insert into repairs_rates (id, code, description, ref, unit, rate) values (775,'PAI-2410-C','STAIN/VARNISH (EXTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, SMALL AREAS (n/e 0.50m2)','(HS6036100)','nr',4.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (776,'PAI-2420-C','STAIN/VARNISH (EXTERNALLY) STAIN/VARNISH TO EXISTING SURFACES, LARGE AREAS (exc.0.50m2)','(HS6036105)','m2',4.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (777,'PAI-2430-C','STAIN/VARNISH (EXTERNALLY) STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, SMALL PANES (n/e 0.10m2)','(HS6036110)','m2',9.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (778,'PAI-2440-C','STAIN/VARNISH (EXTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, MEDIUM PANES (0.10-0.50m2)','(HS6036115)','m2',8.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (779,'PAI-2450-C','STAIN/VARNISH (EXTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, LARGE PANES (0.50-1.00m2)','(HS6036120)','m2',6.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (780,'PAI-2460-C','STAIN/VARNISH (EXTERNALLY), STAIN/VARNISH TO EXISTING SURFACES, WINDOWS/SCREENS, EXTRA LARGE PANES (exc. 1.00m2)','(HS6036125','m2',5.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (781,'PAI-2600-C','WALLPAPER,                                  RENEWING WOODCHIP WALLPAPER TO WALLS & CEILINGS OF ROOM COMPLETE, SMALL (n/e 5.00m2 FLOOR AREA)','(HS6040100)','nr',409.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (782,'PAI-2650-C','WALLPAPER,                                RENEWING WOODCHIP WALLPAPER TO WALLS & CEILINGS OF ROOM COMPLETE, MEDIUM (5.00-10.00m2 FLOOR AREA)','(HS6040105)','nr',857.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (783,'PAI-2700-C','WALLPAPER,                 RENEWING WOODCHIP WALLPAPER TO WALLS & CEILINGS OF ROOM COMPLETE, LARGE (10.00-15.00m2 FLOOR AREA)','(HS6040110)','nr',1123.64);
insert into repairs_rates (id, code, description, ref, unit, rate) values (784,'PAI-2750-C','WALLPAPER,                 RENEWING WOODCHIP WALLPAPER TO WALLS & CEILINGS OF ROOM COMPLETE, EXTRA LARGE (15.00-20.00m2 FLOOR AREA)','(HS6040115)','nr',1303.48);
update repairs_rates set area='PAINTING & DECORATING' where id <= 784 and id > 732;

insert into repairs_rates (id, code, description, ref, unit, rate) values (785,'REN-0050-C','ARTEX,               RENEWING EXISTING ARTEX FINISH TO CEILINGS, SMALL AREAS (n/e 2.00m2)','(HS6405100)','nr',77.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (786,'REN-0100-C','ARTEX,             RENEWING EXISTING ARTEX FINISH TO CEILINGS, LARGE AREAS (exc. 2.00m2)','(HS6405105)','m2',38.71);
insert into repairs_rates (id, code, description, ref, unit, rate) values (787,'REN-0150-C','CHIMNEY STACKS,                  RENEWING EXISTING DRY DASH RENDER TO CHIMNEY STACK, SMALL (n/e 0.25m2 PLAN AREA)','(HS6405100)','nr',462.89);
insert into repairs_rates (id, code, description, ref, unit, rate) values (788,'REN-0200-C','CHIMNEY STACKS, RENEWING EXISTING DRY DASH RENDER TO CHIMNEY STACK, MEDIUM (0.25-0.50m2 PLAN AREA)','(HS6405105)','nr',546.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (789,'REN-0250-C','CHIMNEY STACKS, RENEWING EXISTING DRY DASH RENDER TO CHIMNEY STACK, LARGE (0.50-0.75m2 PLAN AREA)','(HS6405110)','nr',744.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (790,'REN-0300-C','CHIMNEY STACKS, RENEWING EXISTING DRY DASH RENDER TO CHIMNEY STACK, EXTRA LARGE (0.75-1.00m2 PLAN AREA)','(HS6405115)','nr',805.27);
insert into repairs_rates (id, code, description, ref, unit, rate) values (791,'REN-0400-C','PLASTERWORK, OVERLAYING EXISTING CEILING WITH PLASTERBOARD & SKIM COAT FINISH, SMALL AREAS (n/e 3.00m2)','(HS6415100)','nr',77.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (792,'REN-0450-C','PLASTERWORK, OVERLAYING EXISTING CEILING WITH PLASTERBOARD & SKIM COAT FINISH, LARGE AREAS (exc.3.00m2)','(HS6415105)','m2',21.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (793,'REN-0500-C','PLASTERWORK, RENEWING EXISTING FINISH WITH DOUBLE LAYER OF PLASTERBOARD & SKIM COAT TO CEILINGS, SMALL AREAS (n/e 3.00m2)','(HS6415110)','nr',168.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (794,'REN-0550-C','PLASTERWORK, RENEWING EXISTING FINISH WITH DOUBLE LAYER OF PLASTERBOARD & SKIM COAT TO CEILINGS, LARGE AREAS (exc.3.00m2)','(HS6415115)','m2',48.81);
insert into repairs_rates (id, code, description, ref, unit, rate) values (795,'REN-0600-C','PLASTERWORK, RENEWING EXISTING CEILING WITH PLASTER TO CEILINGS, SMALL AREAS (n/e 3.00m2)','(HS6415120)','nr',146.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (796,'REN-0650-C','PLASTERWORK, RENEWING EXISTING CEILING WITH PLASTER TO CEILINGS, LARGE AREAS (exc. 3.00m2)','(HS6415125)','m2',45.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (797,'REN-0700-C','PLASTERWORK, RENEWING EXISTING FINISH WITH PLASTERBOARD & SKIM COAT TO CEILINGS, SMALL AREAS (n/e 3.00m2)','(HS6415130)','nr',122.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (798,'REN-0750-C','PLASTERWORK, RENEWING EXISTING FINISH WITH PLASTERBOARD & SKIM COAT TO CEILINGS, LARGE AREAS (exc.3.00m2)','(HS6415135)','m2',34.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (799,'REN-0800-C','PLASTERWORK, RENEWING EXISTING FINISH WITH PLASTERBOARD & SKIM COAT TO WALLS, SMALL AREAS (n/e 3.00m2)','(HS6415140)','nr',109.77);
insert into repairs_rates (id, code, description, ref, unit, rate) values (800,'REN-0850-C','PLASTERWORK, RENEWING EXISTING FINISH WITH PLASTERBOARD & SKIM COAT TO WALLS, LARGE AREAS (exc.3.00m2)','(HS6415145)','m2',31.14);
insert into repairs_rates (id, code, description, ref, unit, rate) values (801,'REN-0900-C','PLASTERWORK, RENEWING EXISTING FINISH WITH SKIM COAT PLASTER TO CEILINGS, SMALL AREAS (n/e 3.00m2)','(HS6415150)','nr',76.96);
insert into repairs_rates (id, code, description, ref, unit, rate) values (802,'REN-0950-C','PLASTERWORK, RENEWING EXISTING FINISH WITH SKIM COAT PLASTER TO CEILINGS, LARGE AREAS (exc. 3.00m2)','(HS6415155)','m2',20.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (803,'REN-1000-C','PLASTERWORK, RENEWING EXISTING FINISH WITH SKIM COAT PLASTER TO WALLS, SMALL AREAS (n/e 3.00m2)','(HS6415160)','nr',73.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (804,'REN-1050-C','PLASTERWORK, RENEWING EXISTING FINISH WITH SKIM COAT PLASTER TO WALLS, LARGE AREAS (exc. 3.00m2)','(HS6415165)','m2',19.05);
insert into repairs_rates (id, code, description, ref, unit, rate) values (805,'REN-1100-C','PLASTERWORK, RENEWING EXISTING LATH & PLASTER TO WALLS, SMALL AREAS (n/e 3.00m2)','(HS6415170)','nr',262.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (806,'REN-1150-C','PLASTERWORK, RENEWING EXISTING LATH & PLASTER TO WALLS, LARGE AREAS (exc. 3.00m2)','(HS6415175)','m2',74.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (807,'REN-1200-C','PLASTERWORK, RENEWING EXISTING METAL BEADS, n/e 3.00m2','(HS6415180)','nr',24.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (808,'REN-1250-C','PLASTERWORK, RENEWING EXISTING PLASTER COVING, SHORT (n/e 3.00m2)','(HS6415185)','nr',67.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (809,'REN-1300-C','PLASTERWORK, RENEWING EXISTING PLASTER COVING, MEDIUM  (3.00-6.00m2)','(HS6415190)','nr',203.1);
insert into repairs_rates (id, code, description, ref, unit, rate) values (810,'REN-1350-C','PLASTERWORK, RENEWING EXISTING PLASTER COVING, LONG (6.00-9.00m2)','(HS6415195)','nr',304.65);
insert into repairs_rates (id, code, description, ref, unit, rate) values (811,'REN-1400-C','PLASTERWORK, RENEWING EXISTING PLASTER REVEALS n/e 300, SHORT (n/e 3.00m GIRTH)','(HS6415200)','nr',70.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (812,'REN-1450-C','PLASTERWORK, RENEWING EXISTING PLASTER REVEALS n/e 300, MEDIUM (3.00-6.00m GIRTH)','(HS6415205)','nr',141.75);
insert into repairs_rates (id, code, description, ref, unit, rate) values (813,'REN-1500-C','PLASTERWORK, RENEWING EXISTING PLASTER REVEALS n/e 300, LONG (6.00-9.00m GIRTH)','(HS6415210)','nr',212.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (814,'REN-1550-C','PLASTERWORK, RENEWING EXISTING PLASTER TO WALLS, SMALL AREAS (n/e 1.00m2)','(HS6415215)','nr',93.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (815,'REN-1600-C','PLASTERWORK, RENEWING EXISTING PLASTER TO WALLS, LARGE AREAS (exc.1.00m2)','(HS6415220)','m2',53.02);
insert into repairs_rates (id, code, description, ref, unit, rate) values (816,'REN-1650-C','PLASTERWORK, REPAIRING CRACK IN PLASTER, n/e 3.00m LONG','(HS6415225)','nr',32.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (817,'REN-1700-C','RENDERING, APPLYING SAND CEMENT RENDER TO PARTY WALLS IN LOFT SPACE, SMALL (n/e 10.00m2)','(HS6420100)','nr',183.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (818,'REN-1750-C','RENDERING, APPLYING SAND CEMENT RENDER TO PARTY WALLS IN LOFT SPACE, MEDIUM (10.00-20.00m2)','(HS6420105)','nr',367.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (819,'REN-1800-C','RENDERING, APPLYING SAND CEMENT RENDER TO PARTY WALLS IN LOFT SPACE, LARGE (20.00-30.00m2)','(HS6420110)','nr',551.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (820,'REN-1850-C','RENDERING, RENEWING EXISTING DRY DASH RENDER REVEALS n/e 300, SHORT (n/e 3.00m GIRTH)','(HS6420115)','nr',65.04);
insert into repairs_rates (id, code, description, ref, unit, rate) values (821,'REN-1900-C','RENDERING, RENEWING EXISTING DRY DASH RENDER REVEALS n/e 300, MEDIUM (3.00-6.00m GIRTH)','(HS6420120)','nr',130.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (822,'REN-1950-C','RENDERING, RENEWING EXISTING DRY DASH RENDER REVEALS n/e 300, LONG (6.00-9.00m GIRTH)','(HS6420125)','nr',195.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (823,'REN-2000-C','RENDERING, RENEWING EXISTING DRY DASH RENDER, SMALL AREAS (n/e 1.00m2)','(HS6420130)','nr',93.66);
insert into repairs_rates (id, code, description, ref, unit, rate) values (824,'REN-2050-C','RENDERING, RENEWING EXISTING DRY DASH RENDER, LARGE AREAS (exc.1.00m2)','(HS6420135)','m2',49);
insert into repairs_rates (id, code, description, ref, unit, rate) values (825,'REN-2100-C','RENDERING, REPAIRING CRACK IN RENDER, n/e 3.00m LONG','(HS6420140)','nr',72.47);
update repairs_rates set area='RENDERING & PLASTER WORK' where id <= 825 and id > 784;

insert into repairs_rates (id, code, description, ref, unit, rate) values (826,'ROO-0050-C','ASPHALT COVERINGS, RENEWING EXISTING ASPHALT ROOFING, SMALL AREAS (n/e 1.00m2)','(HS7405100)','nr',232.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (827,'ROO-0100-C','ASPHALT COVERINGS, RENEWING EXISTING ASPHALT ROOFING, LARGE AREAS (exc. 1.00m2)','(HS7405105)','m2',183.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (828,'ROO-0150-C','ASPHALT COVERINGS, RENEWING EXISTING ASPHALT SKIRTINGS n/e 225 GIRTH','(HS7405110)','m',60.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (829,'ROO-0200-C','ASPHALT COVERINGS, REPAIRING CRACKS IN EXISTING ASPHALT ROOFING, SHORT (n/e 3.00m LONG)','(HS7405115)','nr',98.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (830,'ROO-0250-C','ASPHALT COVERINGS, REPAIRING CRACKS IN EXISTING ASPHALT ROOFING, LONG (exc. 3.00m LONG)','(HS7405120)','m',32.78);
insert into repairs_rates (id, code, description, ref, unit, rate) values (831,'ROO-0300-C','CORRUGATED SHEETING, RENEWING EXISTING CORRUGATED STEEL ROOF SHEETING','(HS7410100)','m2',43.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (832,'ROO-0350-C','CORRUGATED SHEETING, INSTALLING EAVES FILLER PIECES IN CORRUGATED STEEL ROOF SHEETING','(HS7410105)','m',8.42);
insert into repairs_rates (id, code, description, ref, unit, rate) values (833,'ROO-0400-C','CORRUGATED SHEETING, RENEWING EXISTING RIDGE TO CORRUGATED STEEL ROOF SHEETING','(HS7410110)','m',25.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (834,'ROO-0450-C','FELT COVERINGS, RENEWING EXISTING 3 LAYER FELT COVERINGS, SMALL AREAS (n/e 1.00m2)','(HS7415100)','nr',66.11);
insert into repairs_rates (id, code, description, ref, unit, rate) values (835,'ROO-0500-C','FELT COVERINGS, RENEWING EXISTING 3 LAYER FELT COVERINGS, LARGE AREAS (exc.1.00m2)','(HS7415105)','m2',61.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (836,'ROO-0550-C','FELT COVERINGS, RENEWING EXISTING 3 LAYER FELT COVERINGS, EAVES n/e 200 GIRTH','(HS7415110)','m',23.84);
insert into repairs_rates (id, code, description, ref, unit, rate) values (837,'ROO-0600-C','FELT COVERINGS, RENEWING EXISTING 3 LAYER FELT COVERINGS, SKIRTINGS n/e 200 GIRTH','(HS7415115)','m',38.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (838,'ROO-0700-C','FELT COVERINGS, SINGLE LAYER REPAIRS TO EXISTING FELT COVERINGS, SMALL AREAS (n/e 1.00m2)','(HS7415120)','nr',31.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (839,'ROO-0750-C','FELT COVERINGS, SINGLE LAYER REPAIRS TO EXISTING FELT COVERINGS, LARGE AREAS (exc.1.00n2)','(HS7415125)','m2',24.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (840,'ROO-0800-C','FELT COVERINGS, SINGLE LAYER REPAIRS TO EXISTING FELT COVERINGS, EAVES n/e 200 GIRTH','(HS7415130)','m',20.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (841,'ROO-0850-C','FELT COVERINGS, SINGLE LAYER REPAIRS TO EXISTING FELT COVERINGS, SKIRTINGS n/e 200 GIRTH','(HS7410135)','m',18.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (842,'ROO-0950-C','FIBRE CEMENT SHEETING, RENEWING EXISTING FIBRE CEMENT SHEETING, SMALL AREAS (n/e 1.00m2)','(HS7420100)','nr',53.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (843,'ROO-1000-C','FIBRE CEMENT SHEETING, RENEWING EXISTING FIBRE CEMENT SHEETING, LARGE AREAS (exc.1.00m2)','(HS7420105)','m2',47.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (844,'ROO-1050-C','FIBRE CEMENT SHEETING, RENEWING EXISTING FIBRE CEMENT SHEETING, BARGE BOARDS','(HS7420110)','m',35.1);
insert into repairs_rates (id, code, description, ref, unit, rate) values (845,'ROO-1100-C','FIBRE CEMENT SHEETING, RENEWING EXISTING FIBRE CEMENT SHEETING, RIDGES','(HS7420115)','m',28.93);
insert into repairs_rates (id, code, description, ref, unit, rate) values (846,'ROO-1150-C','LEADWORK,          RENEWING EXISTING LEAD ROOF COVERINGS','(HS7425100)','m2',184.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (847,'ROO-1200-C','LEADWORK,          RENEWING EXISTING LEAD FLASHINGS, HORIZONTAL/SLOPING (n/e 300 GIRTH)','(HS7425105)','m',50.32);
insert into repairs_rates (id, code, description, ref, unit, rate) values (848,'ROO-1250-C','LEADWORK,           RENEWING EXISTING LEAD FLASHINGS, STEPPED (n/e 300 GIRTH)','(HS7425110)','m',75.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (849,'ROO-1300-C','LEADWORK,          RENEWING EXISTING LEAD VALLEYS/GUTTERS (n/e 450 GIRTH)','(HS7425115)','m',73.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (850,'ROO-1400-C','ROOF BOARDING, RENEWING EXISTING TIMBER ROOF BOARDING','(HS7430100)','m2',53.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (851,'ROO-1500-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY RIDGE, SHORT (n/e 5.00m LONG)','(HS7432105)','nr',243.98);
insert into repairs_rates (id, code, description, ref, unit, rate) values (852,'ROO-1550-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY RIDGE, MEDIUM (5.00-10.00m LONG)','(HS7432110)','nr',487.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (853,'ROO-1600-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY RIDGE, LONG (10.00-15.00m LONG)','(HS7432115)','nr',731.95);
insert into repairs_rates (id, code, description, ref, unit, rate) values (854,'ROO-1650-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY VERGE, SHORT (n/e 5.00m LONG)','(HS7432120)','nr',387.08);
insert into repairs_rates (id, code, description, ref, unit, rate) values (855,'ROO-1700-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY VERGE, MEDIUM (5.00-10.00m LONG)','(HS7432125)','nr',774.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (856,'ROO-1750-C','ROOFING ACCESSORIES, RENEWING EXISTING DRY VERGE, LONG (10.00-15.00m LONG)','(HS7432130)','nr',1161.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (857,'ROO-1800-C','ROOFING ACCESSORIES, INSERTING NEW EAVES/SOFFIT VENTS','(HS7432135)','nr',4.72);
insert into repairs_rates (id, code, description, ref, unit, rate) values (858,'ROO-1850-C','ROOFING ACCESSORIES, RENEWING EXISTING GALVANISED STEEL HIP IRON','(HS7432140)','nr',9.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (859,'ROO-1900-C','ROOFING ACCESSORIES, RENEWING EXISTING RIDGE GAS FLUE TERMINAL','(HS7432145)','nr',158.91);
insert into repairs_rates (id, code, description, ref, unit, rate) values (860,'ROO-1950-C','ROOFING ACCESSORIES, RENEWING EXISTING VENT RIDGE/HIP TILE','(HS7432150)','nr',134.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (861,'ROO-2000-C','ROOFING ACCESSORIES, RENEWING EXISTING VENT ROOF TILE','(HS7432155)','nr',171.33);
insert into repairs_rates (id, code, description, ref, unit, rate) values (862,'ROO-2050-C','ROOFING REPAIRS, LOCATING & REPAIRING LEAK IN EXISTING ROOF, ANY TYPE','(HS7434160)','nr',80.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (863,'ROO-2100-C','ROOFING REPAIRS, RESECURING EXISTING LOOSE VERGE TILES','(HS7434165)','m',9.92);
insert into repairs_rates (id, code, description, ref, unit, rate) values (864,'ROO-2150-C','ROOFING REPAIRS, RESECURING EXISTING LOOSE HIP/RIDGE TILES','(HS7434170)','m',52.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (865,'ROO-2200-C','ROOFING REPAIRS, RESECURING EXISTING LOOSE LEAD FLASHINGS','(HS7434175)','m',20.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (866,'ROO-2250-C','ROOFING REPAIRS, RESECURING EXISTING LOOSE SLATES','(HS7434180)','nr',13.69);
insert into repairs_rates (id, code, description, ref, unit, rate) values (867,'ROO-2300-C','ROOFING REPAIRS, RESECURING EXISTING LOOSE TILES','(HS7434185)','nr',8.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (868,'ROO-2350-C','ROOFING REPAIRS, TEMPORARY REPAIR/WEATHERPROOFING TO EXISING PITCHED/FLAT ROOF','(HS7434190)','m2',11.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (869,'ROO-2400-C','SLATES,                 RENEWING EXISTING SINGLE SLATES','(HS7435100)','nr',20.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (870,'ROO-2450-C','SLATES,                  RENEWING EXISTING SLATES, SMALL AREAS (n/e 1.00m2)','(HS7435105)','nr',137.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (871,'ROO-2500-C','SLATES, RENEWING EXISTING SLATES, LARGE AREAS (exc.1.00m2)','(HS7435110)','m2',112.33);
insert into repairs_rates (id, code, description, ref, unit, rate) values (872,'ROO-2550-C','SOLAR REFLECTIVE PAINT, APPLYING SOLAR REFLECTIVE PAINT TO ROOF SURFACES, SMALL AREAS (n/e 5.00m2)','(HS7440100)','nr',50.07);
insert into repairs_rates (id, code, description, ref, unit, rate) values (873,'ROO-2560-C','SOLAR REFLECTIVE PAINT, APPLYING SOLAR REFLECTIVE PAINT TO ROOF SURFACES, LARGE AREAS (exc.5.00m2)','(HS7440105)','m2',10.01);
insert into repairs_rates (id, code, description, ref, unit, rate) values (874,'ROO-2570-C','TILES,                    RENEWING EXISTING SINGLE CONCRETE TILES','(HS7445100)','nr',16.03);
insert into repairs_rates (id, code, description, ref, unit, rate) values (875,'ROO-2580-C','TILES,                    RENEWING EXISTING SINGLE CONCRETE HIP/RIDGE TILES','(HS7445105)','nr',35.5);
insert into repairs_rates (id, code, description, ref, unit, rate) values (876,'ROO-2590-C','TILES,                     RENEWING EXISTING SINGLE CONCRETE VERGE TILES','(HS7445110)','nr',28.61);
insert into repairs_rates (id, code, description, ref, unit, rate) values (877,'ROO-2600-C','TILES,                     RENEWING EXISTING CONCRETE TILES, SMALL AREAS (n/e 1.00m2)','(HS7445115)','nr',405.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (878,'ROO-2650-C','TILES,                     RENEWING EXISTING CONCRETE TILES, LARGE AREAS (exc.1.00m2)','(HS7445120)','m2',81.83);
insert into repairs_rates (id, code, description, ref, unit, rate) values (879,'ROO-2700-C','TILES,                                 RENEWING EXISTING CONCRETE HIP/RIDGE TILES, SHORT (n/e 5.00m LONG)','(HS7445125)','nr',378.17);
insert into repairs_rates (id, code, description, ref, unit, rate) values (880,'ROO-2750-C','TILES,                    RENEWING EXISTING CONCRETE HIP/RIDGE TILES, MEDIUM (5.00-10.00m LONG)','(HS7445130)','nr',756.34);
insert into repairs_rates (id, code, description, ref, unit, rate) values (881,'ROO-2800-C','TILES,                    RENEWING EXISTING CONCRETE HIP/RIDGE TILES, LONG (10.00-15.00m LONG)','(HS7445135)','nr',1134.51);
insert into repairs_rates (id, code, description, ref, unit, rate) values (882,'ROO-2850-C','TILES,                    RENEWING EXISTING CONCRETE VERGE TILES, SHORT (n/e 5.00m LONG)','(HS7445140)','nr',86.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (883,'ROO-2900-C','TILES,                    RENEWING EXISTING CONCRETE VERGE TILES, MEDIUM (5.00-10.00M LONG)','(HS7445145)','nr',173.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (884,'ROO-2950-C','TILES,                    RENEWING EXISTING CONCRETE VERGE TILES, LONG (10.00-15.00m LONG)','(HS7445150)','nr',259.68);
insert into repairs_rates (id, code, description, ref, unit, rate) values (885,'ROO-3350-C','TILES,                  REPOINTING EXISTING EDGE TILES, ANY TYPE, SHORT (n/e 5.00m LONG)','(HS7445155)','nr',49.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (886,'ROO-3400-C','TILES,                  REPOINTING EXISTING EDGE TILES, ANY TYPE, MEDIUM (5.00.10.00m LONG)','(HS7445160)','nr',98.3);
insert into repairs_rates (id, code, description, ref, unit, rate) values (887,'ROO-3450-C','TILES,                  REPOINTING EXISTING EDGE TILES, ANY TYPE, LONG (10.00-15.00m LONG)','(HS7445165)','nr',147.45);
update repairs_rates set area='ROOFING' where id <= 887 and id > 825;

insert into repairs_rates (id, code, description, ref, unit, rate) values (	888	,'	SCA-0050-C	','	ERECT & DISMANTLE SCAFFOLD, INDEPENDENT TIED SCAFFOLD TO SINGLE STOREY DWELLING. (PER ELEVATION)	','	(HS0510100)	','	each	',	274.27	);
insert into repairs_rates (id, code, description, ref, unit, rate) values (	889	,'	SCA-0105-C	','	ERECT & DISMANTLE SCAFFOLD, INDEPENDENT TIED SCAFFOLD TO TWO STOREY DWELLING. (PER ELEVATION)	','	(HS0510105)	','	each	',	419.09	);
insert into repairs_rates (id, code, description, ref, unit, rate) values (	890	,'	SCA-0115-C	','	ERECT & DISMANTLE SCAFFOLD, INDEPENDENT TIED SCAFFOLD TO THREE STOREY DWELLING. (PER ELEVATION)	','	(HS0510115)	','	each	',	708.72	);
update repairs_rates set area='TEMPORARY WORKS' where id <= 890 and id > 887;

insert into repairs_rates (id, code, description, ref, unit, rate) values (891,'STO-0050-C','CAST STONE,         RENEWING EXISTING CAST STONE WALL, SMALL AREAS (n/e 1.00m2), 100 THICK','(HS8405100)','nr',315.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (892,'STO-0100-C','CAST STONE,         RENEWING EXISTING CAST STONE WALL, LARGE AREAS (exc. 1.00m2), 100 THICK','(HS8405105)','m2',292.26);
insert into repairs_rates (id, code, description, ref, unit, rate) values (893,'STO-0150-C','NATURAL STONE RUBBLE, RENEWING EXISTING STONE RUBBLE WALL, SMALL AREAS (n/e 1.00m2), n/e 300 THICK','(HS8410100)','nr',441.43);
insert into repairs_rates (id, code, description, ref, unit, rate) values (894,'STO-0200-C','NATURAL STONE RUBBLE, RENEWING EXISTING STONE RUBBLE WALL, LARGE AREAS (exc.1.00m2), n/e 300 THICK','(HS8410105)','m2',418.26);
update repairs_rates set area='STONEWORK' where id <= 894 and id > 890;

insert into repairs_rates (id, code, description, ref, unit, rate) values (895,'VOI-0050-C','APPLIANCES,             DISCONNECTING & REMOVING ELECTRIC COOKER','(HS8805100)','nr',5.8);
insert into repairs_rates (id, code, description, ref, unit, rate) values (896,'VOI-0100-C','APPLIANCES,             DISCONNECTING & REMOVING GAS APPLIANCE','(HS8805105)','nr',18.29);
insert into repairs_rates (id, code, description, ref, unit, rate) values (897,'VOI-0150-C','CLEARING RUBBISH & CLEANING, CLEARING ALL DEBRIS FROM GARAGE','(HS8810100)','m3',67.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (898,'VOI-0200-C','CLEARING RUBBISH & CLEANING, CLEARING ALL DEBRIS FROM GARDEN; NORMAL','(HS8810105)','nr',64.19);
insert into repairs_rates (id, code, description, ref, unit, rate) values (899,'VOI-0250-C','CLEARING RUBBISH & CLEANING, CLEARING ALL DEBRIS FROM GARDEN; ABNORMAL','(HS8810110)','nr',128.39);
insert into repairs_rates (id, code, description, ref, unit, rate) values (900,'VOI-0300-C','CLEARING RUBBISH & CLEANING, CLEARING ALL DEBRIS FROM SHED','(HS8810115)','m3',67.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (901,'VOI-0350-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; FLAT/BUNGALOW; NORMAL DIRT LEVEL','(HS8810120)','nr',120.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (902,'VOI-0400-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; FLAT/BUNGALOW; ABNORMAL DIRT LEVEL','(HS8810125)','nr',240.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (903,'VOI-0450-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; FLAT/BUNGALOW; EXCEPTIONALLY ABNORMAL DIRT LEVEL','(HS8810130)','nr',360.36);
insert into repairs_rates (id, code, description, ref, unit, rate) values (904,'VOI-0500-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; HOUSE/MAISONETTE; NORMAL DIRT LEVEL','(HS8810135)','nr',160.16);
insert into repairs_rates (id, code, description, ref, unit, rate) values (905,'VOI-0550-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; HOUSE/MAISONETTE; ABNORMAL DIRT LEVEL','(HS8810140)','nr',280.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (906,'VOI-0600-C','CLEARING RUBBISH & CLEANING, CLEANING DOWN COMPLETE; HOUSE/MAISONETTE; EXCEPTIONALLY ABNORMAL DIRT LEVEL','(HS8810145)','nr',440.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (907,'VOI-0650-C','CLEARING RUBBISH & CLEANING, CLEARING OUT COMPLETE; FLAT/BUNGALOW','(HS8810150)','m3',77.54);
insert into repairs_rates (id, code, description, ref, unit, rate) values (908,'VOI-0700-C','CLEARING RUBBISH & CLEANING, CLEARING OUT COMPLETE; HOUSE/MAISONETTE','(HS8810155)','m3',84.21);
insert into repairs_rates (id, code, description, ref, unit, rate) values (909,'VOI-0750-C','CLEARING RUBBISH & CLEANING, CLEARING OUT COMPLETE; ROOF SPACE','(HS8810160)','m3',80.88);
insert into repairs_rates (id, code, description, ref, unit, rate) values (910,'VOI-0800-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, SHORT (n/e 100 HIGH), SMALL (n/e 25.00m2)','(HS8815100)','nr',15.53);
insert into repairs_rates (id, code, description, ref, unit, rate) values (911,'VOI-0850-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, SHORT (n/e 100 HIGH), MEDIUM (25.00-50.00m2)','(HS8815105)','nr',24.4);
insert into repairs_rates (id, code, description, ref, unit, rate) values (912,'VOI-0900-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, SHORT (n/e 100 HIGH), LARGE (exc.50.00m2)','(HS8815110)','m2',0.44);
insert into repairs_rates (id, code, description, ref, unit, rate) values (913,'VOI-0950-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, LONG (exc 100 HIGH), SMALL (n/e 25.00m2)','(HS8815115)','nr',19.97);
insert into repairs_rates (id, code, description, ref, unit, rate) values (914,'VOI-1000-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, LONG (exc. 100 HIGH), MEDIUM (25.00-50.00m2)','(HS8815120)','nr',31.06);
insert into repairs_rates (id, code, description, ref, unit, rate) values (915,'VOI-1050-C','GRASS CUTTING,                 CUTTING GRASS IN GARDEN, LONG (exc. 100 HIGH), LARGE (exc.50.00M2)','(HS8815125)','m2',0.67);
insert into repairs_rates (id, code, description, ref, unit, rate) values (916,'VOI-1150-C','PLUMBING & HEATING, DRAINING DOWN HOT & COLD WATER SYSTEM, REFILLING AT LATER DATE (PER PROPERTY)','(HS8820100)','nr',118.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (917,'VOI-1200-C','PLUMBING & HEATING, DRAINING DOWN CENTRAL HEATING SYSTEM, REFILLING AT LATER DATE (PER PROPERTY)','(HS8825105)','nr',46.52);
insert into repairs_rates (id, code, description, ref, unit, rate) values (918,'VOI-1220-C','PLUMBING & HEATING,        TESTING & COMMISSIONING HOT & COLD WATER SYSTEM, (PER PROPERTY) (N.B WHERE SPECIFICALLY INSTRUCTED BY CLIENT)','(HS8825110)','nr',67.24);
insert into repairs_rates (id, code, description, ref, unit, rate) values (919,'VOI-1250-C','PLUMBING & HEATING,       TESTING & COMMISSIONING CENTRAL HEATING SYSTEM, (PER PROPERTY) (N.B WHERE SPECIFICALLY INSTRUCTED BY CLIENT)','(HS8825115)','nr',134.48);
insert into repairs_rates (id, code, description, ref, unit, rate) values (920,'VOI-1300-C','PLUMBING & HEATING,        TESTING & COMMISSIONING GAS INSTALLATION PER PROPERTY) (N.B WHERE SPECIFICALLY INSTRUCTED BY CLIENT)','(HS8825120)','nr',33.62);
insert into repairs_rates (id, code, description, ref, unit, rate) values (921,'VOI-1350-C','SECURITY,                                 FULLY SECURING VOID PROPERTY','(HS8825100)','nr',1154.31);
insert into repairs_rates (id, code, description, ref, unit, rate) values (922,'VOI-1400-C','SECURITY,                        REMOVING TEMPORARY SECURITY MEASURES TO VOID PROPERTY','(HS8825105)','nr',108.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (923,'VOI-1450-C','SECURITY,                                FIXING TEMPORARY PROTECTIVE SCREEN TO EXISTING EXTERNAL DOOR, REMOVING AT LATER DATE (PER DOOR)','(HS8825110)','nr',180.35);
insert into repairs_rates (id, code, description, ref, unit, rate) values (924,'VOI-1500-C','SECURITY,                                 FIXING TEMPORARY PROTECTIVE SCREENS TO ALL EXISTING EXTERNAL DOORS, REMOVING AT LATER DATE, n/e 2nr (PER PROPERTY)','(HS8830115)','nr',360.7);
insert into repairs_rates (id, code, description, ref, unit, rate) values (925,'VOI-1550-C','SECURITY,                                 FIXING TEMPORARY PROTECTIVE SCREEN TO EXISTING WINDOW, REMOVING AT LATER DATE (PER WINDOW)','(HS8830120)','nr',90.18);
insert into repairs_rates (id, code, description, ref, unit, rate) values (926,'VOI-1600-C','SECURITY,                                FIXING TEMPORARY PROTECTIVE SCREEN TO ALL EXISTING WINDOWS, REMOVING AT LATER DATE, n/e 10nr (PER PROPERTY)','(HS8830125)','nr',901.76);
insert into repairs_rates (id, code, description, ref, unit, rate) values (927,'VOI-1650-C','WEEDKILLER,                       APPLYING WEEDKILLER IN GARDEN, SMALL (n/e 25.00m2)','(HS8830100)','nr',50.56);
insert into repairs_rates (id, code, description, ref, unit, rate) values (928,'VOI-1700-C','WEEDKILLER,                      APPLYING WEEDKILLER IN GARDEN, MEDIUM (25.00-50.00m2)','(HS8830105)','nr',101.12);
insert into repairs_rates (id, code, description, ref, unit, rate) values (929,'VOI-1750-C','WEEDKILLER,                       APPLYING WEEDKILLER IN GARDEN, LARGE (exc.50.00m2)','(HS8830110)','m2',2.02);
update repairs_rates set area='VOID PROPERTIES' where id <= 929 and id > 894;

insert into repairs_rates (id, code, description, ref, unit, rate) values (930,'WAT-0050-C','FLOORS,          APPLYING TANKING TO EXISTING FLOORS, SMALL AREAS (n/e 3.00m2)','(HS9205100)','nr',44.22);
insert into repairs_rates (id, code, description, ref, unit, rate) values (931,'WAT-0100-C','FLOORS,         APPLYING TANKING TO EXISTING FLOORS, LARGE AREAS (exc. 3.00m2)','(HS9205105)','m2',14.74);
insert into repairs_rates (id, code, description, ref, unit, rate) values (932,'WAT-0150-C','WALLS,           APPLYING TANKING TO EXISTING WALLS, SMALL AREAS (n/e 3.00m2)','(HS9210100)','nr',52.23);
insert into repairs_rates (id, code, description, ref, unit, rate) values (933,'WAT-0200-C','WALLS,          APPLYING TANKING TO EXISTING WALLS, LARGE AREAS (exc.3.00M2)','(HS9210105)','m2',17.41);
insert into repairs_rates (id, code, description, ref, unit, rate) values (934,'WAT-0250-C','WALLS,           APPLYING CLEAR WATERPROOF SEALER TO EXISTING WALLS, SMALL AREAS (n/e 3.00m2)','(HS9210110)','nr',15.79);
insert into repairs_rates (id, code, description, ref, unit, rate) values (935,'WAT-0300-C','WALLS,           APPLYING CLEAR WATERPROOF SEALER TO EXISTING WALLS, LARGE AREAS (exc. 3.00m2)','(HS9210115)','m2',5.26);
update repairs_rates set area='WATERPROOFING' where id <= 935 and id > 929;

insert into repairs_rates (id, code, description, ref, unit, rate) values (936,'HOUR-RATE-GEN','General/Labourer','','hour',12.45);
insert into repairs_rates (id, code, description, ref, unit, rate) values (937,'HOUR-RATE-CRA','Craftsman','','hour',16.28);
insert into repairs_rates (id, code, description, ref, unit, rate) values (938,'HOUR-RATE-APLUM','Advanced Plumber','','hour',21.87);
insert into repairs_rates (id, code, description, ref, unit, rate) values (939,'HOUR-RATE-PLUM','Trained Plumber','','hour',18.15);
insert into repairs_rates (id, code, description, ref, unit, rate) values (940,'HOUR-RATE-ELE','Electrician','','hour',17);
-- update repairs_rates set area='' where id <= 940 and id > 935;
