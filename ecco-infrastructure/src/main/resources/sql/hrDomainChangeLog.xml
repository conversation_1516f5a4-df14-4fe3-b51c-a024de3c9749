<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- maintains a separate isolated domain -->

    <!-- HANDLES: (based on search for <createTable)
     - hibernate_sequences, hr_commands
     -
     - NB any changesets that rely on previous changesets in the general domain will need moving to the general domain
     - or carefully separating into its own file, because of the order specified oin 1.2-inwards-changelog.xml
    -->

    <include file="classpath:sql/2023/hr-domain/001-hrDomainChangeLog.xml"/>

</databaseChangeLog>
