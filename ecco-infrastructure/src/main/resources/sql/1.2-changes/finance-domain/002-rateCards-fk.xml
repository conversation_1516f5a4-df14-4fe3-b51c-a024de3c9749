<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="ECCO-1454-ratecardentries-fk" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="ECCO-1454-ratecardentries" author="adamjhamer" changeLogFile="classpath:sql/1.2-changes/finance-domain/002-rateCards.xml"/>
            </not>
        </preConditions>
        <addForeignKeyConstraint constraintName="fk_ratecdent_outId_lists" baseTableName="fin_ratecardentries"
                                 baseColumnNames="matchingOutcomeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_ratecdent_uom" baseTableName="fin_ratecardentries"
                                 baseColumnNames="unitMeasurementId" referencedTableName="fin_unitofmeasurements" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_ratecdent_child" baseTableName="fin_ratecardentries"
                                 baseColumnNames="childRateCardEntryId" referencedTableName="fin_ratecardentries" referencedColumnNames="id" />
    </changeSet>

</databaseChangeLog>
