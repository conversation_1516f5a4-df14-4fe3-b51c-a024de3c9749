<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
    logicalFilePath="classpath:sql/1.2-changes/config-domain/001-configDomainChangeLog.xml">

    <changeSet id="support.evidence.showActionIdsToggle" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="support.evidence.showActionIdsToggle"/>
            <column name="description" value="Show the support filter 'show ids'"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="groupsupport.support.commentType" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="groupsupport.support.commentType"/>
            <column name="description" value="Show the support comment type under the note"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- schema context because everyone needs to get this -->
    <changeSet id="DEV-778-referrals-live-menuitem" author="adamjhamer">
        <insert tableName="cfg_menuitem">
            <column name="id" valueNumeric="80" /> <!-- after my referals -->
            <column name="imageUrl" value="/icons/crystal/import/png/48.png" />
            <column name="linkText" value="menu.linktext.live_referrals" />
            <column name="roles" value="ROLE_EVANGELIST" /> <!-- for now, restrict -->
            <column name="url" value="/online/referrals/live/" />
            <column name="module_name" value="core" />
        </insert>
    </changeSet>
    <changeSet id="DEV-778-referrals-live-menu-menuitem" author="adamjhamer">
        <insert tableName="cfg_menu_cfg_menuitem">
            <column name="cfg_menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="80" />
        </insert>
    </changeSet>

    <include file="classpath:sql/1.2-changes/config-domain/002-DEV-673-gender-listDef.xml"/>

    <changeSet author="adamjhamer" id="DEV-714-remove-editEvidence">
        <delete tableName="cfg_feature">
            <where>name = 'evidence.history.editComment'</where>
        </delete>
    </changeSet>

    <include file="classpath:sql/1.2-changes/config-domain/003-DEV-674-disability-listDef.xml"/>

    <include file="classpath:sql/1.2-changes/config-domain/004-DEV-674-sexorient-listDef.xml"/>

    <include file="classpath:sql/1.2-changes/config-domain/005-DEV-965-resourceType-listDef.xml"/>

    <include file="classpath:sql/1.2-changes/config-domain/006-DEV-1141-nationality-listDef.xml"/>

    <changeSet author="adamjhamer" id="DEV-848-loneWorker-module">
        <insert tableName="cfg_module">
            <column name="name" value="loneWorker" />
            <column name="enabled" valueBoolean="false" />
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-848-loneWorker-module-inTests" context="acceptanceTests">
        <update tableName="cfg_module">
            <column name="enabled" valueBoolean="true" />
            <where>name='loneWorker'</where>
        </update>
    </changeSet>

    <!-- schema context because everyone needs to get this -->
    <changeSet id="DEV-861-loneworker-menuitem" author="adamjhamer">
        <insert tableName="cfg_menuitem">
            <column name="id" valueNumeric="81" /> <!-- after quick log -->
            <column name="imageUrl" value="/icons/crystal/import/png/48.png" />
            <column name="linkText" value="menu.linktext.loneworker" />
            <column name="roles" value="ROLE_EVANGELIST" /> <!-- for now, restrict -->
            <column name="url" value="/online/loneworker/" />
            <column name="module_name" value="loneWorker" />
        </insert>
    </changeSet>
    <changeSet id="DEV-861-loneworker-menu-menuitem" author="adamjhamer">
        <insert tableName="cfg_menu_cfg_menuitem">
            <column name="cfg_menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="81" />
        </insert>
    </changeSet>

    <changeSet id="DEV-611-createReferral-template" author="adamjhamer">
        <insert tableName="templates">
            <column name="name" value="ReferralCreated"/>
            <column name="sourceType" value="MARKDOWN" />
            <column name="rootEntity" value="REFERRAL_CREATED_EVENT"/> <!-- MUST exist in Template.rootEntity -->
            <column name="body" type="CLOB"><![CDATA[A new referral has been received.

[Click here to open their file](http://localhost:8080/ecco-war/nav/sr/{serviceRecipientId})]]></column>
        </insert>
    </changeSet>

    <changeSet id="DEV-611-toggleEmails" author="adamjhamer">
        <insert tableName="setting">
            <column name="id" valueNumeric="56"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="ENABLED"/>
            <column name="namespace" value="com.ecco.mail"/>
            <column name="consumedOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="false"/>
            <column name="description" value="Specifies whether emails should be sent"/>
            <column name="type" value="BOOLEAN"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-624-contacts.cards" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.contacts.cards"/>
            <column name="description" value="Show the 'contacts' as cards"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1025-referrals-list-addresses" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referral.list.clientAddress"/>
            <column name="description" value="Show the client address on 'list referrals'"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-1084-tasks.edit.new.agreements-feature">
        <insert tableName="cfg_feature">
            <column name="name" value="tasks.edit.new.agreements"/>
            <column name="description" value="Use new UI for editing the task: dp / consent etc"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-1098-browser-advice-text-setting">
        <update tableName="setting">
            <column name="keyValue" value="You are using an old browser that does not support all modern web features and may be less secure. ECCO supports modern web browsers which includes: Microsoft Edge, Chrome, Chrome for Android and Safari for iOS" />
            <where>id=37</where>
        </update>
        <update tableName="setting">
            <column name="keyValue" value="https://www.google.com/chrome/" />
            <where>id=38</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-1100-reintroduce-editEvidence">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.history.editComment"/>
            <column name="description" value="Allow edit of history comment area"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="z3029-reverse-evidenceFilter">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.filter.showAchieved.defaultOff"/>
            <column name="description" value="Reverse the default on"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="z3196-reverse-evidenceFilter-undo">
        <insert tableName="cfg_feature">
            <column name="name" value="evidence.filter.showUndo.defaultOff"/>
            <column name="description" value="Reverse the default on"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- NB 'admin referral' is exposed on the user page if the GroupsToExclude are non-standard -->
    <changeSet author="adamjhamer" id="z3041-adminreferral-groupsToExclude">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,admin evidence,admin referral"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,admin evidence'
            </where>
        </update>
    </changeSet>

    <include file="classpath:sql/1.2-changes/config-domain/007-DEV-1151-maritalStatus-listDef.xml"/>

    <changeSet author="adamjhamer" id="DEV-1108-remove-evidenceModal-feature">
        <delete tableName="cfg_feature">
            <where>name = 'referralOverview.supportPlan.modal'</where>
        </delete>
    </changeSet>

    <include file="classpath:sql/1.2-changes/config-domain/008-DEV-1171-associatedTypes-listDef.xml"/>
    <include file="classpath:sql/1.2-changes/config-domain/009-listDefs-Nov-2019-onwards.xml"/>

    <changeSet author="adamjhamer" id="DEV-1228-overseerAccess-hideByDefault">
        <update tableName="setting">
            <column name="keyvalue" value="admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,overseer-tasks,admin evidence,admin referral"/>
            <where>
                keyname='GroupsToExclude' and keyvalue='admin,case-manager,duty,hr-volunteers,commissioner,client,demo,rota,overseer,overseer-history,admin evidence,admin referral'
            </where>
        </update>
    </changeSet>

    <changeSet id="DEV-1231-fix-default-clientDetailOptionalFields" author="nealeu">
        <update tableName="setting">
            <column name="keyvalue" value="firstLanguageId,ethnicOriginId,religionId,disabilityId,sexualityId,ni"/>
            <where>
                keyvalue='firstLanguage,ethnicOrigin,religion,disability,sexuality,ni'
                AND
                id=22
            </where>
        </update>
    </changeSet>

    <changeSet id="DEV-1231-add-clientDetailOptionalFields-2" author="adamjhamer">
        <!-- preCondition to check we've manually added Id to all listdef fields -->
        <preConditions onError="HALT">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM setting
                WHERE (id=22 AND (keyvalue LIKE '%e,%' or keyvalue LIKE '%n,%' or keyvalue LIKE '%tus,%' or keyvalue LIKE '%y,%'))
            </sqlCheck>
        </preConditions>
        <update tableName="setting">
            <column name="description" value="supported fields (firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, genderAtBirthId, sexualOrientationId, maritalStatusId, ni, housingBenefit, nhs)"/>
            <where>id=22</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1243-srcGeographicArea" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralDetails.srcGeographicArea"/>
            <column name="description" value="Allow area to be collected on details of referral"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-1356-create-feature-referralOverview.formHistory">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.formHistory"/>
            <column name="description" value="Shows 'form history' tab on the referral page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1381-referral.emergency.riskFlags" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referral.emergency.riskFlags"/>
            <column name="description" value="Show the risk flags on the grab sheet"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1403-rota.shifts" author="nealeu">
        <insert tableName="cfg_feature">
            <column name="name" value="rota.shifts"/>
            <column name="description" value="Enable rota shifts/care runs"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1485-feature-referralOverview.nhsNumber" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.nhsNumber"/>
            <column name="description" value="Shows 'nhs' on overview tab on the referral page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1485-feature-referralOverview.meetingDays" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.meetingDays"/>
            <column name="description" value="Shows 'meeting days' on overview tab on the referral page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1485-feature-referralOverview.supportLevel" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="referralOverview.supportLevel"/>
            <column name="description" value="Shows 'support level' on overview tab on the referral page"/>
            <column name="defaultVote" value="DISABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1505-feature-menu.welcome.link.dashboards" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="menu.welcome.link.dashboards"/>
            <column name="description" value="Shows 'dashboard' on welcome page menu"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1496-listdef-room-repurposed" author="adamjhamer">
        <update tableName="cfg_list_definitions">
            <column name="name" value="room" />
            <where>id=134</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1745-feature-rota.whole-org.enable" author="nealeu">
        <insert tableName="cfg_feature">
            <column name="name" value="rota.whole-org.enable"/>
            <column name="description" value="Show 'rota' on welcome and remove whole org from building dropdown"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE - USE A CHANGELOG in the correct YEAR folder *** -->

</databaseChangeLog>
