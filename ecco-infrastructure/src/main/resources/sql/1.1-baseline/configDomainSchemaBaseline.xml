<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-61">
        <createTable tableName="messages">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="basename" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column name="language" type="VARCHAR(7)"/>
            <column name="country" type="VARCHAR(7)"/>
            <column name="variant" type="VARCHAR(7)"/>
            <column name="msg_key" type="VARCHAR(255)"/>
            <column name="message" type="VARCHAR(2000)"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-106">
        <createTable tableName="setting">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="keyname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="namespace" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="readOnStartup" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="keyvalue" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-133" logicalFilePath="classpath:sql/1.1-baseline/portableSchemaBaseline.xml">
        <createTable tableName="uploadbytes">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="bytes" type="LONGBLOB"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="1366042567388-134" logicalFilePath="classpath:sql/1.1-baseline/portableSchemaBaseline.xml">
        <createTable tableName="uploadfile">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="filename" type="VARCHAR(255)"/>
            <column name="upload_size" type="BIGINT"/>
            <column name="mediatype" type="VARCHAR(255)"/>
            <column name="bytesid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
