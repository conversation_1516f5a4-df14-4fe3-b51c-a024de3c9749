<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- recreate each year so its included in each run -->
    <changeSet id="should-never-execute" context="should_never_execute" author="nealeu">
        <preConditions onFail="HALT">
            <tableExists tableName="never_exists"/>
        </preConditions>
        <comment>Deliberate changeset to detect if all contexts are being run somehow</comment>
    </changeSet>


    <!-- ALWAYS ensure we have executed the final one of previous year first -->
    <changeSet id="fail-if-not-run-pre2024" author="djc">
        <preConditions onFail="HALT">
            <changeSetExecuted
                    id="DEV-2529-repDef-service-charges-body"
                    author="adamjhamer"
                    changeLogFile="2023/general-domain"/>
        </preConditions>
        <comment>A no-op changeset that HALTs if we've not run earlier changes</comment>
    </changeSet>


    <!-- Add domains in order of their dependency on each other -->
    <include file="classpath:sql/2024/config-domain/001-configDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/cosmo-domain/001-cosmoDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/security-domain/001-securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/hr-domain/001-hrDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/evidence-domain/001-evidenceDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/finance-domain/001-financeDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/general-domain/001-generalDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/general-domain/002-report-defs.xml"/>

    <!-- TODO: In 2025, enable this here, and add a 'stop' warning below
    <include file=="classpath:sql/2025/2025-onwards-changeLog.xml"/>
    -->

</databaseChangeLog>