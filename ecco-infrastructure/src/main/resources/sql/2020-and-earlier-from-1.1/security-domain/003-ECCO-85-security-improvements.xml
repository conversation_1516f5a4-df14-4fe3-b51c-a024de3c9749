<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/003-ECCO-85-security-improvements.xml">

    <changeSet author="nealeu" id="ECCO-85-1-v2">
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- If we ran v1, then we will run ECCO-85-1-fix later, which will end up with the same result -->
                <changeSetExecuted id="ECCO-85-1" author="nealeu" changeLogFile="classpath:sql/1.1-changes/003-ECCO-85-security-improvements.xml"/>
            </not>
        </preConditions>
        <createTable tableName="passwordhistory">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="password" type="VARCHAR(255)"/>
            <column name="userId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="nealeu" id="ECCO-85-2">
        <createTable tableName="passwordhistory_AUD">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="REV" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="REVTYPE" type="TINYINT"/>
            <column name="password" type="VARCHAR(255)"/>
            <column name="userId" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet context="1.1-baseline" author="baseline" id="ECCO-85-password-ora-fix" dbms="oracle">
        <renameColumn tableName="passwordhistory" oldColumnName="password" newColumnName="PASSWORD"/>
    </changeSet>


    <changeSet author="nealeu" id="ECCO-85-3">
        <addForeignKeyConstraint baseColumnNames="userId" baseTableName="passwordhistory"
            constraintName="FK728715997878A3FC" deferrable="false" initiallyDeferred="false"
            onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="users"
            referencesUniqueColumn="false" />
    </changeSet>
    <changeSet author="nealeu" id="ECCO-85-4">
        <addForeignKeyConstraint baseColumnNames="REV" baseTableName="passwordhistory_AUD"
            constraintName="FK78CCEEA6CE82CAB" deferrable="false" initiallyDeferred="false"
            onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="revision"
            referencesUniqueColumn="false" />
    </changeSet>

    <changeSet author="nealeu" id="ECCO-85-5-v2">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/003-ECCO-85-security-improvements.xml"
                        author="nealeu" id="ECCO-85-5"/>
            </not>
        </preConditions>
        <addColumn tableName="users">
            <column name="failedLoginAttempts" type="INT" defaultValueNumeric="0"/>
            <column name="lastFailedLogin" type="DATETIME"/>
            <column name="lastPasswordChange" type="DATETIME"/>
        </addColumn>
    </changeSet>

    <changeSet author="andy-gray" id="ECCO-85-6">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-baseline/securityDomainSchemaBaseline.xml"
                    author="baseline" id="1366042567388-43"/>
        </preConditions>
        <dropColumn tableName="failedlogins" columnName="id" />
    </changeSet>

    <!-- stick to auditing security changes, failed logins are already recorded in LoginListener -->
    <changeSet author="adamjhamer" id="ECCO-85-7">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/003-ECCO-85-security-improvements.xml"
                    author="nealeu" id="ECCO-85-5"/>
        </preConditions>
        <dropColumn tableName="users_AUD" columnName="failedLoginAttempts"/>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-85-5-v2fix">
        <addNotNullConstraint tableName="users" columnName="failedLoginAttempts" columnDataType="INT"/>
    </changeSet>

    <changeSet author="bodeng" id="ECCO-85-1-fix" onValidationFail="MARK_RAN">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-85-1" author="nealeu" changeLogFile="classpath:sql/1.1-changes/003-ECCO-85-security-improvements.xml"/>
        </preConditions>
        <customChange class="com.ecco.infrastructure.liquibase.DropAutoIncrementChange" columnName="id" columnDataType="BIGINT" tableName="passwordhistory"/>
    </changeSet>
</databaseChangeLog>
