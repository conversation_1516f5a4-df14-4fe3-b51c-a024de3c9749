<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-870-actions-table" author="adamjhamer">
        <createTable tableName="gsat_linkedactions">
            <column name="groupsupportactivitytypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="actionid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-870-actions-pk" author="adamjhamer">
        <addPrimaryKey columnNames="groupsupportactivitytypeid, actionid" tableName="gsat_linkedactions"/>
    </changeSet>
    <changeSet id="ECCO-870-actions-fk1" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="groupsupportactivitytypeId" baseTableName="gsat_linkedactions" constraintName="fk_gsatla_gsat" referencedColumnNames="id" referencedTableName="groupsupportactivitytypes"/>
    </changeSet>
    <changeSet id="ECCO-870-actions-fk2" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="actionId" baseTableName="gsat_linkedactions" constraintName="fk_gsatla_actions" referencedColumnNames="id" referencedTableName="actions"/>
    </changeSet>

</databaseChangeLog>
