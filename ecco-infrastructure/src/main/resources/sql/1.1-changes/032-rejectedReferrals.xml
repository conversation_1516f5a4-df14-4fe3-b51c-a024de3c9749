<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="adamjhamer" id="ECCO-398">
        <validCheckSum>8:caf6c604e8b20158bc713b49036245bd</validCheckSum>
        <!-- useful sql: select id,created,receivedDate,decisionReferralMadeOn,acceptedReferral,decisionDate,decision,decisionMadeOn,acceptedOnService,signpostedCommentId,receivingService,exited from referrals; -->
        <update tableName="referrals">
            <column name="decisionMadeOn" valueComputed="decisionReferralMadeOn"/>
            <!-- decided, rejected at referral and decisionMadeOn date is null (this last test captures the quirk) -->
            <where>decision=true and acceptedReferral=false and signpostedCommentId is not null and decisionMadeOn is null</where>
        </update>
    </changeSet>

</databaseChangeLog>
