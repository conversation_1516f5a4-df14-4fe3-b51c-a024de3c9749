DROP PROCEDURE IF EXISTS migrateAddresses#

CREATE PROCEDURE migrateAddresses()
BEGIN
	DECLARE vContactId VARCHAR(255);
	DECLARE vLine1 VARCHAR(255);
	DECLARE vLine2 VARCHAR(255);
	DECLARE vLine3 VARCHAR(255);
	DECLARE vPostCode VARCHAR(255);
	DECLARE vTown VARCHAR(255);
	DECLARE vCountry VARCHAR(255);
	DECLARE vCounty VARCHAR(255);

	DECLARE vAddressId INT;

	DECLARE done INT DEFAULT FALSE;
    DECLARE curs CURSOR FOR SELECT id, addressline1, addressline2, addressline3, addresspostcode, addresstown, addresscountry, addresscounty
        FROM contacts WHERE addressId IS NULL AND addressline1 IS NOT NULL AND addresspostcode IS NOT NULL;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN curs;

    read_loop: LOOP
        FETCH curs INTO vContactId, vLine1, vLine2, vLine3, vPostCode, vTown, vCountry, vCounty;

        IF done THEN
            LEAVE read_loop;
        END IF;

        SELECT MAX(id) + 1 INTO vAddressId FROM bldg_addresses;
        INSERT INTO bldg_addresses (id, version, line1, line2, line3, postcode, town, country, county)
            VALUES (LAST_INSERT_ID(vAddressId), 0, vLine1, vLine2, vLine3, vPostCode, vTown, vCountry, vCounty)
            ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id);
        UPDATE contacts SET addressId = LAST_INSERT_ID() WHERE id = vContactId;

    END LOOP;

    CLOSE curs;
END#

CALL migrateAddresses()#
