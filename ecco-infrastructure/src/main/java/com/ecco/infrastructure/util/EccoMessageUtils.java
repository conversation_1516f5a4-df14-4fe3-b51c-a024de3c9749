package com.ecco.infrastructure.util;

import org.jspecify.annotations.NonNull;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.context.support.MessageSourceAccessor;

/** this Spring Bean class is a convenience hack to get access to the message source in static methods
 * @deprecated use the MessageSourceAware interface directly
 */
@Deprecated
public class EccoMessageUtils implements MessageSourceAware {

    // we don't use the message files are cached anyway, and the custom sources are simply replaced with a bean
    private static MessageSourceAccessor messageSourceAccessor;
    @Override
    public void setMessageSource(@NonNull MessageSource messageSource) {
        messageSourceAccessor = new MessageSourceAccessor(messageSource);
    }

    public static MessageSourceAccessor getUiMessageSource() {
        return messageSourceAccessor;
    }
}
