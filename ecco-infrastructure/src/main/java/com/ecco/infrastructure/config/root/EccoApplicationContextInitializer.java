package com.ecco.infrastructure.config.root;

import com.ecco.infrastructure.config.root.Profiles.Validator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.ResourcePropertySource;
import org.springframework.mock.env.MockPropertySource;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.List;
import java.util.TimeZone;

import static org.springframework.util.StringUtils.hasText;


/**
 * Requires the following in web.xml
 * <pre>
 *  &lt;context-param&gt;
 *      &lt;param-name>contextInitializerClasses&lt;/param-name&gt;
 *      &lt;param-value&gt;com.ecco.infrastructure.config.root.EccoApplicationContextInitializer&lt;/param-value&gt;
 *  &lt;/context-param&gt;
 * </pre>
 *
 * For Spring Boot this needs to be included in spring.factories:
 * <pre>
 *     org.springframework.context.ApplicationContextInitializer=com.ecco.infrastructure.config.root.EccoApplicationContextInitializer
 * </pre>
 * , or in application.properties:
 * <pre>
 *     context.initializer.classes=com.ecco.infrastructure.config.root.EccoApplicationContextInitializer
 * </pre>
 * See answers <a href="https://stackoverflow.com/a/35217768">here</a>
 *
 */
public class EccoApplicationContextInitializer implements
        ApplicationContextInitializer<ConfigurableApplicationContext> {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        logger.info("================== CONTEXT ====================");
        logger.info("working directory = {}", System.getProperty("user.dir"));
        logger.info("java.io.tmpdir (cache etc) = {}", System.getProperty("java.io.tmpdir"));
        logger.info("java.version = {}", applicationContext.getEnvironment().getProperty("java.version"));
        logger.info("java.vendor = {}", applicationContext.getEnvironment().getProperty("java.vendor"));
        logger.info("java.vm.name = {}", applicationContext.getEnvironment().getProperty("java.vm.name"));
        logger.info("java.vm.version = {}", applicationContext.getEnvironment().getProperty("java.vm.version"));
        logger.info("spring.profiles.active (from env) = {}", applicationContext.getEnvironment().getProperty("spring.profiles.active"));
        logger.info("UTC user.timezone vs system: " + applicationContext.getEnvironment().getProperty("user.timezone") + " vs " + TimeZone.getDefault().getID());
        UTCConfig.assertUTC();

        logger.info("================== ENV =====================");
        ConfigurableEccoEnvironment environment = new ConfigurableEccoEnvironment();
        environment.merge(applicationContext.getEnvironment()); // We want whatever may have been injected before e.g. by Spring Test
        configureEnvironment(environment);
        overrideEnvironment(environment);
        environment.refresh();
        applicationContext.setEnvironment(environment);

        registerPostProcessor(applicationContext);

        Assert.notEmpty(applicationContext.getEnvironment().getActiveProfiles(), "Something has gone wrong. At least one profile should be active");

        logger.info("Active bean profiles now: {}", StringUtils.join(applicationContext.getEnvironment().getActiveProfiles(), ","));

        Validator.validate(applicationContext.getEnvironment());

        logger.info("=============================================");
    }


    /**
     * Open for extension to allow us to inject a {@link MockPropertySource} and active profiles with values we want for testing.
     * <p>
     * Override this and call TestSupport.configureMockPropertySource()
     */
    protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
        String env = environment.getProperty("env");
        logger.info("env = {}", env);

        var log4jConfigurationProperty = environment.getProperty("log4j.configuration");
        if (log4jConfigurationProperty != null) {
            logger.info("log4j.configuration = classpath:{}", log4jConfigurationProperty);
        }
        else {
            logger.info("No log4j.configuration specified, so Log4j will scan for using ConfigurationFactory impls. Here's what we've found:");
            logDetectedLog4jFilePaths();
        }
        // If env isn't set or profiles are set explicitly, we use spring.profiles.active from property source instead
        // of deriving them from env,db.
        // This includes for TESTS where we specify @ActiveProfiles() -Denv=dev will override this
        if (env == null || environment.getActiveProfiles().length > 0) {
            return;
        }
        if (!hasText(env)) {
            logger.warn("** It's best that env is not empty. Set -Denv=prod|dev explicitly **");
        }

        String db = environment.getProperty("db", "mysql");
        logger.info("db = {}", db);
        String profile = Environments.getProfileFor(env, db);

        logger.info("profile = {}", profile);

        environment.refresh(); // force (not elegant, but...)

        if (environment.getLiquibaseMode() == LiquibaseMode.DUMP) {
            environment.setActiveProfiles(profile, Profiles.EXPORT_LIQUIBASE);
        }
        else {
            environment.setActiveProfiles(profile);
        }

        if (environment.getDBUnitMode() == DBUnitMode.EXPORT) {
            environment.addActiveProfile(Profiles.EXPORT_DBUNIT);
        }
        if (environment.getDBUnitMode() == DBUnitMode.CLEAN_IMPORT) {
            environment.addActiveProfile(Profiles.IMPORT_DBUNIT);
        }

        String acls = environment.getProperty("enableAcls", "false"); // returns empty string for -DenableAcls
        if (!acls.equals("false")) {
            throw new IllegalArgumentException(
                    "the enableAcls argument was removed and replaced with com.ecco.authn.acl settings (which is now ignored) \n" +
                            "See ecco.authn.acl in AclConfig");
        }
    }

    public void logDetectedLog4jFilePaths() {
        var resourceLoader = new DefaultResourceLoader();
        for (String prefix : List.of("log4j2-test", "log4j2")) {
            for (String suffix : List.of(".xml", ".yml", ".yaml", ".json", ".springboot")) { // .springboot is special trigger for SpringBootConfiguration
                String fileName = "classpath:" + prefix + suffix;
                var resource = resourceLoader.getResource(fileName);
                if (resource.exists()) {
                    try {
                        logger.info("Found: {} at {}", fileName, resource.getURI());
                    } catch (IOException e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * Allow a globally defined file to override properties.
     */
    protected void overrideEnvironment(ConfigurableEccoEnvironment environment) {
        // eg -Doverride.location=file:///C:/ECCO-TEST/override.properties
        String overrideFile = environment.getProperty("override.location");
        logger.info("application properties override.location = {}", overrideFile);

        if (overrideFile != null) {
            // see https://stackoverflow.com/questions/17401583/optional-propertysource-location
            try {
                String localPropertiesPath = environment.resolvePlaceholders(overrideFile);
                ResourcePropertySource localPropertySource = new ResourcePropertySource(localPropertiesPath);
                environment.getPropertySources().addFirst(localPropertySource);
            } catch (IOException e) {
                logger.error("Error processing override.location", e);
            }
        }
    }

    private void registerPostProcessor(ConfigurableApplicationContext appContext) {
//        appContext.addBeanFactoryPostProcessor(new SpringServiceFactoryInjector(appContext));
    }

}
