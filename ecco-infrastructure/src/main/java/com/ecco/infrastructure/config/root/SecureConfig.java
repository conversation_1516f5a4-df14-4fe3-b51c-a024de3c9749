package com.ecco.infrastructure.config.root;

import java.security.NoSuchAlgorithmException;
import java.security.Provider;
import java.security.Security;
import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jasypt.encryption.pbe.PBEStringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.hibernate5.encryptor.HibernatePBEStringEncryptor;
import org.jasypt.salt.FixedStringSaltGenerator;
import org.jasypt.salt.RandomSaltGenerator;
import org.springframework.context.annotation.Bean;


/**
 * Configure how we will encrypt and decrypt sensitive data.  Mappings in Hibernate would need to be as follows:
 *
 * <pre><code>
 * &#64;TypeDef(
 *  name="encryptedString",
 *  typeClass=EncryptedStringType.class,
 *    parameters= {
 *       &#64;Parameter(name="encryptorRegisteredName", value="hibernateStringEncrytor")
 *    }
 * )
 *
 * &#064;Type(type=HibTypeNames.ENCRYPTED_STRING)
 * </pre>
 * The first of these you should be able to find in com.ecco.infrastructure.hibernate package-info.java
 */
public class SecureConfig {

    // NOTE: DO NOT CHANGE - you'll break encryption
    private static final String REGISTERED_NAME = "hibernateStringEncrytor";

    private static final String REG_SEARCHABLE_NAME = "hibernateSearchableEncrytor";


    public SecureConfig() {
        Security.addProvider(new BouncyCastleProvider());
    }

    public PBEStringEncryptor encryptor() {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setProviderName("BC");
        encryptor.setAlgorithm("PBEWITHSHA256AND128BITAES-CBC-BC");

        encryptor.setPassword(REGISTERED_NAME.toLowerCase().replaceAll("[a-e]", "Copyright (c) Ecco Solutions Limited"));
        encryptor.setSaltGenerator(new RandomSaltGenerator());
        encryptor.setKeyObtentionIterations(1787);
        return encryptor;
    }

    public PBEStringEncryptor searchableEncryptor() {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setProviderName("BC");
        encryptor.setAlgorithm("PBEWITHSHA256AND128BITAES-CBC-BC");

        // Runtime generated password to avoid obvious extract from string table
        encryptor.setPassword(REGISTERED_NAME.toLowerCase().replaceAll("[f-q]", "Copyright (c) Ecco Solutions Limited"));
        FixedStringSaltGenerator saltGenerator = new FixedStringSaltGenerator();
        saltGenerator.setSalt("Exception processing configuration: {}"); // DON'T CHANGE - it deliberately looks like a log message
        encryptor.setSaltGenerator(saltGenerator);
        encryptor.setKeyObtentionIterations(1456);
        return encryptor;
    }

    private PBEStringEncryptor nullEncryptor() {
        return new PBEStringEncryptor() {

            @Override
            public void setPassword(String password) {
            }

            @Override
            public String encrypt(String message) {
                return message;
            }

            @Override
            public String decrypt(String encryptedMessage) {
                return encryptedMessage;
            }
        };
    }

    /**
     * Throw exception early if we need JCE and it isn't configured
     * (rather than wait until the point of login to be presented with 'failed login')
     * @return not used
     */
    @Bean
    public boolean verifyJCE(EccoEnvironment env) {
        if (!env.shouldEncryptDatabaseFields()) {
            return true;
        }

        int maxKeyLen = 0;
        try {
            maxKeyLen = Cipher.getMaxAllowedKeyLength("AES");
        } catch (NoSuchAlgorithmException e) {
        }
        // without the unlimited strength policy files this results in 128, after they have been installed properly the result is 2147483647
        if (maxKeyLen <= 128) {
            throw new IllegalStateException("The configuration requires JCE unlimited encryption - but its not found on "
                    + "this JVM");
        }
        return true;
    }

    @Bean
    public HibernatePBEStringEncryptor hibStringEncryptor(EccoEnvironment env) {
        HibernatePBEStringEncryptor hpbese = new HibernatePBEStringEncryptor();
        hpbese.setEncryptor(env.shouldEncryptDatabaseFields() ? encryptor() : nullEncryptor());
        hpbese.setRegisteredName(REGISTERED_NAME);
        return hpbese;
    }

    @Bean
    public HibernatePBEStringEncryptor hibSearchableEncryptor(EccoEnvironment env) {
        HibernatePBEStringEncryptor hpbese = new HibernatePBEStringEncryptor();
        hpbese.setEncryptor(env.shouldEncryptDatabaseFields() ? searchableEncryptor() : nullEncryptor());
        hpbese.setRegisteredName(REG_SEARCHABLE_NAME);
        return hpbese;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        for (Provider provider : Security.getProviders()) {
            System.out.println("Provider: " + provider.getName());
            for (Provider.Service service : provider.getServices()) {
                System.out.println("  Algorithm: " + service);
            }
        }
//        for (Object obj : java.security.Security.getAlgorithms("Cipher")) {
//            System.out.println(obj);
//        }
        SecretKeyFactory instance = javax.crypto.SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1");
        System.out.println(instance.getProvider().getName());
    }
}
