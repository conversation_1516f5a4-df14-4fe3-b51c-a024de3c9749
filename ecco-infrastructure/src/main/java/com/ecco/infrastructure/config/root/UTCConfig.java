package com.ecco.infrastructure.config.root;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

@Configuration
public class UTCConfig {

    /**
     * There are problems with dates between cosmo (DST problems) since ical4j uses the jdk default timezone
     * and it seems if we change the default, joda and ical4j are already instantiated. Therefore we change this
     * from a setter to an error and push configuration back to jvm/mvn
     * @return not used
     */
    @Bean
    public boolean verifyUTC() {
        assertUTC();
        return true;
    }

    public static void assertUTC() {
        if (!"UTC".equals(TimeZone.getDefault().getID())) {
            throw new IllegalStateException("JVM param -Duser.timezone=UTC must be specified for the webapp "
                    + "(one of java cmd; mvn MAVEN_OPTS or -DargLine; m2e test VM args.) "
                    + "The JVM's current timezone is " + TimeZone.getDefault().getID());
        }
    }
}
