package com.ecco.infrastructure.config.root;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import com.ecco.infrastructure.util.LiquibaseChangeLogGenerator;

@Configuration(proxyBeanMethods = false)
@Profile(Profiles.EXPORT_LIQUIBASE)
public class SchemaExportConfig {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private DataSource dataSource;

    @Bean
    public LiquibaseChangeLogGenerator changeLogGenerator() {
        return new LiquibaseChangeLogGenerator(context, dataSource);
    }

}
