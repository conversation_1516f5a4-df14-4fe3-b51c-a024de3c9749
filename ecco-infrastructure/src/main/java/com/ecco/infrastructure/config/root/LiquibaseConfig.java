package com.ecco.infrastructure.config.root;

import liquibase.integration.spring.SpringLiquibase;
import org.apache.tomcat.jdbc.pool.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.ArrayList;

@Configuration
public class LiquibaseConfig {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private EccoEnvironment env;

    @Autowired
    private DataSource dataSource;

    @Value("${liquibase.changelog:}")
    private String liquibaseChangelogPath;

    @Bean
    public SpringLiquibase liquibase() {

        printConfig();

        SpringLiquibase liquibase = new SpringLiquibase();

        if (env.shouldRunLiquibaseChangelogs()) {
            liquibase.setDataSource(dataSource);
            liquibase.setChangeLog(changeLogPathOrDeriveFromDbVersion());
            liquibase.setDropFirst(env.shouldRebuild()); // drops all objects in the current schema
            liquibase.setContexts(getLiquibaseContexts());
        } else {
            liquibase.setShouldRun(false);
        }

        return liquibase;
    }

    private void printConfig() {
        log.info("=================== LIQUIBASE =================");
        log.info(" datasource: " + dataSource.toString());
        log.info("  schema:    " + env.getDbSchema());
        log.info("  mode:      " + env.getLiquibaseMode().name());
        log.info("  contexts:  " + getLiquibaseContexts());
        log.info("  changelog: " + changeLogPathOrDeriveFromDbVersion());
        log.info("===============================================");

        UTCConfig.assertUTC();
    }

    private String changeLogPathOrDeriveFromDbVersion() {
        return StringUtils.hasText(liquibaseChangelogPath)
                ? liquibaseChangelogPath
                : "classpath:sql/" + env.getSchemaVersion().changeLogFileNameFor(env.getLiquibaseMode());
    }

    /**
     * @return comma-sep list of liquibase contexts to activate
     */
    private String getLiquibaseContexts() {
        var contexts = new ArrayList<String>();

        contexts.add("schema"); // Prevents us having everything run by accident, but liquibase runs every changeset with no context

        if (env.shouldCreateSchema()) {
            contexts.add("1.1-baseline");
            contexts.add(env.isTestRun() ? "test-schema,test-data" : "1.1-base-data,base-data");
        }

        if (env.shouldEncryptDatabaseFields()) {
            contexts.add("1.1-apply-encryption");
        }

        if (StringUtils.hasText(env.extraContexts())) {
            contexts.add(env.extraContexts());
        }

        return String.join(",", contexts);
    }
}
