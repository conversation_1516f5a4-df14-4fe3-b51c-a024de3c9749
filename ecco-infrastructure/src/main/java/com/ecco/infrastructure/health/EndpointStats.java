package com.ecco.infrastructure.health;

import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;

/** A serialisable bean containing endpoint health stats */
public class EndpointStats {

    private Map<String, EndpointInfo> endpoints = new HashMap<>();

    @JsonIgnore
    public EndpointInfo getEndpoint(String endpointKey) {
        return endpoints.computeIfAbsent(endpointKey, key -> new EndpointInfo(endpointKey));
    }

    public Map<String, EndpointInfo> getEndpoints() {
        return endpoints;
    }
}
