package com.ecco.infrastructure.liquibase;

import lombok.Getter;
import lombok.Setter;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Introducing &#64;OrderColumn requires the order column to be initialised as the persistence provider expects.
 * This custom change allows an order column to be added to a join table
 */
@Getter
@Setter
public class AddOrderColumnChange extends UpdateableResultSetChange {

    private String collectionColumnName;

    private String entryColumnName;

    private String orderColumnName;

    private String idColumnName;

    private Object lastCollectionId = null;

    private int orderIndex = 0;

    private int orderJump = 1;

    @Override
    public String getPrimaryKeyColumns() {
        return collectionColumnName + "," + entryColumnName + (idColumnName != null ? ", " + idColumnName : "");
    }

    @Override
    protected String getAdditionalColumns() {
        return orderColumnName;
    }

    public String getOrderJumpBy() {
        return null;
    }
    public void setOrderJumpBy(String orderJumpBy) {
        this.orderJump = Integer.parseInt(orderJumpBy);
    }

    @Override
    protected String getOrderSql() {
        return "ORDER BY " + collectionColumnName + " ASC," + entryColumnName + " ASC";
    }

    @Override
    protected void computeChange(ResultSet rs) throws SQLException {

        Object collectionId = rs.getObject(collectionColumnName);

        // If new collection, reset
        if (!collectionId.equals(lastCollectionId)) {
            orderIndex = 0;
        }
        lastCollectionId = collectionId;

        rs.updateInt(orderColumnName, orderIndex);
        orderIndex += orderJump;
    }
}
