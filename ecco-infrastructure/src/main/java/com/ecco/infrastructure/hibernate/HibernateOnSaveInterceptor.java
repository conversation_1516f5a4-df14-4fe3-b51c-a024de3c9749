package com.ecco.infrastructure.hibernate;

import java.io.Serializable;

import org.hibernate.EmptyInterceptor;
import org.hibernate.type.Type;

/** Pick up onSave and test entity for implementing HibernateOnSave to allow us to do some useful things */
public class HibernateOnSaveInterceptor extends EmptyInterceptor {

    private static final long serialVersionUID = 1L;

    @Override
    public boolean onSave(Object entity,
            Serializable id,
            Object[] state,
            String[] propertyNames,
            Type[] types)
    {
        if (entity instanceof HibernateOnSave) {
            return ((HibernateOnSave) entity).hibernateOnSave(id, state, propertyNames, types);
        }
        return false;
    }
}