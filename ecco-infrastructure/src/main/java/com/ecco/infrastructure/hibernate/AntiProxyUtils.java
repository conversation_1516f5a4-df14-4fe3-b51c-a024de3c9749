package com.ecco.infrastructure.hibernate;

import com.ecco.dom.BaseEntity;
import org.hibernate.proxy.HibernateProxy;
import org.hibernate.proxy.LazyInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import javax.persistence.EntityManager;

/**
 * Static utility methods to make Hibernate Proxies less painful.
 *
 * @since 04/08/2014
 */
public class AntiProxyUtils {
    private static final Logger log = LoggerFactory.getLogger(AntiProxyUtils.class);

    /** Ensures the entity is the clean implementation. Returns the original entity if it is not a proxy. */
    public static <T extends BaseEntity<?>> T deproxy(final T entity) {
        return specificImplementation(entity);
    }

    /** Derives the true (non-proxied) persistent class for the entity. */
    public static <T extends BaseEntity<?>> Class<? extends T> persistentClass(final T entity) {
        return specificPersistentClass(entity);
    }

    /**
     * Returns the identifier for an entity. Does not deproxy entities to get it.
     */
    public static <PK extends Serializable, T extends BaseEntity<PK>> PK identifier(final T entity) {
        PK entityId = null;

        if (entity != null) {
            if (entity instanceof HibernateProxy) {
                HibernateProxy proxy = (HibernateProxy) entity;
                LazyInitializer li = proxy.getHibernateLazyInitializer();
                entityId = (PK) li.getIdentifier();
            } else {
                entityId = entity.getId();
            }
        }

        return entityId;
    }

    /** Ensures the entity is currently managed by the supplied entity manager. Does not deproxy entities if they are already managed. */
    public static <T extends BaseEntity<?>> T ensureManaged(EntityManager em, T entity) {
        final Class<? extends T> entityClass;
        final Serializable entityId;

        if (entity != null) {
            if (entity instanceof HibernateProxy) {
                HibernateProxy proxy = (HibernateProxy) entity;
                LazyInitializer li = proxy.getHibernateLazyInitializer();
                entityClass = li.getPersistentClass();
                entityId = li.getIdentifier();
            } else {
                entityClass = (Class<? extends T>) entity.getClass();
                entityId = entity.getId();
            }
            if (em != null) {
                if (!em.contains(entity)) {
                    return em.find(entityClass, entityId);
                }
            } else {
                log.warn("EntityManager null in ensureManaged() - cannot ensure {0} is managed.", entity.getClass());
            }
        }

        return entity;
    }

    private static <T> T specificImplementation(T entity) {
        if (entity != null && entity instanceof HibernateProxy) {
            entity = (T) ((HibernateProxy) entity).getHibernateLazyInitializer().getImplementation();
        }
        return entity;
    }

    private static <T> Class<? extends T> specificPersistentClass(T entity) {
        final Class<? extends T> entityClass;

        if (entity != null) {
            // LazyInitializer.getPersistentClass() does not correctly return the superclass, not the subclass for
            // e.g. Individual, it returns the superclass ContactImpl instead.
            // Consequently, we just obtain the true implementation, then get its class.
            entityClass = (Class<? extends T>) specificImplementation(entity).getClass();
        } else {
            entityClass = null;
        }

        return entityClass;
    }

}
