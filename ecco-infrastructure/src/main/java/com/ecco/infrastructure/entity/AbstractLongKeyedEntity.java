package com.ecco.infrastructure.entity;

import com.ecco.dom.LongKeyedEntity;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import javax.persistence.*;

/**
 * Abstract Entity that uses {@link AccessType#FIELD} access, except for the ID.
 *
 * NOTE: This is LONG.  Most often you probably just want AbstractIntKeyedEntity instead
 */
@MappedSuperclass
@Access(AccessType.FIELD)
public abstract class AbstractLongKeyedEntity extends AbstractUnidentifiedVersionedEntity<Long> implements LongKeyedEntity {

    private static final long serialVersionUID = 1L;
    public  static final int ENTITIES_INIT = 1000;

    @Id
    @GeneratedValue(generator="entitiesTableGenerator")
    @GenericGenerator(name="entitiesTableGenerator", strategy="com.ecco.infrastructure.entity.UseExistingOrGenerateTableGenerator", parameters={
            @Parameter(name="table_name", value="hibernate_sequences"),
            @Parameter(name="increment_size", value="1"),
            @Parameter(name="optimizer", value="none"),
            @Parameter(name="initial_value", value=""+ENTITIES_INIT),
            @Parameter(name="segment_value", value="entities")})
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Long id = null;

    public AbstractLongKeyedEntity() {
    }

    protected AbstractLongKeyedEntity(Long id) {
        this.id = id;
    }


    /** See {@link AbstractIntKeyedEntity#getId()} for details on use of generators */
    @Override
    public Long getId() {
        return id;
    }

    // ideally id should be private and we use business keys for identification
    // however its public because we need to set it in places in multiId code
    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
