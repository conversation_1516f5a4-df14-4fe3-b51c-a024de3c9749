package com.ecco.dom;


public enum YesNoDontKnow implements IdName<Integer> {

    Unknown(-1), No(0), Yes(1), DontKnow(2);

    int value;

    private YesNoDontKnow(int value) {
        this.value = value;
    }

    public int toInt() {return value;}

    public static YesNoDontKnow fromInt(int value) {
        switch (value) {
            case 0: return No;
            case 1: return Yes;
            case 2: return DontKnow;
            default: return Unknown;
        }
    }

    @Override
    public Integer getId() {
        return value;
    }
    @Override
    public String getName() {
        if (value == 0) {
            return "no";
        }
        if (value == 1) {
            return "yes";
        }
        if (value == 2) {
            return "don't know";
        }
        return "unknown";
    }

}
