package com.ecco.infrastructure.platform;

import org.hamcrest.MatcherAssert;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;

/**
 * We need to run on at least Java 17.
 */
public class JreVersionTest {
    @Test
    public void testJavaSpecificationVersionAtLeast17() {
        String javaVersion = System.getProperty("java.specification.version");
        BigDecimal parsedVersion = new BigDecimal(javaVersion);
        MatcherAssert.assertThat("Expect version at least 17", parsedVersion, greaterThanOrEqualTo(new BigDecimal("17")));
    }

    @Test
    public void testJava17ClassesAreAvailable() throws ClassNotFoundException {
        Assert.assertNotNull("Expect record base class", Class.forName("java.lang.Record"));
    }

}
