package com.ecco.infrastructure.util;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.junit.Assert.*;

import org.junit.Test;


public class FlagMapTest {

    @Test
    public void singleEntryShouldReturnOneEntry() {

        FlagMap flags =new FlagMap(
                " something (with comma\\, spaces) in it ");

        assertThat(flags.keySet(), contains(
                "something (with comma, spaces) in it"));
    }

    @Test
    public void listWithCommasSpacesAndBracketsShouldSplitCorrectly() {

        FlagMap flags =new FlagMap(
                " precedingSpace,simpleComma ,spaceComma, commaSpace, " +
                "something (with comma\\, spaces) in it, the end with spaces on end ");

        assertThat(flags.keySet(), contains(
                "precedingSpace", "simpleComma", "spaceComma", "commaSpace",
                "something (with comma, spaces) in it", "the end with spaces on end"));
    }

    @Test
    public void nullValueShouldHaveNoEntries() {

        FlagMap flags =new FlagMap(null);

        assertThat(flags.keySet(), empty());
    }
    @Test
    public void emptyStringShouldHaveNoEntries() {

        FlagMap flags =new FlagMap("");

        assertThat(flags.keySet(), empty());
    }
}
