package com.ecco.dom.notifications;

import lombok.*;
import org.hibernate.annotations.Type;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;

/**
 * A record of notifications ready for users
 */
@Entity
@Table(name = "usr_notifications")
@IdClass(NotificationRecipient.CompositeKey.class)
@Getter
@Setter
public class NotificationRecipient {

    @Id
    @Column(name = "commandUuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID commandUuid;

    /**
     * Gets the ID of the user who should receive this notification
     */
    @Id
    @Column(nullable = false)
    private long userId;

    @DateTimeFormat
    @Column(nullable = false)
    private Instant created;

    /**
     * When the notification was read by the user (if it has been read)
     */
    @DateTimeFormat
    @Column
    private Instant readAt;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class CompositeKey implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private UUID commandUuid;
        private long userId;
    }
}
