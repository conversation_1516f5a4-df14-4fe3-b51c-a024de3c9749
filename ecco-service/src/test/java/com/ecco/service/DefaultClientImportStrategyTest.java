package com.ecco.service;

import com.ecco.dto.ClientDefinition;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.bus.Subscriber;
import com.ecco.service.event.HttpTunnelDownEvent;
import com.ecco.service.event.HttpTunnelEvent;
import com.ecco.service.event.HttpTunnelResponseEvent;
import com.ecco.service.event.HttpTunnelUpEvent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.atomic.AtomicReference;

import static com.ecco.config.service.ExternalSystemService.ApiType.TUNNEL_WITH_FALLBACK;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.same;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;

/**
 * @since 23/08/2016
 */
@RunWith(MockitoJUnitRunner.class)
public class DefaultClientImportStrategyTest {
    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String GENDER = "115";

    @Mock
    private MessageBus<HttpTunnelEvent> messageBus;
    @Mock
    private WebSocketSession webSocketSession;
    @Captor
    private ArgumentCaptor<Subscriber<HttpTunnelUpEvent>> upEventSubscriber;
    @Captor
    private ArgumentCaptor<Subscriber<HttpTunnelDownEvent>> downEventSubscriber;
    @Captor
    private ArgumentCaptor<Subscriber<HttpTunnelResponseEvent>> responseEventSubscriber;
    @Captor
    private ArgumentCaptor<TextMessage> requestMessage;

    private DefaultClientImportStrategy defaultClientImportStrategy;

    @BeforeClass
    public static void speedUpTimeout() {
        WebSocketHttpRequestFactory.TIMEOUT_SECS = 1;
    }

    @AfterClass
    public static void normalTimeout() {
        WebSocketHttpRequestFactory.TIMEOUT_SECS = 10;
    }

    @Before
    public void setUp() {
        doNothing().when(messageBus).subscribe(same(HttpTunnelUpEvent.class), upEventSubscriber.capture());
        doNothing().when(messageBus).subscribe(same(HttpTunnelDownEvent.class), downEventSubscriber.capture());
        doNothing().when(messageBus).subscribe(same(HttpTunnelResponseEvent.class), responseEventSubscriber.capture());
        defaultClientImportStrategy = new DefaultClientImportStrategy(messageBus);
    }

    @Test
    public void testStrategySupport() {
        assertTrue(defaultClientImportStrategy.supports(TUNNEL_WITH_FALLBACK));
    }

    @Test
    public void testQueryThroughTunnelWithNoResults() throws IOException, InterruptedException {
        String sourceName = "bianca";

        // Respond to a request (we'll check the request later).
        doAnswer(invocation -> {
            final TextMessage message = invocation.getArgument(0);
            final JsonNode parsed = objectMapper.readTree(message.getPayload());
            final String correlationId = parsed.get("correlationId").asText();
            responseEventSubscriber.getValue().receive(new HttpTunnelResponseEvent(this, webSocketSession, sourceName,
                    "{\"correlationId\":\"" + correlationId + "\", \"statusCode\":200, \"contentType\":\"application/json\", \"body\": \"[]\"}"));
            return null;
        }).when(webSocketSession).sendMessage(requestMessage.capture());

        Iterable<ClientDefinition> queryResults = queryResults(sourceName);

        // Check everything
        assertFalse("Expect no results", queryResults.iterator().hasNext());

        final TextMessage message = requestMessage.getValue();
        final JsonNode parsed = objectMapper.readTree(message.getPayload());
        final String correlationId = parsed.get("correlationId").asText();
        assertNotNull("Expect a correlation ID", correlationId);
        final ClientDefinition request = objectMapper.readValue(parsed.get("body").asText(), ClientDefinition.class);
        assertEquals("Expect exemplar external system", sourceName, request.getExternalClientSource());
        assertEquals("Expect exemplar first name", "Jack", request.getFirstName());
        assertEquals("Expect exemplar last name", "Whitehall", request.getLastName());
    }

    @Test
    public void testQueryThroughTunnelWithSomeResults() throws IOException, InterruptedException {
        String sourceName = "bianca";

        // Respond to a request (we'll check the request later).
        doAnswer(invocation -> {
            final TextMessage message = invocation.getArgument(0);
            final JsonNode parsed = objectMapper.readTree(message.getPayload());
            final String correlationId = parsed.get("correlationId").asText();
            final ClientDefinition request = objectMapper.readValue(parsed.get("body").asText(), ClientDefinition.class);
            final ClientDefinition[] responseBody = {
                    ClientDefinition.BuilderFactory.create()
                            .firstName("Vamsi")
                            .lastName("Patel")
                            .externalClientSource(request.getExternalClientSource())
                            .genderKey(GENDER)
                            .build()
            };
            final ObjectNode response = objectMapper.createObjectNode();
            response.put("correlationId", correlationId);
            response.put("statusCode", 200);
            response.put("contentType", MediaType.APPLICATION_JSON_VALUE);
            response.put("body", objectMapper.writeValueAsString(responseBody));

            responseEventSubscriber.getValue().receive(new HttpTunnelResponseEvent(this, webSocketSession, sourceName,
                    objectMapper.writeValueAsString(response)));
            return null;
        }).when(webSocketSession).sendMessage(requestMessage.capture());

        Iterable<ClientDefinition> queryResults = queryResults(sourceName);

        // Check everything
        assertTrue("Expect some results", queryResults.iterator().hasNext());
        final ClientDefinition result = queryResults.iterator().next();
        assertEquals("Expect result first name", "Vamsi", result.getFirstName());
        assertEquals("Expect result last name", "Patel", result.getLastName());
        assertEquals("Expect result gender", GENDER, result.getGenderKey());
        assertEquals("Expect result external system", sourceName, result.getExternalClientSource());
    }

    @Test(expected = HttpStatusCodeException.class)
    public void testIgnoresUnknownCorrelationId() throws IOException, InterruptedException {
        String sourceName = "bianca";

        // Respond to a request (we'll check the request later).
        doAnswer(invocation -> {
            responseEventSubscriber.getValue().receive(new HttpTunnelResponseEvent(this, webSocketSession, sourceName,
                    "{\"correlationId\":\"f398fef7-6d89-4bd6-8214-1254ff408575\", \"statusCode\":200, \"contentType\":\"application/json\", \"body\": \"[]\"}"));
            return null;
        }).when(webSocketSession).sendMessage(requestMessage.capture());

        queryResults(sourceName);
    }

    @Test(expected = HttpStatusCodeException.class)
    public void testQueryThroughTunnelWithTimeout() throws IOException, InterruptedException {
        String sourceName = "bianca";

        // Don't respond to a request
        doNothing().when(webSocketSession).sendMessage(requestMessage.capture());

        queryResults(sourceName);
    }

    /** Do the query on another thread and wait for it. */
    private Iterable<ClientDefinition> queryResults(String sourceName) throws InterruptedException {
        AtomicReference<Iterable<ClientDefinition>> queryResults = new AtomicReference<>();

        final Thread thread = new Thread(() -> {
            // Bring the tunnel up
            upEventSubscriber.getValue().receive(new HttpTunnelUpEvent(this, webSocketSession, sourceName));
            // Query the source
            final Iterable<ClientDefinition> results = defaultClientImportStrategy
                    .queryClients(sourceName, TUNNEL_WITH_FALLBACK, URI.create("http://externalsystem/api/"),
                            ClientDefinition.BuilderFactory.create().firstName("Jack").lastName("Whitehall").build());
            // Take the tunnel down
            downEventSubscriber.getValue().receive(new HttpTunnelDownEvent(this, webSocketSession, sourceName));
            // Expose the results to the parent thread
            queryResults.set(results);
        });
        thread.run();
        thread.join();
        return queryResults.get();
    }
}
