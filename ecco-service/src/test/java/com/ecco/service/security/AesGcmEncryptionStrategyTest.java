package com.ecco.service.security;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.junit.BeforeClass;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.KeyGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.Provider;
import java.security.Security;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AesGcmEncryptionStrategyTest {
    final Logger log = LoggerFactory.getLogger(getClass());

    AesGcmEncryptionStrategy aesGcmEncryptionStrategy = new AesGcmEncryptionStrategy();
    byte[] randomSourceData = new byte[8192];

    public static final long SEED = 56275638975328L;

    {
        Random random = new Random(SEED);
        random.nextBytes(randomSourceData); // Generate 8K of random data to encrypt.
    }

    @BeforeClass
    public static void registerBouncyCastle() {
        Security.addProvider(new BouncyCastleProvider());
    }

    @Test
    public void testRoundTrippingAES_128() throws NoSuchAlgorithmException {
        assertTrue(aesGcmEncryptionStrategy.supports(EncryptionManagerService.CIPHER_AES_GCM));
        final Provider[] providers = Security.getProviders();
        log.info("Providers: " + Arrays.asList(providers).toString());
        final Set<String> ciphers = new HashSet<String>();
        for (Provider provider : providers) {
            for (Provider.Service service : provider.getServices()) {
                if ("Cipher".equals(service.getType())) {
                    ciphers.add(service.getAlgorithm());
                }
            }
        }
        log.info("Cipher algorithms: " + ciphers);
        final KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(128);
        byte[] key = keyGenerator.generateKey().getEncoded();
        byte[] encryptedData = aesGcmEncryptionStrategy.encrypt(EncryptionManagerService.CIPHER_AES_GCM, key, randomSourceData);
        assertFalse(Arrays.equals(randomSourceData, encryptedData));
        byte[] outputData = aesGcmEncryptionStrategy.decrypt(EncryptionManagerService.CIPHER_AES_GCM, key, encryptedData);
        assertArrayEquals(randomSourceData, outputData);
    }
}
