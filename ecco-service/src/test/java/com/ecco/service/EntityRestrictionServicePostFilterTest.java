package com.ecco.service;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.security.acl.AclHandler;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test to verify that @PostFilter annotations work correctly when methods are called
 * from within the same class (self-invocation). This tests the fix for the Spring AOP
 * limitation where self-invocation bypasses proxy-based aspects.
 */
@RunWith(MockitoJUnitRunner.class)
public class EntityRestrictionServicePostFilterTest {

    @Mock
    private AclHandler aclHandler;

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private RepositoryBasedServiceCategorisationService svcCatsService;

    @Mock
    private EntityRestrictionService proxiedService;

    private EntityRestrictionServiceImpl entityRestrictionService;

    @Before
    public void setUp() {
        entityRestrictionService = new EntityRestrictionServiceImpl(
            aclHandler, serviceRepository, projectRepository, applicationContext);

        // Set up authentication context for security tests
        TestingAuthenticationToken auth = new TestingAuthenticationToken("testuser", "password", "ROLE_USER");
        SecurityContextHolder.getContext().setAuthentication(auth);
    }

    @Test
    public void testGetRestrictedServicesProjectsDto_UsesProxiedService() {
        // Given
        List<ServiceAclId> mockServices = Arrays.asList(
            new ServiceAclId(1L, "Service 1"),
            new ServiceAclId(2L, "Service 2")
        );

        List<ProjectAclId> mockProjects = Arrays.asList(
            new ProjectAclId(10L, "Project 1"),
            new ProjectAclId(20L, "Project 2")
        );

        List<ServiceCategorisationViewModel> mockSvcCats = Collections.emptyList();

        // Mock the ApplicationContext to return the proxied service
        when(applicationContext.getBean(EntityRestrictionService.class)).thenReturn(proxiedService);

        // Mock the proxied service methods (these would have @PostFilter applied)
        when(proxiedService.getRestrictedServiceIds()).thenReturn(mockServices);
        when(proxiedService.getRestrictedProjectIds()).thenReturn(mockProjects);

        // Mock the service categorisation service
        when(svcCatsService.getServiceCategorisationViewModels()).thenReturn(mockSvcCats);

        // When
        ServicesProjectsDto result = entityRestrictionService.getRestrictedServicesProjectsDto(svcCatsService);

        // Then
        assertNotNull("Result should not be null", result);

        // Verify that the proxied service was used instead of direct method calls
        verify(applicationContext).getBean(EntityRestrictionService.class);
        verify(proxiedService).getRestrictedServiceIds();
        verify(proxiedService).getRestrictedProjectIds();

        // Verify that the direct repository methods were NOT called (which would bypass @PostFilter)
        verify(serviceRepository, never()).findAllAclIds();
        verify(projectRepository, never()).findAllAclIds();

        // Verify the service categorisation service was called
        verify(svcCatsService).getServiceCategorisationViewModels();
    }

    @Test
    public void testGetRestrictedServicesProjectsDto_HandlesEmptyResults() {
        // Given
        List<ServiceAclId> emptyServices = Collections.emptyList();
        List<ProjectAclId> emptyProjects = Collections.emptyList();
        List<ServiceCategorisationViewModel> emptySvcCats = Collections.emptyList();

        when(applicationContext.getBean(EntityRestrictionService.class)).thenReturn(proxiedService);
        when(proxiedService.getRestrictedServiceIds()).thenReturn(emptyServices);
        when(proxiedService.getRestrictedProjectIds()).thenReturn(emptyProjects);
        when(svcCatsService.getServiceCategorisationViewModels()).thenReturn(emptySvcCats);

        // When
        ServicesProjectsDto result = entityRestrictionService.getRestrictedServicesProjectsDto(svcCatsService);

        // Then
        assertNotNull("Result should not be null even with empty data", result);
        verify(proxiedService).getRestrictedServiceIds();
        verify(proxiedService).getRestrictedProjectIds();
    }

    @Test
    public void testDirectMethodCallsStillWork() {
        // Given
        List<ServiceAclId> mockServices = Arrays.asList(new ServiceAclId(1L, "Service 1"));
        // Use ArrayList to create a mutable list since getRestrictedProjectIds() adds to the list
        List<ProjectAclId> mockProjects = new ArrayList<>(Arrays.asList(new ProjectAclId(10L, "Project 1")));

        when(serviceRepository.findAllAclIds()).thenReturn(mockServices);
        when(projectRepository.findAllAclIds()).thenReturn(mockProjects);

        // When - calling methods directly (not through getRestrictedServicesProjectsDto)
        List<ServiceAclId> services = entityRestrictionService.getRestrictedServiceIds();
        List<ProjectAclId> projects = entityRestrictionService.getRestrictedProjectIds();

        // Then
        assertNotNull("Services should not be null", services);
        assertNotNull("Projects should not be null", projects);

        // Note: In a real scenario with security enabled, these would be filtered by @PostFilter
        // but in this unit test, we're just verifying the methods can be called directly
        verify(serviceRepository).findAllAclIds();
        verify(projectRepository).findAllAclIds();
    }

    @Test
    public void testSelfProxyPattern_PreventsBypassOfSecurity() {
        // This test demonstrates that our fix prevents the bypass of @PostFilter
        // by ensuring that internal calls go through the proxied service

        // Given
        when(applicationContext.getBean(EntityRestrictionService.class)).thenReturn(proxiedService);
        when(proxiedService.getRestrictedServiceIds()).thenReturn(Collections.emptyList());
        when(proxiedService.getRestrictedProjectIds()).thenReturn(Collections.emptyList());
        when(svcCatsService.getServiceCategorisationViewModels()).thenReturn(Collections.emptyList());

        // When
        entityRestrictionService.getRestrictedServicesProjectsDto(svcCatsService);

        // Then - verify the proxy was used, not direct repository access
        verify(applicationContext).getBean(EntityRestrictionService.class);
        verify(proxiedService).getRestrictedServiceIds();
        verify(proxiedService).getRestrictedProjectIds();

        // Most importantly: verify that direct repository calls were avoided
        verify(serviceRepository, never()).findAllAclIds();
        verify(projectRepository, never()).findAllAclIds();
    }
}
