package com.ecco.service;

import com.ecco.buildings.dom.BuildingServiceRecipient;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.dom.QFixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.buildings.viewModel.BuildingToViewModel;
import com.ecco.buildings.viewModel.BuildingViewModel;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.querydsl.core.BooleanBuilder;

import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import org.jspecify.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


/**
 * Implementation of BuildingService that handles building operations
 * with proper transaction management and lazy loading resolution.
 * Returns ViewModels to ensure proper session management.
 */
@Service("buildingService")
@ReadOnlyTransaction
public class BuildingServiceImpl implements BuildingService {

    public static final int CARERUN_RESOURCE_ID = 158;

    private final ServiceRecipientRepository serviceRecipientRepository;
    private final FixedContainerRepository fixedContainerRepository;
    private final BuildingToViewModel buildingToViewModel;

    public BuildingServiceImpl(FixedContainerRepository fixedContainerRepository,
                              ServiceRecipientRepository serviceRecipientRepository,
                              BuildingToViewModel buildingToViewModel) {
        this.fixedContainerRepository = fixedContainerRepository;
        this.buildingToViewModel = buildingToViewModel;
        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    @Nullable
    @Transactional(propagation = Propagation.REQUIRED, readOnly = true)
    public BuildingViewModel findBuildingViewModelById(Integer buildingId) {
        return fixedContainerRepository.findById(buildingId)
                .map(this::initializeAndConvert)
                .orElse(null);
    }

    /**
     * Initializes lazy-loaded associations and converts to ViewModel within transaction boundary.
     * This prevents "could not initialize proxy" errors by ensuring all required data is loaded
     * before the conversion happens.
     */
    private BuildingViewModel initializeAndConvert(FixedContainer container) {
        //var srId = container.getServiceRecipientId();
        //var sr = serviceRecipientRepository.findById(srId).orElse(null);
        //var srB = (BuildingServiceRecipient) sr;
        //container.setServiceRecipient(srB);
        /*// Force initialization of serviceRecipient to prevent lazy loading issues
        // This should work now that we have REQUIRED propagation ensuring an active transaction
        if (container.getServiceRecipient() != null) {
            // Access a property to trigger initialization within the active transaction
            container.getServiceRecipient().getServiceAllocationId();
        }*/
        return buildingToViewModel.toViewModel(container);
    }

    @Override
    @Nullable
    public BuildingViewModel findBuildingViewModelByServiceRecipientId(int serviceRecipientId) {
        return fixedContainerRepository.findByServiceRecipient_Id(serviceRecipientId)
                .map(this::initializeAndConvert)
                .orElse(null);
    }

    @Override
    @Nullable
    public BuildingViewModel findBuildingViewModelByExternalRef(String externalRef) {
        return fixedContainerRepository.findByExternalRef(externalRef)
                .map(this::initializeAndConvert)
                .orElse(null);
    }

    @Override
    public List<BuildingViewModel> findBuildingViewModelsByIds(List<Integer> buildingIds) {
        return fixedContainerRepository.findAllByIdIn(buildingIds)
                .stream()
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findChildBuildingViewModels(int parentBuildingId) {
        return fixedContainerRepository.findAllByParentId(parentBuildingId)
                .stream()
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findBuildingViewModelsWithFilters(boolean showChildren, boolean showDisabled,
                                                                    String[] resourceTypes, List<Integer> addressLocationIds,
                                                                    Integer parentId) {
        Iterable<FixedContainer> containers = getFixedContainers(showChildren, showDisabled, resourceTypes, addressLocationIds, parentId);
        return StreamSupport.stream(containers.spliterator(), false)
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findCareRunViewModels(int parentId, int careRunResourceId) {
        return fixedContainerRepository.findAllByParentIdAndResourceTypeId(parentId, careRunResourceId)
                .stream()
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findBuildingViewModelsHierarchical(boolean showChildren, boolean showDisabled,
                                                                     String[] resourceTypes, List<Integer> addressLocationIds) {
        Iterable<FixedContainer> containers = getFixedContainersHierarchical(showChildren, showDisabled, resourceTypes, addressLocationIds);
        return StreamSupport.stream(containers.spliterator(), false)
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findBuildingViewModelsHierarchical(int buildingId) {
        Iterable<FixedContainer> containers = getFixedContainersHierarchicalByBuildingId(buildingId);
        return StreamSupport.stream(containers.spliterator(), false)
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    public List<BuildingViewModel> findBuildingViewModelsWithPagination(PageRequest pageRequest) {
        Iterable<FixedContainer> containers = fixedContainerRepository.findAll(pageRequest);
        return StreamSupport.stream(containers.spliterator(), false)
                .map(this::initializeAndConvert)
                .collect(Collectors.toList());
    }

    @Override
    @WriteableTransaction
    public void deleteBuilding(Integer buildingId) {
        fixedContainerRepository.deleteById(buildingId);
    }

    private Iterable<FixedContainer> getFixedContainersHierarchical(boolean showChildren, boolean showDisabled,
                                                                   String[] resourceTypes, List<Integer> addressLocationIds) {
        var bldgsAtLocation = getFixedContainers(showChildren, showDisabled, resourceTypes, addressLocationIds, null);
        List<FixedContainer> results = new ArrayList<>();
        bldgsAtLocation.forEach(b -> {
            results.add(b);
            buildHierarchy(b, results);
        });
        return results;
    }

    private void buildHierarchy(FixedContainer node, List<FixedContainer> result) {
        // find all buildings inside
        // ListDefinitionEntry.BUILDING_RESOURCETYPE_ID
        var resourceTypeNames = new String[]{"building", "room"};
        var allBldgs = getFixedContainers(true, false, resourceTypeNames, null, node.getId());

        for (FixedContainer child : allBldgs) {
            if (child.getParent() != null && child.getParent().getId().equals(node.getId())) {
                result.add(child);
                buildHierarchy(child, result);
            }
        }
    }

    private Iterable<FixedContainer> getFixedContainers(boolean showChildren, boolean showDisabled,
                                                       String[] resourceTypes, List<Integer> addressLocationIds,
                                                       Integer parentId) {
        var p = new BooleanBuilder();
        var bldg = QFixedContainer.fixedContainer;

        if (parentId != null) {
            p.and(bldg.parentId.eq(parentId));
        }
        if (addressLocationIds != null && !addressLocationIds.isEmpty()) {
            p.and(bldg.locationId.in(addressLocationIds));
        }
        if (showChildren) {
            if (resourceTypes != null) {
                p.and(bldg.resourceType.name.in(resourceTypes));
            } else {
                // if we're not specific, just don't load careruns
                p.and(bldg.resourceTypeId.ne(getCareRunResourceId()));
            }
        } else {
            p.and(bldg.parent.isNull());
            if (resourceTypes != null) {
                p.and(bldg.resourceType.name.in(resourceTypes));
            } else {
                // if we're not specific, just don't load careruns
                p.and(bldg.resourceTypeId.ne(getCareRunResourceId()));
            }
        }

        if (!showDisabled) {
            p.and(bldg.disabled.isFalse());
        }
        return fixedContainerRepository.findAll(p);
    }

    private Integer getCareRunResourceId() {
        return CARERUN_RESOURCE_ID;
    }

    /**
     * Finds the building and its children 'building' or 'room' hierarchically by building ID.
     * This is used by ReferralController to find all buildings in a hierarchy for referral queries.
     * NB we did load all buildings/rooms and then sort out
     * but we often just need the hierarchy inside one parent
     * and it seems more performant to do a few queries than to load all buildings
     * NB this is from Addresses (1) work.
     */
    private Iterable<FixedContainer> getFixedContainersHierarchicalByBuildingId(int buildingId) {
        // find the starting node
        FixedContainer startNode = fixedContainerRepository.findById(buildingId).orElse(null);
        if (startNode == null) {
            return new ArrayList<>(); // return empty if starting node is not found
        }

        // build the hierarchical structure from the starting node
        List<FixedContainer> rootNodes = new ArrayList<>();
        rootNodes.add(startNode);
        buildHierarchy(startNode, rootNodes);

        return rootNodes;
    }

}
