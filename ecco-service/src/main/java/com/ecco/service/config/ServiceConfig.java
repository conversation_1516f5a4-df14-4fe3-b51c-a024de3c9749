package com.ecco.service.config;


import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.EccoDaoConfig;
import com.ecco.dom.agreements.AppointmentTypeRepository;
import com.ecco.evidence.EvidenceConfig;
import com.ecco.evidence.TaskDefinitionNameIdResolver;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.referral.ReferralCloseScheduleSyncAgent;
import com.ecco.referral.ReferralCloseWorkflowSyncAgent;
import com.ecco.referral.ReferralWorkflowExternalSyncAgent;
import com.ecco.security.config.SecurityConfig;
import com.ecco.service.ClientSyncStrategy;
import com.ecco.service.TaskDefinitionService;
import com.ecco.service.event.DeleteRequestEvidenceWorkflowAgent;
import com.ecco.service.event.ExternalSystemSyncAgent;
import com.ecco.serviceConfig.config.ServiceConfigConfig;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.workflow.WorkflowService;
import com.ecco.calendar.config.CalendarServiceConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.*;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Configuration(proxyBeanMethods = false)
@Import({SecurityConfig.class, ServiceConfigConfig.class, EccoDaoConfig.class,
    EvidenceConfig.class, CalendarServiceConfig.class})
@EnableJpaRepositories(
        repositoryBaseClass=QueryDslJpaEnhancedRepositoryImpl.class,
        basePackageClasses= {ClientRepository.class, AppointmentTypeRepository.class})
// TODO this picks up SettingsServiceImpl but should be put in configconfig
@ComponentScans({
        @ComponentScan(basePackageClasses = com.ecco.service.ClientDetailService.class, includeFilters = @Filter(Service.class)),
        @ComponentScan(basePackageClasses = com.ecco.service.upload.UploadServiceImpl.class)
})
public class ServiceConfig {

    @Bean
    public ExternalSystemSyncAgent clientCreatedSyncAgent(ExternalSystemRepository externalSystemRepository,
                                                          MessageBus<ApplicationEvent> messageBus,
                                                          Collection<ClientSyncStrategy> delegates,
                                                          ClientRepository clientRepository,
                                                          ServiceRecipientRepository srr,
                                                          ServiceRepository serviceRepository) {
        return new ExternalSystemSyncAgent(externalSystemRepository, messageBus, delegates, clientRepository, srr, serviceRepository);
    }

    @Bean
    public ReferralCloseScheduleSyncAgent referralClosureScheduleSyncAgent(DemandScheduleRepository demandScheduleRepository,
                                                                   MessageBus<ApplicationEvent> messageBus) {
        return new ReferralCloseScheduleSyncAgent(demandScheduleRepository, messageBus);
    }

    @Bean
    public ReferralWorkflowExternalSyncAgent referralWorkflowSyncAgent(MessageBus<ApplicationEvent> messageBus) {
        return new ReferralWorkflowExternalSyncAgent(messageBus);
    }

    @Bean
    public ReferralCloseWorkflowSyncAgent referralClosureWorkflowSyncAgent(TaskStatusRepository taskStatusRepository,
                                                                           MessageBus<ApplicationEvent> messageBus) {
        return new ReferralCloseWorkflowSyncAgent(taskStatusRepository, messageBus);
    }

    @Bean
    public DeleteRequestEvidenceWorkflowAgent deleteRequestEvidenceWorkflowAgent(MessageBus<ApplicationEvent> messageBus,
                                                                     EntityUriMapper entityUriMapper,
                                                                     @Qualifier("activitiWorkflow") WorkflowService workflowService) {
        return new DeleteRequestEvidenceWorkflowAgent(messageBus, entityUriMapper, workflowService);
    }

    @Bean
    public TaskDefinitionNameIdResolver taskDefIdNameResolver(TaskDefinitionService taskDefinitionService) {
        return new com.ecco.service.TaskDefinitionNameIdResolver(taskDefinitionService);
    }

}
