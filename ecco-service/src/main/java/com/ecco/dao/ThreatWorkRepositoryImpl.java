package com.ecco.dao;

import com.ecco.dom.QContactImpl;
import com.ecco.dom.QEvidenceThreatComment;
import com.ecco.dom.QEvidenceThreatWork;
import com.ecco.evidence.dom.AssociatedAction;
import com.ecco.evidence.repositories.ThreatActionRepository;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.google.common.collect.*;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Slice;
import org.springframework.data.querydsl.QPageRequest;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Custom implementation to take advantage of using ..Summary classes which avoid eager pull in of data we
 * don't need.
 */
public class ThreatWorkRepositoryImpl implements ThreatWorkRepositoryCustom {

    @Autowired
    private ThreatActionRepository actionRepository;

    @Autowired
    private ThreatFlagRepository flagRepository;

    @Autowired
    private EvidenceSupportWorkRepository supportWorkRepository;

    @Autowired
    private RiskAreaEvidenceRepository riskAreaRepository;

    @Autowired
    private EvidenceAttachmentRepository evidenceAttachmentRepository;

    @PersistenceContext
    private EntityManager em;

    /** For performance we get everything by referralId and then organise into ThreatWorkSummary */
    @Override
    public Slice<ThreatWorkSummary> findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(int serviceRecipientId,
                                                                                                      QPageRequest pr,
                                                                                                      boolean attachmentsOnly) {
        return findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(Collections.singleton(serviceRecipientId), null, pr, attachmentsOnly);
    }

    public Slice<ThreatWorkSummary> findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(Set<Integer> siblingServiceRecipientIds,
                                                                                                      QPageRequest pr,
                                                                                                      boolean attachmentsOnly) {
        return findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(siblingServiceRecipientIds, null, pr, attachmentsOnly);
    }

    @Override
    public Optional<ThreatWorkSummary> findOneThreatWorkSummaryWithActions(Integer serviceRecipientId, UUID uuid) {
        final Slice<ThreatWorkSummary> workSummaries = findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(
                Collections.singleton(serviceRecipientId), uuid, null, false);

        return workSummaries.hasContent()
                ? Optional.of(workSummaries.getContent().get(0))
                : Optional.empty();
    }

    private Slice<ThreatWorkSummary> findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(Set<Integer> siblingServiceRecipientIds,
                                                                                                       @Nullable UUID workUuid,
                                                                                                       QPageRequest pr,
                                                                                                       boolean attachmentsOnly) {
        Slice<ThreatWorkSummary> workSummaries = findAllThreatWorkSummaryByServiceRecipientIds(siblingServiceRecipientIds, workUuid, pr, attachmentsOnly);
        populateWorkSummary(siblingServiceRecipientIds, workSummaries);
        return workSummaries;
    }

    // TODO performance - this loads all risk history (albeit separately) even though we've been given the work uuids
    private void populateWorkSummary(Set<Integer> serviceRecipientIds, final Slice<ThreatWorkSummary> workSummaries) {
        if (workSummaries.hasContent()) {
            final List<EvidenceThreatActionSummary> actions =
                    actionRepository.findAllThreatActionSummaryByServiceRecipientIds(serviceRecipientIds);
            final List<ThreatAreaSummary> areas =
                    riskAreaRepository.findAllThreatAreaSummaryByServiceRecipientIds(serviceRecipientIds);
            final List<EvidenceFlagSummary> flags =
                    flagRepository.findAllThreatFlagSummaryByServiceRecipientIds(serviceRecipientIds);
            final List<EvidenceSupportWorkRiskManagementHandledSummary> handled = supportWorkRepository.findAllSupportWorkWithRiskManagementHandled(serviceRecipientIds);
            // Map it
            final ImmutableListMultimap<UUID, EvidenceThreatActionSummary> actionsByWorkId = Multimaps.index(actions, EvidenceThreatActionSummary::getWorkId);
            final ImmutableListMultimap<UUID,ThreatAreaSummary> areasByWorkId = Multimaps.index(areas, ThreatAreaSummary::getWorkId);
            final ImmutableListMultimap<UUID, EvidenceFlagSummary> flagsByWorkId = Multimaps.index(flags, EvidenceFlagSummary::getWorkId);
            final ImmutableListMultimap<UUID, EvidenceSupportWorkRiskManagementHandledSummary> handledByWorkId
                    = Multimaps.index(handled, EvidenceSupportWorkRiskManagementHandledSummary::getThreatWorkUuid);

            // and again for attachments
            java.util.function.Function<Integer, Stream<EvidenceAttachmentSummary>> toAttachments
                    = id -> evidenceAttachmentRepository.findThreatAttachmentsByServiceRecipientId(id)
                    .stream();
            Stream<EvidenceAttachmentSummary> attachments = serviceRecipientIds.stream()
                    .flatMap(toAttachments )
                    ;

            Map<UUID, List<EvidenceAttachmentSummary>> attachmentsMap = attachments
                    .collect(Collectors.groupingBy(EvidenceAttachmentSummary::getWorkId));

            // and again for associated actions
            List<AssociatedAction> associatedActions =
                    actionRepository.findAllAssociatedActionsByWork_serviceRecipientIds(serviceRecipientIds);
            ImmutableListMultimap<UUID,AssociatedAction> associatedMap = Multimaps.index(associatedActions, input -> input.workId);

            // Munge them together
            for (ThreatWorkSummary work : workSummaries) {
                UUID key = work.getId();
                work.setActions(ImmutableSet.copyOf(actionsByWorkId.get(key)));
                work.setRiskAreas(ImmutableList.copyOf(areasByWorkId.get(key))); // TODO: REVIEW: Do we want Set or List on these?
                work.setFlags(ImmutableList.copyOf(flagsByWorkId.get(key)));
                work.setHandledSupportWorkIds(Lists.transform(handledByWorkId.get(key), EvidenceSupportWorkRiskManagementHandledSummary::getSupportWorkUuid));
                work.setAttachments(attachmentsMap.get(key));
                work.setAssociatedActions(ImmutableSet.copyOf(associatedMap.get(key)));
            }
        }
    }

    /**
     * This method does not populate {@link ThreatWorkSummary#getActions()}.
     * @see #findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(int, QPageRequest, boolean)
     * was:
     * select new com.ecco.dao.ThreatWorkSummary(" +
            " w.id, w.parent.id, a, c.comment, t," +
            " c.minutesSpent, w.signature, w.workDate, w.created" +
            ")" +
            " from evidenceThreatWork w left join w.author a left join w.comment c left join w.comment.type t" +
            " where w.parent.id = ?1" +
            " order by w.workDate DESC, w.created DESC"
     */
    private Slice<ThreatWorkSummary> findAllThreatWorkSummaryByServiceRecipientIds(Set<Integer> siblingServiceRecipientIds,
                                                                                   @Nullable UUID workUuid,
                                                                                   QPageRequest pr,
                                                                                   boolean findAttachmentsOnly) {
        QEvidenceThreatWork workQ = QEvidenceThreatWork.evidenceThreatWork;
        QEvidenceThreatComment commentQ = QEvidenceThreatComment.evidenceThreatComment;
        QContactImpl authorQ = QContactImpl.contactImpl;

        BooleanExpression workUuidExp = workUuid != null ?
                workQ.id.eq(workUuid)
                : null;

        BooleanExpression attachmentsOnlyExp = findAttachmentsOnly ?
                workQ.attachments.isNotEmpty()
                : null;

        // NB its important to use alias in 'leftJoin(source, alias)' since the alias needs to be used
        // in the list(resultObj) to use the left joins, and not use inner joins from the original entity
        // see the test GenericTypeWorkRepositoryTest
        QThreatWorkSummary resultObj = new QThreatWorkSummary(
                workQ.id, workQ.requestedDelete, workQ.taskDefId, workQ.serviceRecipient.id, workQ.serviceRecipient.serviceAllocation.id,
                authorQ, commentQ.comment, commentQ.typeDefId,
                commentQ.minutesSpent, workQ.signature.id, workQ.workDate, workQ.created);

        var query = new JPAQuery<ThreatWorkSummary>(em)
                .from(workQ)
                .select(resultObj)
                .leftJoin(workQ.author, authorQ)
                .leftJoin(workQ.comment, commentQ)
                .orderBy(workQ.workDate.desc())
                .orderBy(workQ.created.desc())
                .where(workQ.serviceRecipient.id.in(siblingServiceRecipientIds),
                        workUuidExp,
                        attachmentsOnlyExp); // null gets ignored

        return QueryDslJpaEnhancedRepositoryImpl.queryAsSlice(query, pr, resultObj);
    }

}
