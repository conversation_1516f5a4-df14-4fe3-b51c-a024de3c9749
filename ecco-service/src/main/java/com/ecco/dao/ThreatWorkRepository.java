package com.ecco.dao;

import com.ecco.dom.EvidenceThreatWork;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import org.jspecify.annotations.NonNull;
import java.util.List;
import java.util.UUID;

public interface ThreatWorkRepository extends JpaRepository<EvidenceThreatWork, UUID>, ThreatWorkRepositoryCustom {

    @Modifying
    @Query("UPDATE EvidenceThreatWork w " +
            "SET w.version = w.version + 1, w.signature.id = :signatureId " +
            "WHERE w.id IN :workIds")
    void attachSignature(@Param("workIds") List<UUID> workIds, @Param("signatureId") UUID signatureId);

    void delete(@NonNull EvidenceThreatWork work);

}
