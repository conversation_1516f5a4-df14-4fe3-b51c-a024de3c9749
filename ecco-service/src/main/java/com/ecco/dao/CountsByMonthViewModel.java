package com.ecco.dao;

import org.jspecify.annotations.Nullable;

import com.querydsl.core.annotations.QueryProjection;


public class CountsByMonthViewModel {

    @Nullable
    public Number entityId;

    public String entityName;

    @Nullable
    public Number entityId2;

    /** Label to show
     * ISO-8601 month as yyyy-MM e.g. 2016-07 for July 2016 */
    public String yearMonth;

    public Integer count;


    public CountsByMonthViewModel() {
    }

    /**
     * @param count count of the number of items in that date range
     */
    @QueryProjection
    public CountsByMonthViewModel(Number entityId, String entityName, @Nullable Integer year, @Nullable Integer month, Integer count) {
        this(entityId, entityName, null, year, month, count);
    }

    /**
     * @param entityId an entity that this relates to such as serviceId where grouping by service and date
     * @param entityName the name of the entity being grouped, e.g. service.name
     * @param count count of the number of items in that date range
     */
    @QueryProjection
    public CountsByMonthViewModel(Number entityId, String entityName, @Nullable Number entityId2, @Nullable Integer year, @Nullable Integer month, Integer count) {
        this.entityId = entityId;
        this.entityName = entityName;
        this.entityId2 = entityId2;
        this.count = count;
        if (year != null && month != null) {
            this.yearMonth = year.toString() + "-" + (month < 10 ? "0" : "") + month.toString();
        }
    }
}
