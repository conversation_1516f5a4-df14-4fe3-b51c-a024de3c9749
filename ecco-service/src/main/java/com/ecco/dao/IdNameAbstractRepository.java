package com.ecco.dao;

import com.ecco.infrastructure.entity.IdName;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Optional;

@NoRepositoryBean
public interface IdNameAbstractRepository<T extends IdName> extends JpaRepository<T, Long> {

    T findOneByName(String name);
    List<T> findAllByName(String name);

    Optional<T> findById(long id);
}
