const path = require("path");

const projectDir = __dirname;
const distDir = path.resolve(projectDir, "./dist/");
const debugDir = path.resolve(projectDir, "./debug/");

const mode = "development";
const configuration = {
    mode: mode,
    name: mode === "production" ? "prod" : "dev",
    entry: "./index.ts",
    externals: {
        "application-properties": "applicationProperties"
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                loader: "esbuild-loader",
                options: {
                    // presets: ["@babel/preset-env"]
                    target: "es2015"
                }
            },
            {
                test: /\.css$/i,
                use: ["style-loader", "css-loader"] // style- is for putting stuff into a <style> tag, css-loader is for reading the .css file
            },
            {
                // COPIED from Create-React-App react-scripts/config/webpack.config.js
                // "url" loader works like "file" loader except that it embeds assets
                // smaller than specified limit in bytes as data URLs to avoid requests.
                // A missing `test` is equivalent to a match.
                test: [/\.png$/],
                loader: require.resolve("url-loader"),
                options: {
                    limit: true // Unlimited - don't use file loader
                    // name: 'static/media/[name].[hash:8].[ext]',
                }
            },
            {
                test: /\.svg$/,
                use: {
                    loader: "svg-url-loader",
                    options: {
                        encoding: "base64"
                    }
                }
            },
            {
                test: /\.tsx?$/,
                loader: "esbuild-loader",
                options: {
                    loader: "tsx", // Or 'ts' if you don't need tsx
                    target: "es2015"
                },
                exclude: /(^|\/)node_modules\//
            }
        ]
    },
    resolve: {
        extensions: [".tsx", ".ts", ".js", ".png", ".svg"],
        fallback: {
            stream: require.resolve("stream-browserify")
        }
    },
    output: {
        path: mode === "production" ? distDir : debugDir,
        filename: "ecco-admin.js",
        libraryTarget: "umd",
        devtoolModuleFilenameTemplate: "[resource-path]?[loaders]"
    }
};
module.exports = configuration;
