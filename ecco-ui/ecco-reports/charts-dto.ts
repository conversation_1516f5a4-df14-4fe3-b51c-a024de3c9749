
/** PIE can only be used with other PIEs to make a donut graph */
export enum SeriesType {
    BAR = "BAR",
    LINE = "LINE",
    POINT = "POINT",
    PIE = "PIE",
    DONUT = "DONUT"
}

/** A mix of visible and invisible stages - acts as discriminator for domain classes of a report stage */
export enum StageType {
    CHART = "CHART",
    TABLE = "TABLE",
    MATRIX = "MATRIX",
    ANALYSER = "ANALYSER",
    BADGE = "BADGE",
    AUDIT = "AUDIT",
    CALENDAREVENT = "CALENDAREVENT" /* also things like PUBLISH_TO_WEBSERVICE etc */
}
