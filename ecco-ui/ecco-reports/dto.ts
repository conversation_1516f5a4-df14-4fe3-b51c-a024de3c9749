/** A dto to take the result from a dashboard report.
 *
 * This interface matches the Java class com.ecco.dto.reports.ReportAction. */
export interface ActionDto {
    actionId: number;
    // name of the smart step
    actionName: string;

    outcomeId: number;
    outcomeName: string;

    serviceId: number;
    serviceName: string;

    projectId: number;
    projectName: string;

    author: string;

    // the number of relevant smart steps for this action
    relevant: number;

    // the number of achieved smart steps for this action
    achieved: number;
}

/**
 * Intended to capture the comment of a piece of evidence
 * including supplementary information to categorise it
 * (such as support worker, service, etc)
 */
export interface EvidenceComment {
    /** The support work ID. */
    workId: number;

    /** The referral ID of the referral corresponding to this support work. */
    referralId: number;

    serviceId: number;
    serviceName: string;

    /** The current project the client is on - can be null */
    projectId: number;
    projectName: string;

    /** The client ID of the client who is the subject of this referral */
    clientId: number;
    /** The name of the service user who is the subject of this referral. */
    clientName: string;

    /** The text of the comment */
    comment: string;

    /** The display name of the person who recorded this evidence. */
    authorName: string;

    /** The date the support work was carried out. */
    workDate: string;
    /** The date the support work was created. */
    createdDate: string;
}
