import * as React from "react";
import {<PERSON><PERSON><PERSON><PERSON>and<PERSON>} from "react";
import {IconButton, Menu} from "@eccosolutions/ecco-mui";

interface IconMenuProps {
    id: string;
    /** e.g. "fa fa-user" or alternatively provide an icon component */
    iconClasses?: string | undefined;
    /** React component that renders SVG inside the button */
    iconComponent?: React.ReactElement | undefined;

    color?: "primary" | "secondary" | "inherit" | undefined;

    onClick: MouseEventHandler<any>;
    open: boolean;
    onClose: MouseEventHandler<any>;
}

interface IconMenuState {
    anchorEl: HTMLButtonElement | null;
}

export class IconMenu extends React.Component<IconMenuProps, IconMenuState> {
    constructor(props: IconMenuProps) {
        super(props);
        this.state = {
            anchorEl: null
        };
    }

    private handleMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
        this.setState({anchorEl: event.currentTarget});
        this.props.onClick(event);
    };

    private handleClose = (event: React.MouseEvent) => {
        this.setState({anchorEl: null});
        this.props.onClose(event);
    };

    override render() {
        return (
            <>
                <IconButton
                    aria-owns={this.props.open ? this.props.id : undefined}
                    aria-haspopup="menu"
                    onClick={this.handleMenu}
                    color={this.props.color}
                    component="span" // because we want to nest buttons
                    className={this.props.iconClasses}
                >
                    {this.props.iconComponent}
                </IconButton>
                <Menu
                    id={this.props.id}
                    anchorEl={this.state.anchorEl}
                    anchorOrigin={{
                        vertical: "top",
                        horizontal: "right"
                    }}
                    transformOrigin={{
                        vertical: "top",
                        horizontal: "right"
                    }}
                    open={this.props.open}
                    onClose={this.handleClose}
                >
                    {this.props.children}
                </Menu>
            </>
        );
    }
}
