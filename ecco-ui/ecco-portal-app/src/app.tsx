import {AsyncSessionData, handleLazy, LoadingSpinner, useServicesContext} from "ecco-components";
import {EccoTheme} from "ecco-components-core";
import * as React from "react";
import {useEffect} from "react";
import * as ReactDOM from "react-dom";
import {<PERSON><PERSON>erRouter} from "react-router-dom";
import {initApiClient, sessionDataFn} from "./services"; // Should be pointing at build/offline/router
import {ServicesContextProvider} from "./ServicesContextProvider";
import * as serviceWorker from "./serviceWorkerRegistration";
import {getUserSessionManager, OfflineSyncStatusEvent} from "ecco-offline-data";
import {ApiClient, isOffline} from "ecco-dto";
import {Route, Switch, useParams} from "react-router";
import {Sr2View} from "ecco-components/service-recipient/Sr2View";
import {RouteFallbackWithDiagnostics} from "ecco-components/AppBarBase";

const SrIdViewDefault = () => {
    return <>load my srId and redirect? or a list of my files</>;
};

const SrIdViewWrapper = () => {
    const {srId} = useParams<{srId: string}>();
    const srIdNum = parseInt(srId);
    //const srId = 200975;
    return <Sr2View basepath={`/sr2/${srId}/`} srId={srIdNum} />;
};

export const AppRouter = () => {
    return (
        <Switch>
            {/* ONLINE new wip file - menu is in SrAppBar */}
            <Route path="/sr2/:srId/" component={() => <SrIdViewWrapper />} />
            <Route exact path="/" component={SrIdViewDefault} />

            {/* NB Consider the OFFLINE app - see ecco-staff-app */}

            <RouteFallbackWithDiagnostics />
        </Switch>
    );
};

interface PageProps {
    readonly appPath: string;
}

const Page = ({appPath}: PageProps) => {
    const {sessionData} = useServicesContext();

    const username = sessionData.getDto().username;
    useEffect(() => {
        loginIfNeeded(username);
    }, [username]);

    console.debug(`BrowserRouter basename=${appPath}`);
    return handleLazy(
        <BrowserRouter basename={appPath}>
            {
                !sessionData ? (
                    <LoadingSpinner />
                ) : (
                    <AppRouter />
                ) /* this was scripts/offline/router.tsx */
            }
        </BrowserRouter>
    );
};

interface AppProps {
    readonly appPath: string;
    readonly apiClient: ApiClient;
}

const App = ({appPath, apiClient}: AppProps) => (
    <AsyncSessionData promiseFn={sessionDataFn}>
        <ServicesContextProvider client={apiClient}>
            <EccoTheme prefix="portal-app">
                <Page appPath={appPath} />
            </EccoTheme>
        </ServicesContextProvider>
    </AsyncSessionData>
);

function addToCache(cacheName: string, urls: string[]) {
    return window.caches.open(cacheName).then(cache => cache.addAll(urls));
}

export interface AppOptions {
    readonly appPath: string;
    readonly remoteRoot: string;
}

export function start({appPath, remoteRoot}: AppOptions): void {
    serviceWorker.register(); // Note only active if NODE_ENV is production

    // Ensure we cache what we loaded - could iterate through document <script> tags for this
    window.addEventListener("load", () => {
        // ...determine the list of related URLs for the current page...
        addToCache("public", [
            `${appPath}jquery-3.6.0.min.js`,
            `${appPath}jquery-ui-1.10.3.custom.min.js`,
            `${appPath}jquery-datepicker.js`,
            `${appPath}lazy.min.js`,
            `${appPath}bootstrap.min.js`
        ]);
        addToCache("images", [
            `${appPath}favicon.ico`,
            `${appPath}images/datepicker.png`,
            `${appPath}images/flag_red24.png`,
            `${appPath}images/link.png`,
            `${appPath}images/plus24.png`,
            `${appPath}images/star24.png`,
            `${appPath}images/tick.png`
        ]);
        addToCache("api", [
            // `${remoteRoot}/api/config/user/`, // DONT as app is not logged in at this point
            // `${remoteRoot}/api/config/global` // ditto
        ]);
    });

    const apiClient = initApiClient(remoteRoot);

    ReactDOM.render(
        <App appPath={appPath} apiClient={apiClient} />,
        document.getElementById("appbar") as HTMLElement
    );

    OfflineSyncStatusEvent.initNavigator(navigator);
}

function loginIfNeeded(username: string) {
    const userSessionManager = getUserSessionManager();
    if (userSessionManager) {
        // TODO: We need to avoid this path if using ecco login
        userSessionManager.loginIfNeeded(username, undefined, isOffline());
    }
}
