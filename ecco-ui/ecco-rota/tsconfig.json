{
  "extends": "../tsconfig.ecco-module.json",
  "compilerOptions": {
    "lib": ["es2017.object", "es2015", "dom"],
    "baseUrl": "",
    "outDir": "./build-tsc",
    "declarationDir": "./build-tsc",
    "types": ["@types/jest", "application-properties"]
  },
  "references": [
    { "path": "../ecco-commands/tsconfig.json" }
  ],
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["debug", "dist", "build-tsc", "__tests__"] // Technically shouldn't need to specify debug and dist as they don't have *.tsx? in
}
