{"private": true, "name": "ecco-finance", "version": "0.0.0", "license": "UNLICENSED", "main": "./build-tsc/index.js", "typings": "./build-tsc/index.d.ts", "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Nothing to do", "test-sequential": "yarn cy:test", "cy:test": "cypress run --component", "cy:dev": "cypress open --component"}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-mui": "0.0.0", "@eccosolutions/ecco-mui-controls": "0.0.0", "ecco-components-core": "^0.0.0", "ecco-components": "0.0.0", "ecco-commands": "0.0.0", "ecco-dto": "0.0.0", "react": "16.13.1", "react-dom": "16.13.1", "stream-browserify": "^3.0.0"}, "peerDependencies": {}, "devDependencies": {"@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@testing-library/cypress": "^8.0.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "css-loader": "^6.11.0", "cypress": "^11.2.0", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "style-loader": "^2.0.0", "svg-url-loader": "^7.1.1", "terser-webpack-plugin": "^4.2.3", "typescript": "5.8.3", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.2"}}