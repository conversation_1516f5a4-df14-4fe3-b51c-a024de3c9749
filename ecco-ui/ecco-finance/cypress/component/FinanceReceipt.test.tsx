import * as React from "react";
import {mount} from "cypress/react";
import {
    FinanceReceipt,
    FinanceReceiptCommandForm,
    FinanceReceiptState
} from "../../receipt/FinanceReceipt";
import {FC, useState} from "react";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {CommandForm, useServicesContext} from "ecco-components";
import {Command} from "ecco-commands";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {
    CHARGENAME_LISTDEF,
    FeatureSetDto,
    PersonUserSummary,
    ServiceCategorisation,
    SessionData,
    SessionDataDto
} from "ecco-dto";
import {UserSessionDataDto} from "ecco-dto/session-data/feature-config-dto";
import {EccoAPI} from "ecco-components/EccoAPI";

const sessionDataDto: SessionDataDto = {} as SessionDataDto;
sessionDataDto.featureSets = {global: {} as FeatureSetDto};
sessionDataDto.listDefinitions = {};

sessionDataDto.listDefinitions[CHARGENAME_LISTDEF] = [
    {
        id: 219,
        name: "service charges",
        listName: "chargeNames",
        businessKey: "219",
        disabled: false,
        defaulted: false,
        order: null
    }
];
const serviceCat1: ServiceCategorisation = {
    id: 1,
    serviceId: 1,
    projectId: null,
    disabled: false,
    serviceName: "service1",
    buildingId: 1065 // buildingTestData[0].buildingId
};
const person1: PersonUserSummary = {
    individualId: 1,
    displayName: "my name",
    calendarId: "calId"
} as PersonUserSummary;
sessionDataDto.individualUserSummary = person1;

const userSession: UserSessionDataDto = {
    username: "mystaff",
    restrictedServiceCategorisations: [serviceCat1]
} as UserSessionDataDto;

const sessionData = new SessionData({...sessionDataDto, ...userSession});

const overrides = {
    sessionData: sessionData
} as any as EccoAPI;

const FinanceReceiptLayout: FC = () => {
    const {sessionData} = useServicesContext();
    const [state, setState] = useState<FinanceReceiptState>({
        amount: undefined,
        typeDefId: undefined,
        received: undefined,
        description: undefined
    });
    return <FinanceReceipt data={state} setData={setState} sessionData={sessionData} />;
};

describe("FinanceReceipt layout", () => {
    it("layout", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <FinanceReceiptLayout />
            </TestServicesContextProvider>
        );

        //cy.get('[name*="taskName"]').type("my new task...");
    });
});

describe("FinanceReceipt tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            <FinanceReceiptCommandForm
                                init={{
                                    serviceRecipientId: 99,
                                    receiptId: null,
                                    typeDefId: undefined,
                                    amount: undefined,
                                    received: undefined,
                                    description: undefined
                                }}
                            />
                            <CommandFormTestOutput
                                cmdEmitted={cmdEmitted}
                                cmdEmittedDraft={cmdEmittedDraft}
                            />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        //cy.findByLabelText("due date").should("be.disabled");
    });
});
