import {ApiClient, isOffline, setGlobalApiClient} from "ecco-dto";
import {askUserForCredentials} from "ecco-components";
import {getFeatureConfigRepository} from "ecco-offline-data";

export function initApiClient(remoteRoot: string): ApiClient {
    console.debug(`initialising ApiClient with path ${remoteRoot}/api/`);
    const client = new ApiClient(
        remoteRoot + "/api/",
        askUserForCredentials,
        isOffline, // If true prevents API client login, so that offline-data can handle it
        {
            attemptReAuth: true,
            credentials: window.global_credentials
        }
    );

    setGlobalApiClient(client);

    return client;
}

export const sessionDataFn = () => getFeatureConfigRepository().getSessionData();
