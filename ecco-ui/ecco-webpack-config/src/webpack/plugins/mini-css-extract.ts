import MiniCssExtractPlugin from "mini-css-extract-plugin";
import {ResolvedSinglePageAppOptions} from "../../options/single-page-app";

export function miniCssExtractPlugins(options: ResolvedSinglePageAppOptions) {
    return options.mode === "production"
        ? [
              new MiniCssExtractPlugin({
                  filename: "static/css/[name].[contenthash:8].css",
                  chunkFilename: "static/css/[name].[contenthash:8].chunk.css"
              })
          ]
        : [];
}
