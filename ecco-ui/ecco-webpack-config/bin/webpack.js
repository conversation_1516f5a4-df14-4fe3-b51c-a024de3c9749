#!/usr/bin/env node

// Wrapper for webpack-cli.
// This ensures we get the correct version of webpack by resolving from the
// context of ecco-webpack-config.
// It also compiles ecco-webpack-config on demand if required.

"use strict";

const {fork} = require("child_process");
const {argv, exit} = require("process");

// Compile ecco-webpack-config if required
const compiler = fork(require.resolve("typescript/bin/tsc"), [], {
    cwd: __dirname
});

compiler.on("exit", code => {
    if (code === 0) {
        // Run webpack
        const webpack = fork(require.resolve("webpack/bin/webpack"), argv.slice(2));

        webpack.on("exit", code => {
            exit(code ?? 1);
        });
    } else {
        exit(code ?? 1);
    }
});
