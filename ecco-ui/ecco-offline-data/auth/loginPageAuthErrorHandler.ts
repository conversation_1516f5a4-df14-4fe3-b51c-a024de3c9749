import {AuthenticationException} from "../security/AuthenticationException";
import {getUserSessionManager} from "../core-services";
import {getGlobalApiClient} from "ecco-dto";

/**
 * Attempt to handle error as AuthenticationError. Returns true if it was handled, false if something else should
 * handle it.
 */
export function loginPageAuthErrorHandler(error: Object): boolean {
    if (error instanceof AuthenticationException) {
        getGlobalApiClient().getCredentials()
            .then(credentials => getUserSessionManager()!
                .login(credentials.username, credentials.password, true))
            .then(() => window.location.reload()); // TODO: Or perhaps ReloadEvent.bus.fire()

    }
    return false;
}