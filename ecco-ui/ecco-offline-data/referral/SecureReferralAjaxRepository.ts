import {Encrypted} from "@eccosolutions/ecco-common";
import {ApiClient, ReferralPlainFields, ReferralSecretFields} from "ecco-dto";

/** A referral (encrypted). */
export interface EncryptedReferralDto extends Encrypted<ReferralSecretFields, ReferralPlainFields> {
}

export class SecureReferralAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findAllReferralsForOffline(userDeviceId: string, sinceInstant: string | null): Promise<EncryptedReferralDto[]> {
        const options = sinceInstant ? {query: {sinceInstant}} : undefined
        return this.apiClient.secureGet<EncryptedReferralDto[]>(userDeviceId, "user/_self/referrals/filter/live/", options);
    }
}
