import {bus, EccoDate, EccoTime} from "@eccosolutions/ecco-common";
import {Box, Grid, Paper, Typography} from "@eccosolutions/ecco-mui";
import {TimePickerEccoTime, DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {Agreement, DemandResource, Activity} from "ecco-rota";
import * as React from "react";
import {useAgreements} from "../data/entityLoadHooks";
import {dropdownList, numberInput, textInput} from "ecco-components-core";

import {useServicesContext} from "../ServicesContext";
import {WorkerJobSelector} from "../rota/WorkerJobSelector";

export interface AppointmentFields {
    agreementId?: number | undefined;
    categoryId?: number | undefined;
    date: EccoDate;
    time: EccoTime | null;
    durationMins: number | null;
    resourceSrId: number | null;
    /** title can be changed when editing an appointment */
    title?: string | undefined;
    /** Provide the reference data we used */
    referenceData: {
        agreement?: Agreement | undefined;
    };
}

interface Props {
    serviceId: number | null; // filter the resources to allocate to
    resource?: DemandResource | undefined;
    date: EccoDate;
    /** Don't need these for edit */
    demandFilter?: string | undefined;
    resourceFilter?: string | undefined;
    data: AppointmentFields;
    onChange: (data: AppointmentFields) => void;
    /** Activity is provided when editing */
    activity?: Activity | undefined;
}


export const ScheduleEvent = bus<void>();

export function AppointmentForm(props: Props) {
    const {resource, data, date, demandFilter, resourceFilter} = props;
    const {sessionData} = useServicesContext();

    const {agreements} =
        resourceFilter && date
            ? useAgreements(date, date, resourceFilter, demandFilter!)
            : {agreements: null};

    const filteredTypes = sessionData.getAppointmentTypes();
    // TODO: This is a hack to match AppointmentSchedule - it's unclear why this is like this
    //  .filter(apptType => apptType.serviceId == serviceId);

    const onChange = (data: AppointmentFields) => {
        // Add reference data for use by callee
        const agreement = agreements
            ? agreements.find(a => a.getAgreementId() == data.agreementId)!
            : undefined;

        props.onChange({...data, referenceData: {agreement}});
    };

    return (
        <Box m={2} p={2}>
            <Paper>
                <Box p={2} mb={4}>
                    <Typography variant="h6" component="h2">
                        {props.activity
                            ? `Edit appointment ${
                                  resource && `with ${resource.getName()}`
                              } on ${date.formatPretty()}`
                            : `New ad-hoc appointment`}
                    </Typography>
                </Box>
            </Paper>
            <Grid container direction="row" justify="flex-start" alignItems="flex-start">
                <Grid container>
                    {!props.activity && (
                        <Grid item xs={12}>
                            {agreements &&
                                dropdownList(
                                    "agreement",
                                    onChange,
                                    data,
                                    "agreementId",
                                    agreements.map(a => {
                                        return {
                                            id: a.getAgreementId(),
                                            name: a.getDescription(),
                                            disabled: false
                                        };
                                    }),
                                    {helperText: "Agreements valid on given date"},
                                    undefined,
                                    true
                                )}
                        </Grid>
                    )}
                    {!props.activity && (
                        <Grid item xs={12}>
                            {dropdownList(
                                "type",
                                onChange,
                                data,
                                "categoryId",
                                filteredTypes.map(a => {
                                    return {id: a.id, name: a.name, disabled: false};
                                }),
                                undefined,
                                undefined,
                                true
                            )}
                        </Grid>
                    )}
                    <Grid item sm={6} xs={12}>
                        <DatePickerEccoDate
                            name="date"
                            label="date"
                            value={data.date}
                            onChange={date => onChange({...data, date: date!})}
                            required={true}
                        />
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        <TimePickerEccoTime
                            label="start time"
                            time={data.time}
                            onTimeChange={time => onChange({...data, time})}
                            required={true}
                        />
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        {numberInput("durationMins", "duration (mins)", onChange, data, false, 0)}
                    </Grid>
                    {sessionData.isModuleEnabled("rota") &&
                        sessionData.isEnabled("rota.scheduler") &&
                        props.serviceId && (
                            <Grid item sm={6} xs={12}>
                                <WorkerJobSelector
                                    labelName={"allocate"}
                                    role={"ROLE_CARER"}
                                    serviceIds={[props.serviceId]}
                                    onChange={(
                                        _contactId,
                                        _contactName,
                                        _calendarId,
                                        _workerJobId,
                                        serviceRecipientId
                                    ) => (data.resourceSrId = serviceRecipientId)}
                                    selectedSrId={data.resourceSrId}
                                />
                            </Grid>
                        )}
                    {props.activity && (
                        <Grid item xs={12}>
                            {textInput("title", "title", onChange, data, "event description")}
                        </Grid>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
}
