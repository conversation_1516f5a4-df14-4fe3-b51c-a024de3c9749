import {AdminMode} from "@eccosolutions/ecco-common";
import {applicationRootPath} from "application-properties";
import {AsyncSessionData} from "../AsyncSessionData";
import {
    ServiceRecipientWithEntitiesContext,
    WithLatestCommands
} from "../data/serviceRecipientHooks";
import {
    adminModeEnabled,
    isOffline,
    ServiceType,
    SessionData,
    TaskNames,
    TaskRepository,
    TaskSetting,
    TaskSettingName
} from "ecco-dto";
import {TaskDto, WorkflowDto} from "ecco-dto/workflow-dto";
import * as React from "react";
import {Component} from "react";
import {TaskAudit, translateTaskName} from "./TaskAudit";
import {WorkflowActiveRecord, WorkflowReloadEvent} from "./WorkflowLoader";
import {TaskRow} from "./TaskRow";
import {TaskSummary} from "./TaskSummary";

function isVisibleTask(
    task: TaskDto,
    serviceTypeId: number,
    sessionData: SessionData,
    limitToTaskNames?: string[] | undefined
) {
    const taskName = translateTaskName(task.taskName);
    const taskDef = sessionData.getTaskDefinitionByName(taskName)!;
    console.assert(taskDef != null, `failed to find taskDef by name: ${taskName}`);

    if (limitToTaskNames && limitToTaskNames.indexOf(taskName) == -1) {
        return false;
    }

    // if no limitToTaskNames, then we are the default 'pathway'
    // so check the current task has no 'showOn' somewhere else, if it does, don't display it
    if (!limitToTaskNames) {
        const hideIfShowOnElsewhere: TaskSettingName[] = [
            "showOnAboutMeTab",
            "showOnHealthTab",
            "showOnSupportTab",
            "showOnRiskTab",
            "showFlagsOn"
        ];
        const keepIfShowOnPathway: TaskSettingName[] = ["showOnPathwayTab"];

        const taskSettings = sessionData
            .getServiceTypeById(serviceTypeId)
            .getTaskDefinitionEntry(taskName)
            ?.getSettings()
            ?.filter(s => !!s.getValue()); // don't allow empty (ie turn on, off, setting still exists)
        const hasShowOnSomewhereElse =
            taskSettings &&
            taskSettings.some(s => {
                const hideOnPathway =
                    hideIfShowOnElsewhere.indexOf(s.getName() as TaskSettingName) > -1;
                // ignore showFlagsOn if it's something other than statusArea - it's not relevant
                if (s.getName() == "showFlagsOn" && s.getValue().indexOf("statusArea") == -1) {
                    return false;
                }
                return hideOnPathway;
            });
        const hasShowOnPathway =
            taskSettings &&
            taskSettings.some(
                s => keepIfShowOnPathway.indexOf(s.getName() as TaskSettingName) > -1
            );
        if (hasShowOnSomewhereElse && !hasShowOnPathway) {
            return false;
        }
    }

    if (sessionData.hasRoleReferralOverviewTasks1()) {
        return sessionData.isTaskDefinitionAgreement(taskDef);
    }

    switch (translateTaskName(task.taskName)) {
        case TaskNames.managerNotes:
            return sessionData.hasRoleReferralAdmin();

        case TaskNames.customFormMgr:
            return sessionData.hasRoleReferralAdmin();

        case TaskNames.referralView: // fallthru
        case TaskNames.autoStart: // fall thru
        case TaskNames.endFlow:
        case TaskNames.needsAttachment:
        case TaskNames.newMultipleReferral:
        case TaskNames.clientWithContact:
        case TaskNames.staffDetail:
            return false;
        default:
            const definition = sessionData.getTaskDefinitionByName(task.taskName)!;
            console.assert(
                definition != null,
                `no task def (ra table) exists for: ${task.taskName}`
            );
            return definition.displayOverview;
    }
}

function hasPermissionToOpen(
    signposted: boolean,
    started: boolean,
    taskName: string,
    sessionData: SessionData
): boolean {
    switch (translateTaskName(taskName)) {
        case TaskNames.decideFinal:
            return sessionData.hasRoleReferralStart();
        case TaskNames.startAccommodation:
        case TaskNames.start:
            return signposted ? false : sessionData.hasRoleReferralStart();
        case TaskNames.assessmentDetails:
            return sessionData.hasRoleInterviewer();
        case TaskNames.close:
            return sessionData.hasRoleReferralClose();
        default:
            return true;
    }
}

/** Used to restrict what we even attempt to do via offline UI (for now) */
function isSupportedOffline(task: TaskDto, sessionData: SessionData) {
    const taskDef = sessionData.getTaskDefinitionByName(task.taskName)!;
    if (taskDef.type == "EVIDENCE_CUSTOMFORM") {
        // as per referralDetails below
        return sessionData.hasRoleEvangelist();
    }

    switch (task.taskName) {
        case TaskNames.project:
        case TaskNames.projectRegion:
        case TaskNames.projectAsAccommodation:
        case TaskNames.pendingStatus:
        case TaskNames.referralAccepted:
        case TaskNames.decideFinal:
        case TaskNames.startAccommodation:
        case TaskNames.start:
        case TaskNames.assessmentDate:
        case TaskNames.funding:
        case TaskNames.referralDetails:
        case TaskNames.needsReduction:
            // FALL THROUGH
            return sessionData.hasRoleEvangelist(); // Allow to use these tasks if evangelist
        // TODO: We could allow form to open read-only for some of these though

        default:
            return false;
    }
}

function isEnabledTask(
    signposted: boolean,
    started: boolean,
    task: TaskDto,
    sessionData: SessionData
): boolean {
    return (
        task.isAvailable &&
        hasPermissionToOpen(signposted, started, task.taskName, sessionData) &&
        (!isOffline() || isSupportedOffline(task, sessionData))
    );
}

interface Props {
    srId: number;
    workflow: WorkflowDto;
    onClick: (task: TaskDto) => void;
    limitToTaskNames?: string[] | undefined;
    srContext: ServiceRecipientWithEntitiesContext;
    taskRepository: TaskRepository;
}

interface State {
    adminMode: boolean;
}

function getTaskProps(task: TaskDto, serviceType: ServiceType, _sessionData: SessionData) {
    const title = serviceType.lookupTaskName(task.taskName)!;
    const settings = serviceType.getTaskDefinitionEntry(task.taskName)!.getSettings();
    return {title, settings};
}

export class TaskList extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = {
            adminMode: adminModeEnabled()
        };
        AdminMode.bus.addHandler((event?: AdminMode | undefined) => {
            this.setState({adminMode: event!.enabled}); // ! because EventHandler is badly defined should be <T=undefined>
        });
    }

    private getSubTitleUsingServiceType(
        serviceTypeId: number,
        taskName: string,
        sessionData: SessionData
    ) {
        if (this.state.adminMode && sessionData.hasRoleAdmin()) {
            return (
                <a
                    className="small"
                    href={`${applicationRootPath}nav/r/welcome/settings/servicetypes/${serviceTypeId}/${taskName}/`}
                >
                    {serviceTypeId}: {taskName}
                </a>
            );
        } else {
            return undefined;
        }
    }

    private getSubTitle(task: TaskDto, sessionData: SessionData, settings: TaskSetting[]) {
        if (this.state.adminMode && sessionData.hasRoleAdmin() && task.taskDefinitionHandle) {
            // For Activiti workflow: demo-all:46:8923:referralSource
            // For Linear workflow: linear-workflow:-:3:clientWithContact
            const linearMatches = /^linear-workflow:-:(.*):/.exec(task.taskDefinitionHandle);
            let stId: string | undefined;
            if (linearMatches) {
                stId = linearMatches[1];
            } else {
                const processKey = /^([^:]+):/.exec(task.taskDefinitionHandle)![1];
                stId =
                    processKey &&
                    Object.values(sessionData.getDto().serviceTypesById)
                        .find(s => s.processKey == processKey)!
                        .id.toFixed();
            }
            return (
                stId && (
                    <a
                        className="small"
                        href={`${applicationRootPath}nav/r/welcome/settings/servicetypes/${stId}/${task.taskName}/`}
                    >
                        {task.taskDefinitionHandle}, group:{" "}
                        {settings.find(s => s.getName() == "sourcePageGroup")?.getValue() || "-"}
                    </a>
                )
            );
        } else {
            return undefined;
        }
    }

    override render() {
        TaskAudit.instance.clearAllCache(this.props.srId); // NOTE: We may no longer need this, but it should cause any harm
        const workflowDto = this.props.workflow;
        return (
            <AsyncSessionData.Resolved>
                {(sessionData: SessionData) => (
                    <WithLatestCommands srId={this.props.srId}>
                        {this.renderTaskList(workflowDto, sessionData)}
                    </WithLatestCommands>
                )}
            </AsyncSessionData.Resolved>
        );
    }

    // TODO migrate to service recipient
    private signpostedServiceRecipient(): boolean {
        switch (this.props.srContext.serviceRecipient.prefix) {
            case "r":
                return this.props.srContext.referral!.signpostedReasonId != null;
            case "i":
                return this.props.srContext.incident!.signpostedReasonId != null;
            default:
                return false;
        }
    }
    private startedServiceRecipient(): boolean {
        switch (this.props.srContext.serviceRecipient.prefix) {
            case "r":
                return this.props.srContext.referral!.receivingServiceDate != null;
            case "i":
                return this.props.srContext.incident!.receivingServiceDate != null;
            default:
                return false;
        }
    }

    private renderTaskList(workflowDto: WorkflowDto, sessionData: SessionData): JSX.Element {
        const srContext = this.props.srContext;
        const workflow = new WorkflowActiveRecord(
            workflowDto,
            () => {
                WorkflowReloadEvent.bus.fire();
            },
            this.props.taskRepository
        );
        return (
            <>
                {((workflowDto && workflowDto.tasks) || [])
                    .filter(task =>
                        isVisibleTask(
                            task,
                            srContext.serviceType.id,
                            sessionData,
                            this.props.limitToTaskNames
                        )
                    )
                    // avoid NPE - only show tasks that are in the cached task list
                    .filter(
                        task => srContext.serviceType.getTaskDefinitionEntry(task.taskName) != null
                    )
                    .map(task => {
                        const {title, settings} = getTaskProps(
                            task,
                            srContext.serviceType,
                            sessionData
                        );

                        return (
                            <TaskRow
                                key={task.taskName}
                                sessionData={sessionData}
                                serviceType={srContext.serviceType}
                                serviceAllocationId={srContext.serviceRecipient.serviceAllocationId} // NB if our task was loaded from TaskStatus we'd have serviceAllocationId
                                disabled={
                                    !isEnabledTask(
                                        this.signpostedServiceRecipient(),
                                        this.startedServiceRecipient(),
                                        task,
                                        sessionData
                                    )
                                }
                                task={task}
                                workflow={workflow}
                                username={sessionData.getDto().username}
                                title={title}
                                subTitle={this.getSubTitleUsingServiceType(
                                    srContext.serviceType.id,
                                    task.taskName,
                                    sessionData
                                )}
                                onClick={() => this.props.onClick(task)}
                                SummaryComponent={props => (
                                    <TaskSummary {...props} srId={this.props.srId} />
                                )}
                            />
                        );
                    })}
            </>
        );
    }
}
