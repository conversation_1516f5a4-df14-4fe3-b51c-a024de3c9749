import * as React from "react";
import {FC} from "react";
import ServicesContext from "../ServicesContext";
import {
    AuditHistoryItemControl,
    ConfigurablePageComponentRegistry,
    EccoAPI,
    setGlobalEccoAPI
} from "../EccoAPI";
import {ApiClient, BaseServiceRecipientCommandDto, RequestOptions, SessionData} from "ecco-dto";
import {fail, getFailAllMethodsMock} from "./mock-utils";
import {CommandAjaxRepository} from "ecco-commands";
import {CommandViewHandler} from "../service-recipient/AuditHistoryPaged";

const allFailTaskIntegrations = {
    handleTaskClick: () => {
        fail();
    },
    formForTask: () => fail(),
    registerIntegrationHooks: () => {
        fail();
    }
};

const allFailAuditHistoryIntegrations = {
    handleCommand: (command: BaseServiceRecipientCommandDto, sessionData: SessionData) => {
        fail();
        return {} as CommandViewHandler<any>;
    },
    componentFactory: (sessionData: SessionData) => {
        fail();
        return {} as AuditHistoryItemControl;
    }
};

const allFailApiClientGet = {
    get<T>(
        path: string,
        options?: RequestOptions | undefined,
        requestBody?: Object | null | undefined
    ): Promise<T> {
        fail();
        return Promise.resolve({} as T);
    }
} as ApiClient;

const allFailEccoAPI: Partial<EccoAPI> = {
    apiClient: allFailApiClientGet,
    pageComponentRegistry: new ConfigurablePageComponentRegistry(),
    auditHistoryIntegrations: allFailAuditHistoryIntegrations,
    taskIntegrations: allFailTaskIntegrations,
    getAddressRepository: fail,
    getCommandRepository: () => getFailAllMethodsMock(CommandAjaxRepository),
    referralRepository: fail,
    getSignatureRepository: fail
};

export const TestServicesContextProvider: FC<{overrides: Partial<EccoAPI>}> = ({
    overrides,
    children
}) => {
    const eccoApi = {...allFailEccoAPI, ...overrides} as EccoAPI; // Makes everything blow up if we use but don't configure it
    setGlobalEccoAPI(eccoApi);
    return <ServicesContext.Provider value={eccoApi} children={children} />;
};
