import {mount} from "cypress/react";
import * as React from "react";
import {CronScheduleStatus} from "../../agreements/CronScheduleStatus";
import {ScheduleData} from "ecco-rota";

describe("CronSchedule tests", () => {
    it("single time correct", () => {
        const data = ScheduleData.fromTargetSchedule(null, "start:2022-03-20 days:mon times:21:45");
        mount(
            <CronScheduleStatus
                readOnly={false}
                title={"cron schedule test"}
                allowMultipleTimes={true}
                instanceState={data}
                withStartEnd={true}
                onChange={() => {}}
            />
        );

        // NB missing font-awesome icons, like the + for more times

        //cy.get(".target-schedule").click();
    });
});
