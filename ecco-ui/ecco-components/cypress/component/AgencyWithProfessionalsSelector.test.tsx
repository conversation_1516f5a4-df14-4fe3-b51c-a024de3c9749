import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {ContactsAjaxRepository} from "ecco-dto";
import {mockAgencyDtos, mockProfessionalDtos} from "../../test-support/mockContacts";
import {AgencyWithProfessionalsSelector} from "../../contacts/AgencyWithProfessionalsSelector";
import {useAgenciesWithProfessionals} from "../../data/entityLoadHooks";
import {useState} from "react";

const contactsRepository = getFailAllMethodsMock<ContactsAjaxRepository>(ContactsAjaxRepository);
contactsRepository.findAllProfessionals = () => Promise.resolve(mockProfessionalDtos);
contactsRepository.findAllAgencies = () => Promise.resolve(mockAgencyDtos);

const overrides = {
    sessionData: sessionData,
    contactsRepository: contactsRepository
} as EccoAPI;

const AgencyWithProfessionalWrapper = () => {
    const {agenciesWithProfessionals, loading} = useAgenciesWithProfessionals(null, null);
    const [selected, setSelected] = useState<number>(null);
    return (
        <AgencyWithProfessionalsSelector
            labelName={"find contact"}
            onChange={(agency, professional) =>
                setSelected(professional?.contactId || agency?.contactId)
            }
            agenciesWithProfessionals={agenciesWithProfessionals}
            selectedId={selected}
        />
    );
};

describe("AgencyProfessional tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <AgencyWithProfessionalWrapper />
            </TestServicesContextProvider>
        );
        // cy
    });
});
