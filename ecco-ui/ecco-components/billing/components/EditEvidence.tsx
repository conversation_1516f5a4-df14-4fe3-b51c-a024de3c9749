import * as React from "react";
import {ChangeEvent, ReactNode, useState} from "react";
import {
    <PERSON><PERSON>Bar,
    <PERSON>ton,
    Dialog,
    DialogContent,
    FormControl,
    IconButton,
    InputLabel, makeStyles,
    MenuItem,
    Select,
    Slide, Theme,
    Toolbar,
    Typography,
    createStyles
} from "@eccosolutions/ecco-mui";
import CloseIcon from '@material-ui/icons/Close';
import {SessionData} from "ecco-dto";
// eslint-disable-next-line no-restricted-imports
import {TransitionProps} from "@material-ui/core/transitions"

// slide up animation.
function Transition(props: TransitionProps) {
    return <Slide direction="up" {...props} />;
}

export interface EditEvidenceDto {
    eventsStatusRateId?: number | null | undefined;
}

interface Props {
    evidence: EditEvidenceDto
    onClose?(): void
    saveText: string
    onSuccess: (evidence: EditEvidenceDto) => Promise<void>
    sessionData: SessionData
}

function EditEvidence(props: Props) {
    const { sessionData, saveText } = props;
    const classes = useStyles();
    const [open, setOpen] = useState(true);
    const [evidence, setEvidence] = useState({ ...props.evidence }); // destructure prop into state. Not great but okay.

    const rateOptions = sessionData.getListDefinitionEntriesByListName("eventStatusRateId");

    // handle the close, call parent props (onclose) before closing.
    // Need to consider async close actions for the real version
    function handleClose() {
        if (props.onClose) {
            props.onClose();
        }
        setOpen(false);
    }

    //TODO: type information needed on props.
    // takes an event, and updates a given property on the evidence
    function handleChange(
        event: ChangeEvent<{name?: string | undefined; value: unknown}>,
        _child: ReactNode
    ) {
        const name = event.target.name as keyof EditEvidenceDto;
        const value = event.target.value as number;
        setEvidence({...evidence, [name]: value});
    }

    // assumptipn, onSuccess will be async... but actually... it may not.
    function handleSave() {
        return props.onSuccess(evidence).then(() => handleClose());
    }

    return (
        <div>
            <Dialog
                fullScreen={true}
                open={open}
                onClose={handleClose}
                TransitionComponent={Transition}
            >
                <AppBar className={classes.appBar}>
                    <Toolbar>
                        <IconButton
                            color="inherit"
                            onClick={handleClose}
                            aria-label="Close"
                        >
                            <CloseIcon />
                        </IconButton>
                        <Typography
                            variant="h6"
                            color="inherit"
                            className={classes.flex}
                        >
                            Edit Evidence
                        </Typography>
                        <Button color="inherit" onClick={handleSave}>
                            {saveText}
                        </Button>
                    </Toolbar>
                </AppBar>
                <DialogContent>
                    <form
                        className={classes.container}
                        noValidate={true}
                        autoComplete="off"
                    >
                        <FormControl>
                            <InputLabel htmlFor="rateName">
                                Select Rate
                            </InputLabel>
                            <Select
                                value={evidence.eventsStatusRateId}
                                onChange={handleChange}
                                displayEmpty={true}
                                name="eventsStatusRateId" // This is used by handleChange to set the field
                                placeholder="Select rate"
                                className={classes.selectEmpty}
                            >
                                {rateOptions.map(entry =>
                                     <MenuItem value={entry.getId()}>{entry.getName()}</MenuItem>
                                )}
                            </Select>
                        </FormControl>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}

const useStyles = makeStyles((theme: Theme) =>
 createStyles({
    appBar: {
        position: 'relative'
    },
    formControl: {
        minWidth: 120
    },
    container: {
        display: 'flex',
        flexWrap: 'wrap'
    },
    selectEmpty: {
        minWidth: 200
    },
    textField: {
        width: 200
    },
    flex: {
        flex: 1
    }
}));

export default EditEvidence;
