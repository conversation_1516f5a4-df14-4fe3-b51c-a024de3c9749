import * as React from "react";
import {FC} from "react";
import {
    Autocomplete,
    Box,
    createStyles,
    InputAdornment,
    makeStyles,
    Popper,
    PopperProps,
    TextField
} from "@eccosolutions/ecco-mui";
import {AgencyProfessionals} from "../data/entityLoadHooks";
import {Agency, Individual} from "ecco-dto";
import PersonIcon from "@material-ui/icons/Person";
import BusinessIcon from "@material-ui/icons/Business";


const findAgencyMaybe = (contactId: number | null, data: AgencyProfessionals[]) => {
    return (
        (contactId &&
            data
                .filter(ap => ap.agency.contactId == contactId)
                .map(ap => ap.agency)
                .pop()) ||
        null
    );
};
const findProfessionalMaybe = (contactId: number | null, data: AgencyProfessionals[]) => {
    return (
        (contactId &&
            data
                .map(ap => ap.professionals)
                .reduce((r, x) => r.concat(x), []) // flatMap
                .filter(p => (p.contactId ? p.contactId == contactId : false))
                .pop()) ||
        null
    );
};

type ContactType = "agency" | "individual";

const agencyToList = (a: Agency) => {
    const items = [];
    items.push(a.phoneNumber, a.mobileNumber, a.email);
    const str = items.join(" ");
    return {
        isPerson: false,
        id: a.contactId!,
        name: a.companyName,
        other: str,
        disabled: false,
        discriminator: "agency" as ContactType
    };
};
const professionalToList = (a: Agency, p: Individual) => {
    const name = `${p.firstName} ${p.lastName} ${p.jobTitle ? `/${p.jobTitle}` : ""}`;
    const items = [];
    items.push(p.phoneNumber, p.mobileNumber, p.email);
    const other = items.join(" ");
    return {
        isPerson: true,
        id: p.contactId!,
        name: name,
        other: `${other} [${a.companyName}]`,
        disabled: false,
        discriminator: "individual" as ContactType
    };
};

interface SelectListOptionType {
    discriminator: ContactType;
    id: number | string;
    name: string;
    other: string;
    disabled?: boolean | undefined;
}

export const convertContactToEntities = (
    contactId: number | null,
    agenciesWithProfessionals: AgencyProfessionals[]
) => {
    if (!contactId) {
        return {agency: null, professional: null};
    }
    let agency = findAgencyMaybe(contactId, agenciesWithProfessionals);
    let professional = null;
    if (!agency) {
        professional = findProfessionalMaybe(contactId, agenciesWithProfessionals);
        if (professional) {
            agency = findAgencyMaybe(
                professional.organisationId || null,
                agenciesWithProfessionals
            );
        }
    }
    return {agency: agency, professional: professional};
};

interface SelectListOptionTypeExtended extends SelectListOptionType {
    isPerson: boolean;
}

/*
 * The height of our li in the dropdown gets overridden by:
        @media (min-width: 600px)
        <style>
        .MuiAutocomplete-option {
            min-height: auto;
        }
 * which means the default MuiAutocomplete-option min-height: 48px; isn't applied
 * so we re-instate.
 * We need to use a custom component though, since the popper is rendered separately.
 * see https://smartdevpreneur.com/how-to-style-the-material-ui-autocomplete-component/
 *
 * HOWEVER, we may not need to render the popup separately... by using disablePortal.
 *
 * Keeping with portal for now (outside the dom) we also need to now increase the zIndex
 * since it appears to hide under the current modal (eg source of referral).
*/
const useStyles = makeStyles(_theme =>
    createStyles({
        autocompleteDropdown: {
            "& .MuiAutocomplete-paper": {
                fontSize: "initial"
            },
            "& .MuiAutocomplete-listbox": {
                "& li": {minHeight: "48px"},
                // adornment works well by changing whiteSpace, but on small screen then need to
                // remove height and maxHeight, but can't remove css
                "& div.MuiInputAdornment-root": {
                    whiteSpace: "normal",
                    height: "initial",
                    maxHeight: "initial"
                }
            }
        }
    })
);
const CustomPopper = (props: PopperProps) => {
    const classes = useStyles();
    return (
        <Popper
            {...props}
            style={{zIndex: 1400}}
            className={classes.autocompleteDropdown}
            placement="bottom-start"
            children={props.children}
        />
    );
};

const renderOption = (props: SelectListOptionTypeExtended) => (
    <>
        {props.isPerson ? (
            <div>
                <span style={{marginLeft: "10px", fontWeight: "bold"}}>{props.name}</span>{" "}
                {props.other}
            </div>
        ) : (
            <Box>
                <InputAdornment position="start">
                    <BusinessIcon />
                    <span>
                        <span style={{fontWeight: "bold"}}>{props.name}</span> {props.other}
                    </span>
                </InputAdornment>
            </Box>
        )}
    </>
);

// test ecco-app (ecco-ui/_welcome_/package.json) at http://localhost:3000/test/test-agent-profs
export const AgencyWithProfessionalsSelector: FC<{
    labelName: string;
    onChange: (agency: Agency | null, professional: Individual | null) => void;
    agenciesWithProfessionals: AgencyProfessionals[];
    selectedId: number | null;
}> = props => {
    const {agenciesWithProfessionals} = props;
    if (!agenciesWithProfessionals) {
        return null;
    }

    const contactsList: SelectListOptionTypeExtended[] = agenciesWithProfessionals
        .map(ap => {
            const listA = agencyToList(ap.agency);
            const listPs = ap.professionals
                ? ap.professionals.map(p => professionalToList(ap.agency, p))
                : [];
            return [listA, ...listPs];
        })
        .reduce((r, x) => r.concat(x), []); // flatMap

    const selected =
        (props.selectedId && contactsList.filter(c => c.id == props.selectedId).pop()) || null;

    const Search = (
        <Autocomplete
            id={"eccotest-agencyProfs"}
            PopperComponent={CustomPopper}
            renderOption={renderOption}
            renderInput={params => {
                const Icon = selected ? (
                    selected.isPerson ? (
                        <PersonIcon />
                    ) : (
                        <BusinessIcon />
                    )
                ) : null;
                return (
                    // @ts-ignore FIXME compatibility with exactOptionalPropertyTypes
                    <TextField
                        {...params}
                        label={props.labelName}
                        variant="outlined"
                        InputProps={{
                            ...params.InputProps,
                            startAdornment: <InputAdornment position="start">{Icon}</InputAdornment>
                        }}
                    />
                );
            }}
            getOptionLabel={option => `${option.name} ${option.other}`}
            getOptionSelected={(a, b) => a.id == b.id}
            options={contactsList}
            value={selected}
            onChange={(_event, obj) => {
                const {agency, professional} = convertContactToEntities(
                    obj ? (obj.id as number) : null,
                    agenciesWithProfessionals
                );
                props.onChange(agency, professional);
            }}
        />
    );

    return <>{Search}</>;
};
