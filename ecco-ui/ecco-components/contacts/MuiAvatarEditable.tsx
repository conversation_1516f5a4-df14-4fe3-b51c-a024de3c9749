import * as React from "react";
import {FC} from "react";
import "../styles/mui-avatar-editable.css";
import {withServicesContext} from "../ServicesContext";
import {AvatarProps, BaseAvatarControl} from "./BaseAvatarControl";
import {Avatar} from "@eccosolutions/ecco-mui";
import {EccoAPI} from "../EccoAPI";
import {stringAvatar, useStyles} from "../referral/SrAvatarImage";

interface Props extends AvatarProps {
    size: string;
}
class _MuiAvatarEditable extends BaseAvatarControl<Props & {services: EccoAPI}> {
    override render() {
        const imageOverlay = (
            <div
                className="image-overlay"
                style={{position: "absolute", overflow: "hidden"}}
                onMouseLeave={this.disabled ? undefined : () => this.setState({mouseOver: false})}
                onClick={e => this.onClickOverlay(e)}
            >
                <div className="placeholder" style={{cursor: "pointer"}}>
                    <p className="fa fa-times-circle" style={{margin: "0.7em"}}></p>
                    <div className="prompt">Remove photo</div>
                </div>
            </div>
        );

        const imageThumbnail = this.state.imageUrl ? (
            <Avatar
                className={`${this.props.size} image-thumbnail`}
                src={this.state.imageUrl}
                alt="photo"
                onMouseEnter={this.disabled ? undefined : () => this.setState({mouseOver: true})}
            />
        ) : null;

        const prompt = (
            <p
                className="prompt fa fa-edit"
                title={this.state.errorReason || "Click or drop an image"}
            >
                {this.state.errorText || ""}
            </p>
        );

        const fileInput = (
            <input
                ref={this.fileInputRef}
                type="file"
                accept="image/jpeg"
                style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    margin: 0,
                    border: "10000px solid transparent",
                    opacity: 0,
                    cursor: "pointer"
                }}
                onClick={() => this.onFileInputClick()}
                onChange={() => this.onFileChange()}
            />
        );

        const initialsAvatar = (
            <Avatar
                {...stringAvatar(
                    `${this.props.contact.firstName || "-"} ${this.props.contact.lastName || "-"}`
                )}
                className={`${this.props.size} image-thumbnail`}
            />
        );

        const placeholder = this.disabled ? (
            initialsAvatar
        ) : (
            <>
                {initialsAvatar}
                {prompt}
            </>
        );

        const imageDrop = (
            <div
                className={`image-drop ${this.props.size}`}
                style={{position: "relative", overflow: "hidden"}}
            >
                <div className="placeholder">{placeholder}</div>
                {this.disabled ? null : fileInput}
            </div>
        );

        return (
            <div className={`mui-avatar-editable`} style={{position: "relative"}}>
                {this.state.mouseOver ? imageOverlay : null}
                {imageThumbnail}
                {this.state.imageUrl ? null : imageDrop}
            </div>
        );
    }
}

export const MuiAvatarEditable: FC<AvatarProps> = props => {
    const classes = useStyles();
    return withServicesContext(services =>
        services ? <_MuiAvatarEditable {...props} services={services} size={classes.pic} /> : null
    );
};

export default MuiAvatarEditable;
