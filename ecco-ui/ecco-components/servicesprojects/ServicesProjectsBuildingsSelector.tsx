import {FC} from "react";
import * as React from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {useServicesContext} from "../ServicesContext";
import {link} from "ecco-components-core";
import {useBuildings} from "../data/entityLoadHooks";

// DON'T NEED COMMAND FORM - just links
// HOWEVER, this command form approach is what we should be using for editing services/proj etc
/*
type State = {
    svccatIds: number[];
};
type Action = Partial<State>;
type SPBSReducer = Reducer<State, Action>;

const useCommandSubForm = () => {
    // We use this fixed object to hold the state we're mutating
    const stateHolder = useMemo<{state: State}>(
        () => ({
            state: {
                svccatIds: []
            }
        }),
        []
    );

    const reducer: SPBSReducer = (prevState, action) => {
        stateHolder.state = {...stateHolder.state, ...action};
        return stateHolder.state;
    };
    const [state, dispatch] = useReducer<SPBSReducer>(reducer, stateHolder.state);

    const commandSource: CommandSource = {
        // Note: The closure will freeze whatever values are seen here,
        emitChangesTo: function (cmdQ: CommandQueue): void {
            // TODO
        },
        getErrors(): string[] {
            return [];
        }
    };

    useCommandSourceRegistration(commandSource);

    return {state, dispatch};
};

export const ServicesProjectsBuildingsSelectorForm: FC<{
    show: boolean;
    setShow: (s: boolean) => void;
}> = ({show, setShow}) => {
    return (
        <ModalCommandForm
            title={"rota"}
            action={"continue"}
            show={show}
            setShow={setShow}
            helpTitle="help title"
            maxWidth="sm"
        >
            <ServicesProjectsBuildingsSelectorSubform />
        </ModalCommandForm>
    );
};
*/

export const ServicesProjectsBuildingsSelector: FC<{
    applicationRootPath: string;
}> = ({applicationRootPath}) => {
    // a svccat with building means it can have careruns, which is our fudge for rota-based services
    const {sessionData} = useServicesContext();
    const {buildings} = useBuildings();
    const svcCatsWithBuilding = sessionData
        .getRestrictedServiceCategorisations()
        .filter(sc => !!sc.buildingId);

    if (!sessionData && !buildings) {
        return null;
    }
    return (
        <>
            <Grid container direction={"row"} justify={"center"} alignItems={"center"}>
                {svcCatsWithBuilding.map(sc => {
                    const name = buildings?.filter(b => b.buildingId == sc.buildingId).pop()?.name;
                    const hrefRota = `${applicationRootPath}nav/w/welcome/rota/week/workers:all/svccats:${sc.id}`;
                    const hrefRuns = `${applicationRootPath}nav/w/welcome/runs/week/careruns:all/svccats:${sc.id}`;
                    return (
                        <>
                            <Grid item xs={6} style={{textAlign: "right"}}>
                                {name || ""}
                            </Grid>
                            <Grid item xs={6} style={{textAlign: "center"}}>
                                {link("rota", undefined, hrefRota, false)}
                                {sessionData.isEnabled("rota.shifts") && (
                                    <>
                                        <span style={{paddingLeft: "25px"}}>&nbsp;</span>
                                        {link("run builder", undefined, hrefRuns, false)}
                                    </>
                                )}
                            </Grid>
                        </>
                    );
                })}
            </Grid>
        </>
    );
};
