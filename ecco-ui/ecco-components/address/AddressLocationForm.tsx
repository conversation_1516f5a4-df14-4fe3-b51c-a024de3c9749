import * as React from "react"
import {ClassAttributes, Component, FC, useEffect, useState} from "react"
import {AddressDetailModal} from "./AddressDetail";
import {AddressList} from "./AddressList";
import {Address, BLDG_SYMBOL} from "ecco-dto";
import {EccoDate} from "@eccosolutions/ecco-common";
import {AddressHistoryModalLink} from "./addresshistory/AddressHistory";
import {useServicesContext} from "../ServicesContext";
import {Box} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {EccoV3Modal} from "ecco-components-core";

function hasAddress(adr: Address | null): boolean {
    return summariseAddress(adr) != "";
}

function summariseBuildingAddress(buildingName: string | null, adr: Address | null): string {
    // also see ReferralOverviewControl which additionally shows the parent line1
    return (buildingName ? `${BLDG_SYMBOL} ` : "").concat(summariseAddress(adr));
}

export function summariseAddress(adr: Address | null | undefined): string {
    return adr
        ? adr.address
              .concat(adr.postcode || "", adr.town || "")
              .filter(a => a != null)
              .join(", ")
        : "";
}

interface AddressDisplayProps {
    addressLocationId?: number | null | undefined;
    buildingLocationId?: number | null | undefined;
    displayAddress: Address | null;
}

/**
 * Displays the address in client history (AddressHistory) and edit client/staff/building details (via ClientAddressLocation).
 */
export const AddressDisplay: FC<AddressDisplayProps> = props => {
    // The displayAddress is the expanded address from the SR file. It can be from an inbound referral, or it can be from
    // editing the address - which provides the addressLocationId and maybe the buildingLocationId but which also maintains
    // the displayAddress - see SRAddressLocationCCH.updateContactAddress.
    // So we prioritise displaying the displayAddress.
    // if we are a building, load it to get the displayBuildingName (NB we could use residenceName?)

    const validProps = props.buildingLocationId || props.addressLocationId || props.displayAddress;
    if (!validProps) {
        return null;
    }

    const [displayAddress, setDisplayAddress] = useState<Address | null>(props.displayAddress);
    const [displayBuildingName, setDisplayBuildingName] = useState<string | null>(null);

    const services = useServicesContext();

    useEffect(() => {
        if (props.buildingLocationId) {
            services
                .getBuildingRepository()
                .findOneBuilding(props.buildingLocationId)
                .then(bldg =>
                    services
                        .getAddressRepository()
                        .findOneAddress(bldg.locationId)
                        .then(adr => {
                            setDisplayBuildingName(bldg.name);
                            setDisplayAddress(adr);
                        })
                );
        } else if (props.addressLocationId && !hasAddress(props.displayAddress)) {
            services
                .getAddressRepository()
                .findOneAddress(props.addressLocationId)
                .then(adr => {
                    setDisplayBuildingName(null);
                    setDisplayAddress(adr);
                });
        }
    }, [props.buildingLocationId, props.addressLocationId, props.displayAddress]);

    return displayAddress ? (
        <span>{summariseBuildingAddress(displayBuildingName, displayAddress)}</span>
    ) : null;
}


interface Props extends ClassAttributes<AddressLocation> {
    /** show buildings with the addresses */
    showBuildings: boolean;
    addressLocationId?: number | null | undefined;
    /** optional - the building, which takes priority over the address */
    buildingLocationId?: number | null | undefined;
    /** optional - for history - see ClientAddressLocationForm */
    serviceRecipientId?: number | undefined;
    /** optional - for history - see ClientAddressLocationForm */
    contactId?: number | undefined;

    /** This is to display legacy address instead */
    displayAddress: Address | null;
    /** Notify if current address is valid or not */
    handleAddressValidChange: (isValid: boolean) => void;
    /** Enable address history "valid from" for the address part */
    showValidFrom?: true | undefined;
}

interface State {
    addressLocationId?: number | null | undefined;
    buildingLocationId?: number | null | undefined;
    newAddress: boolean;
    editingAddress: boolean;
    /** If we have a postcode that we want to create a new address from, this is it */
    initialPostCode?: string | undefined;
    validFrom: EccoDate | null;
}

/** Display address and allow it to be changed.
 * If showBuildings=true then allow buildings at an address to be selected, which will result in
 * both an addressLocationId and buildingLocationId.
 * FIXME: This currently relies on a component ref to this object existing so that
 *   resolveAddressLocationId(), getBuildingLocationId() and getValidFrom() can be called
 */
export class AddressLocation extends Component<Props, State> {
    constructor(props: Props) {
        super(props);

        this.state = {
            addressLocationId: props.addressLocationId,
            buildingLocationId: props.buildingLocationId,
            editingAddress: false,
            newAddress: false,
            validFrom: null
        };
    }

    override componentDidUpdate(
        prevProps: Readonly<Props>,
        _prevState: Readonly<State>,
        _snapshot?: any
    ) {
        if (
            this.props.addressLocationId != prevProps.addressLocationId ||
            this.props.buildingLocationId != prevProps.buildingLocationId
        ) {
            this.setState({
                addressLocationId: this.props.addressLocationId,
                buildingLocationId: this.props.buildingLocationId
            });
        }
    }

    private handleToggleClick = () => {
        this.props.handleAddressValidChange(!this.state.editingAddress);

        this.setState({
            addressLocationId: null,
            editingAddress: !this.state.editingAddress,
            newAddress: !this.state.newAddress
        });
    };

    private handleResetClick = () => {
        this.setState({
            addressLocationId: this.props.addressLocationId,
            editingAddress: false,
            newAddress: false
        });
        this.props.handleAddressValidChange(true);
    };

    private handleEditableClick = () => {
        this.setState({
            addressLocationId: null,
            editingAddress: true,
            newAddress: false
        });
    };

    public getAddressLocationId(): number | null {
        return this.state.addressLocationId || null;
    }

    public getBuildingLocationId(): number | null {
        return this.state.buildingLocationId || null;
    }
    public getValidFrom(): EccoDate | null {
        return this.state.validFrom;
    }
    public hasAddressChange(): boolean {
        return (
            this.state.addressLocationId != this.props.addressLocationId ||
            this.state.buildingLocationId != this.props.buildingLocationId
        );
    }

    override render() {
        let AddressSummary = null;
        let AddressElement;
        let AddressHintElement;

        // if we had an incoming value, and haven't changed anything
        if (!this.state.newAddress && !this.state.editingAddress) {
            AddressSummary = (
                <>
                    <h3>
                        address{" "}
                        <small>
                            <a
                                onClick={() => {
                                    if (
                                        this.state.addressLocationId != this.props.addressLocationId
                                    ) {
                                        this.setState({
                                            addressLocationId: this.props.addressLocationId,
                                            buildingLocationId: this.props.buildingLocationId
                                        });
                                    } else {
                                        this.handleEditableClick();
                                    }
                                }}
                            >
                                {this.state.addressLocationId != this.props.addressLocationId
                                    ? "cancel change"
                                    : this.props.addressLocationId
                                    ? "change"
                                    : "add"}
                            </a>
                        </small>
                    </h3>
                    <div>
                        <AddressDisplay
                            displayAddress={this.props.displayAddress}
                            addressLocationId={this.props.addressLocationId || null}
                            buildingLocationId={this.props.buildingLocationId || null}
                        />
                    </div>
                    {(this.props.serviceRecipientId || this.props.contactId) && (
                        <small>
                            <AddressHistoryModalLink
                                serviceRecipientId={this.props.serviceRecipientId}
                                contactId={this.props.contactId}
                            />
                        </small>
                    )}
                </>
            );
        } else if (this.state.newAddress) {
            const address: Address = {
                address: [],
                postcode: this.state.initialPostCode
            } as Partial<Address> as Address;
            AddressElement = (
                <div style={{marginTop: "10px"}}>
                    <AddressDetailModal
                        address={address}
                        onSave={addressLocationId =>
                            this.setState({addressLocationId, newAddress: false})
                        }
                        onCancel={() => this.setState({newAddress: false})}
                    />
                </div>
            );
            AddressHintElement = (
                <span>
                    create a new address or <a onClick={this.handleToggleClick}>choose one</a>
                    <span>
                        {" "}
                        or <a onClick={this.handleResetClick}>cancel</a>
                    </span>
                </span>
            );
        } else {
            // i.e. editingAddress = true
            AddressElement = (
                <EccoV3Modal
                    show={true}
                    title="change address"
                    action="cancel"
                    onCancel={() => this.setState({editingAddress: false})}
                    onSave={() => {}}
                >
                    <AddressList
                        key={"adrList"}
                        showBuildings={this.props.showBuildings}
                        onChange={(id: number, bldgId: number | null) =>
                            this.setState({
                                editingAddress: false,
                                addressLocationId: id,
                                buildingLocationId: bldgId
                            })
                        }
                        onCreateNew={initialPostCode =>
                            this.setState({
                                editingAddress: false,
                                newAddress: true,
                                initialPostCode
                            })
                        }
                    />
                </EccoV3Modal>
            );
            AddressHintElement = (
                <span>
                    choose an address or <a onClick={this.handleToggleClick}>create a new one</a>
                    <span>
                        {" "}
                        or <a onClick={this.handleResetClick}>cancel</a>
                    </span>
                </span>
            );
        }

        const differentAddressId =
            this.state.addressLocationId !== null &&
            this.state.addressLocationId != this.props.addressLocationId;
        const differentBuildingId =
            this.state.buildingLocationId !== null &&
            this.state.buildingLocationId != this.props.buildingLocationId;

        return (
            <div className="v-gap-15">
                {AddressSummary}
                {AddressHintElement}
                {AddressElement}
                {(differentAddressId || differentBuildingId) && (
                    <>
                        <p>
                            {this.props.addressLocationId ? "change address to" : "add address as"}
                            <dt>
                                <AddressDisplay
                                    addressLocationId={this.state.addressLocationId}
                                    buildingLocationId={this.state.buildingLocationId}
                                    displayAddress={null}
                                />
                            </dt>
                        </p>
                        {this.props.showValidFrom !== undefined && (
                            <Box my={2}>
                                <DatePickerEccoDate
                                    showTodayButton={true}
                                    name="valid from"
                                    label="valid from"
                                    value={this.state.validFrom}
                                    onChange={validFrom => this.setState({validFrom})}
                                    required={true}
                                    error={!this.state.validFrom}
                                    helperText={
                                        this.state.validFrom
                                            ? undefined
                                            : "please specify when the address changed"
                                    }
                                />
                            </Box>
                        )}
                    </>
                )}
            </div>
        );
    }
}
