import {Route, Switch} from "react-router";
import * as React from "react";
import {FC} from "react";
import {usePageComponentLookup} from "../ServicesContext";

export const SvcRecPageRouter: FC<{
    basepath: string;
    srId: number;
    preview: boolean;
    eventId?: string | undefined;
}> = props => {
    const {basepath, srId, preview, eventId} = props;
    const pageComponentFactory = usePageComponentLookup(srId, preview, eventId);
    return (
        <Switch>
            {/* see defaultPageComponentRegistry */}
            {/* default is the landing page chooser (online/offline) */}
            <Route exact path={`${basepath}`} component={pageComponentFactory("default")} />
            {/* info is the overview panel used in offline */}
            <Route exact path={`${basepath}info`} component={pageComponentFactory("info")} />

            <Route path={`${basepath}visit`} component={pageComponentFactory("visit")} />

            <Route exact path={`${basepath}hms`} component={pageComponentFactory("hms")} />

            <Route exact path={`${basepath}finance`} component={pageComponentFactory("finance")} />

            {/* example of useful components - not exposed as menu item */}
            <Route
                path={`${basepath}taskTimeline`}
                component={pageComponentFactory("taskTimeline")}
            />
            {/* this is the whole referrals list, but we're thinking the 'quick log' button really */}
            {/*<Route path={`${basepath}quick-log`} component={pageComponentFactory("quick-log")}/>*/}
            {/* example of useful components */}

            {/* when on a main/sr2/:srId/ page */}
            <Route
                path={`${basepath}emergencyDetails`}
                component={pageComponentFactory("emergencyDetails")}
            />

            <Route path={`${basepath}calendar`} component={pageComponentFactory("calendar")} />
            <Route path={`${basepath}contacts`} component={pageComponentFactory("contacts")} />
            <Route
                path={`${basepath}relationships`}
                component={pageComponentFactory("relationships")}
            />
            <Route
                path={`${basepath}attachments`}
                component={pageComponentFactory("attachments")}
            />

            {/* history */}
            <Route path={`${basepath}timeline`} component={pageComponentFactory("timeline")} />
            <Route
                path={`${basepath}support-history`}
                component={pageComponentFactory("support-history")}
            />
            <Route
                path={`${basepath}visit-history`}
                component={pageComponentFactory("visit-history")}
            />
            <Route
                path={`${basepath}risk-history`}
                component={pageComponentFactory("risk-history")}
            />
            <Route path={`${basepath}audits`} component={pageComponentFactory("audits")} />
            <Route path={`${basepath}search`} component={pageComponentFactory("search")} />

            {/* future - mostly */}
            <Route
                path={`${basepath}appointments`}
                component={pageComponentFactory("appointments")}
            />
            <Route
                path={`${basepath}forward-plan`}
                component={pageComponentFactory("forward-plan")}
            />
            <Route
                path={`${basepath}risk-forward-plan`}
                component={pageComponentFactory("risk-forward-plan")}
            />

            <Route path={`${basepath}workflow`} component={pageComponentFactory("tasks")} />
            <Route path={`${basepath}services`} component={pageComponentFactory("services")} />
            <Route path={`${basepath}incidents`} component={pageComponentFactory("incidents")} />
            <Route
                path={`${basepath}managedvoids`}
                component={pageComponentFactory("managedvoids")}
            />
            <Route path={`${basepath}repairs`} component={pageComponentFactory("repairs")} />
            <Route path={`${basepath}occupancy`} component={pageComponentFactory("occupancy")} />
            <Route path={`${basepath}workerJobs`} component={pageComponentFactory("workerJobs")} />
            <Route
                path={`${basepath}reports`}
                exact={true}
                component={pageComponentFactory("reports")}
            />
            <Route
                path={`${basepath}reports/:chartUuid`}
                component={pageComponentFactory("reports")}
            />
            <Route
                path={`${basepath}communication`}
                component={pageComponentFactory("communication")}
            />

            <Route
                exact={true}
                path={`${basepath}tasks`}
                component={pageComponentFactory("tasks")}
            />
            {/* we had this from 8179144d, but could be that <TasksControl was more important in that commit */}
            <Route path={`${basepath}tasks/:taskName`} component={pageComponentFactory("tasks")} />
            {/*    <EccoModal
                               title={
                                   <CardHeader title={referralContext.referral.displayName}
                                               subheader={referralContext.referral.referredServiceName}/>
                               }
                               fullScreen={true}
                               maxWidth="md"
                               show={true}
                               onEscapeKeyDown={() => {
                                   history.goBack() // TODO: We need to check if back is a different page, else goto ../../
                               }}>
                           <ServiceRecipientTasks/>
                       </EccoModal>
                   )}/>*/}

            <Route path={`${basepath}access`} component={pageComponentFactory("access")} />

            <Route path={`${basepath}units`} component={pageComponentFactory("units")} />
            <Route path={`${basepath}staff`} component={pageComponentFactory("staff")} />
            <Route path={`${basepath}residents`} component={pageComponentFactory("residents")} />
            <Route path={`${basepath}checksDue`} component={pageComponentFactory("checksDue")} />
            <Route
                path={`${basepath}checksHistory`}
                component={pageComponentFactory("checksHistory")}
            />

            <Route
                path={`${basepath}**`}
                component={() => <h3>Task not found {`basepath: ${basepath}`}</h3>}
            />
        </Switch>
    );
};
