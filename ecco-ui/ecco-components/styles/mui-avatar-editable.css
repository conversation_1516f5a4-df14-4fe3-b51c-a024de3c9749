.mui-avatar-editable .MuiAvatar-root img {
    width: 100%;
    height: 100%;
}

.mui-avatar-editable .image-drop,
.mui-avatar-editable .image-overlay {
  min-height: 50%;
  text-align: center;
  /*padding-top: 20px;*/
}
.mui-avatar-editable .image-drop p {
  top: -50px;
  position: relative;
  color: white;
}

.mui-avatar-editable .image-overlay {
  z-index: 1;
  margin: -4px;
  width: 100%;
  height: 100%;
  background: #eeeeee; /* Fallback solid colour */
  background: rgba(238, 238, 238, 0.75);
  border-radius: 50%;
}
.mui-avatar-editable .image-drop > .placeholder {
}

.mui-avatar-editable .image-overlay > .placeholder {
    padding: 16px;
}

.mui-avatar-editable {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);
}
.mui-avatar-editable {
  display: block;
  padding: 4px;
  /*margin-bottom: 20px;*/
  line-height: 1.2;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 50%;
  transition: border 0.2s ease-in-out;
}
