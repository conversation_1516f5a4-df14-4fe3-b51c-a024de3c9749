// from https://gist.github.com/Danziger/336e75b6675223ad805a88c2dfdcfd4a
import {EffectCallback, MutableRefObject, useEffect, useRef} from 'react';

/**
 * Use setTimeout with <PERSON><PERSON> in a declarative way.
 *
 * @see https://stackoverflow.com/a/59274757/3723993
 * @see https://overreacted.io/making-setinterval-declarative-with-react-hooks/
 */
export function useTimeout(callback: EffectCallback, delaySecs: number): MutableRefObject<number | null> {
    const timeoutRef = useRef<number | null>(null);
    const callbackRef = useRef(callback);

    // Remember the latest callback:
    //
    // Without this, if you change the callback, when setTimeout kicks in, it
    // will still call your old callback.
    //
    // If you add `callback` to useEffect's deps, it will work fine but the
    // timeout will be reset.
    useEffect(() => {
        callbackRef.current = callback;
    }, [callback]);

    // Set up the timeout:

    useEffect(() => {
        timeoutRef.current = window.setTimeout(() => callbackRef.current(), delaySecs * 1000);

        // Clear timeout if the components is unmounted or the delay changes:
        return () => window.clearTimeout(timeoutRef.current || 0);
    }, [delaySecs]);

    // In case you want to manually clear the timeout from the consuming component...:
    return timeoutRef;
}