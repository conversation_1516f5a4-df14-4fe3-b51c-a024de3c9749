import {EvidenceGroup} from "ecco-dto/evidence-dto";
import {FormDefinition} from "ecco-dto/form-definition-dto";
import {Reducer, useEffect, useMemo, useReducer} from "react";
import {useServicesContext} from "../ServicesContext";
import {useReferralBySrId} from "./entityLoadHooks";
import {
    CollectionSubscription,
    CollectionSubscriptionState,
    DomainObject,
    FormEvidenceWork,
    SubscriptionStateReducerActions,
    SubscriptionStateEntry,
    subscriptionStateReducer,
    CollectionMutator
} from "ecco-commands";


export function useFormDefinition(formDefinitionUuid: string | null): FormDefinition | null {
    const {sessionData} = useServicesContext();
    return formDefinitionUuid ? sessionData.findFormDefinition(formDefinitionUuid) : null;
}

/** Warning: currently uses referral by service recipient id, so it can work offline */
export function useFormDefinitionBySrId(srId: number, taskName: string) {
    const {sessionData} = useServicesContext();
    const {referral} = useReferralBySrId(srId);
    if (!referral) return null;
    const uuid = sessionData
        .getServiceTypeByServiceCategorisationId(referral.serviceAllocationId)
        .getTaskDefinitionSetting(taskName, "formDefinition");
    if (!uuid) {
        console.info("Custom form not configured for %s - %s", srId, taskName);
        return null;
    }
    const definition = sessionData.findFormDefinition(uuid);
    if (!definition)
        console.error("Could not find form definition %s for %s - %s", uuid, srId, taskName);
    return definition;
}

function errorHandler(it: any) {
    // TODO
    console.error(it);
}

/** For use with useReducer<StateReducer<T>>(...) */
type StateReducer<T> = Reducer<CollectionSubscriptionState<T>, SubscriptionStateReducerActions<T>>;


export function useCollectionSubscription<T extends DomainObject>(
    subscription: CollectionSubscription<T>
): CollectionSubscriptionState<T> {
    const [state, dispatch] = useReducer<StateReducer<T>>(subscriptionStateReducer, null);
    const collectionMutator: CollectionMutator<void, T> = {
        add(objectKey: string, newDomainObject: T): void {
            console.debug("add", newDomainObject)
            dispatch({
                type: "add",
                key: objectKey,
                value: newDomainObject
            });
        },
        change(objectKey: string, change: (object: T) => T) {
            console.debug("change - key = ", objectKey);
            dispatch({
                type: "modify",
                key: objectKey,
                mutator: change
            });
        },
        remove(objectKey: string): void {
            console.debug("delete")
            dispatch({
                type: "delete",
                key: objectKey
            });
        }
    };
    useEffect(() => {
        console.debug("Fetching snapshots for useCollectionSubscription")
        subscription.getSnapshots().then(orderedEntries => {
            console.debug("Setting initial entries", orderedEntries)
            dispatch({type: "initial", initial: orderedEntries});
            const effects = subscription.getEffects();
            effects.subscribe(effect => {
                effect.applyToCollection(collectionMutator);
            }, errorHandler);
            effects.connect();
        });
    }, [subscription, dispatch]);
    return state;
}


export function useFormDataContext(
    serviceRecipientId: number,
    evidenceGroup: EvidenceGroup
): {formEvidence: SubscriptionStateEntry<FormEvidenceWork> | null; loaded: boolean} {
    const {getEvidenceEffectsRepository} = useServicesContext();
    const subscription = useMemo(
        () =>
            getEvidenceEffectsRepository().findLatestFormSnapshotByServiceRecipientId(
                serviceRecipientId,
                evidenceGroup
            ),
        [serviceRecipientId, evidenceGroup.name]
    );
    const x = useCollectionSubscription(subscription);
    return useMemo(() => {
        if (!x) return {loaded: false, formEvidence: null};
        else {
            // Hackily return the first result
            const iterator = x[Symbol.iterator]();
            const next = iterator.next();
            return {loaded: true, formEvidence: next.done ? null : next.value[1]};
        }
    }, [x, serviceRecipientId, evidenceGroup.name]);
}
