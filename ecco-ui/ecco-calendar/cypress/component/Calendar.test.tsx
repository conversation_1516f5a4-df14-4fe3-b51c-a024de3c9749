import '../../wdyr'; // <--- first import

import {mount} from "cypress/react";
import * as React from "react";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {EccoAPI} from "ecco-components/EccoAPI";
import {getFailAllMethodsMock} from "ecco-components/test-support/mock-utils";
import {
    CalendarAjaxRepository,
    EventResourceDto,
    FeatureSetDto,
    PersonUserSummary,
    ServiceRecipientAjaxRepository,
    SessionData,
    SessionDataDto,
    WorkersAjaxRepository
} from "ecco-dto";
import {EccoDate} from "@eccosolutions/ecco-common";
import {calendarWithMsDataShifted} from "../../testUtils";
import {CalendarWithStaffOverlay} from "../../calendars/Calendar";
import {ServiceRecipient} from "ecco-dto/service-recipient-dto";

const calData = calendarWithMsDataShifted();

const calendarRepository = getFailAllMethodsMock(CalendarAjaxRepository);
let expectedContactIds;
let fetchCounter = 0;
calendarRepository.fetchCalendarsByContactIds = (
    contactIds: number[],
    start: EccoDate,
    end: EccoDate
) => {
    // note when method is called
    console.log("calling fetchCalendarsByContactIds");

    // TODO fetchCounter gets loaded twice on initial load - can't see what triggers it
    // fudge data when method is called, but can't get access outside component
    fetchCounter++;
    // const data = calendarWithMsData as EventResourceDto[]
    // data[0].location = ""+fetchCounter;

    if (contactIds.length != expectedContactIds) {
        throw Error("contactId has not changed");
    }

    if (contactIds.length == 1) {
        return Promise.resolve([calData[0]] as EventResourceDto[]);
    } else {
        return Promise.resolve(calData as EventResourceDto[]);
    }
};
const serviceRecipientRepository = getFailAllMethodsMock(ServiceRecipientAjaxRepository);
serviceRecipientRepository.findOneServiceRecipientById = (srId: number) => {
    const d = {
        contactId: 99,
        contactName: "Me - 99",
        calendarId: "my-cal-id"
    } as any as ServiceRecipient;
    return Promise.resolve(d);
};

const contact = {
    contactId: 1,
    calendarId: "sdfsdfsdf",
    contactName: "my name"
};
const person1: PersonUserSummary = {
    individualId: 1,
    displayName: "my name",
    calendarId: "calId"
} as PersonUserSummary;
const person2: PersonUserSummary = {
    individualId: 2,
    displayName: "my other friend",
    calendarId: "calId2"
} as PersonUserSummary;
const workersRepository = getFailAllMethodsMock(WorkersAjaxRepository);
workersRepository.findWorkersWithSameAccess = (sessionData, excludeMe) =>
    Promise.resolve([person1, person2]);

const sessionDataDto: SessionDataDto = {} as SessionDataDto;
sessionDataDto.featureSets = {global: {} as FeatureSetDto};
sessionDataDto.softwareModulesEnabled = {};
sessionDataDto.softwareModulesEnabled["hact"] = {name: "hact", enabled: false};
sessionDataDto.listDefinitions = {};
sessionDataDto.listDefinitions["eventCategory"] = [
    {
        id: 500,
        name: "box call",
        listName: "eventCategory",
        businessKey: "500",
        disabled: false,
        defaulted: false,
        order: null,
        // NB isIconClassSuccess checks for 'fa-check-circle'
        metadata: {iconClasses: null, value: null, displayName: null}
    }
];
sessionDataDto.serviceTypesById = {};
sessionDataDto.individualUserSummary = person1;
const sessionData = new SessionData(sessionDataDto);

const overrides = {
    sessionData: sessionData,
    calendarRepository: calendarRepository,
    workersRepository,
    serviceRecipientRepository
} as any as EccoAPI;

function clickOverlay() {
    cy.then(() => expectedContactIds++);
    // prepare to intercept the 'alert' in showErrorAsAlert, because any error we throw causes a popup which cypress consumes
    let alerted: boolean | string = false;
    cy.on("window:alert", msg => (alerted = msg));
    // click the list
    cy.get('[id="eccotest-staffAvail"]').should("have.length", 1).parent().click();
    // click 'my other friend'
    cy.get('[id="eccotest-staffAvail-popup"]')
        .find("li")
        .should("have.length", 1)
        .click()
        // autocomplete creates a completely new element in 'MuiAutocomplete-listbox'
        .then(() => expect(alerted).to.be.eq(false));
}

describe("render calendars", () => {
    it("calendar", () => {
        expectedContactIds = 1;
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CalendarWithStaffOverlay />
                {/*<Typography>{`fetch counter: ${fetchCounter}`}</Typography>*/}
            </TestServicesContextProvider>
        );
        cy.contains("alldayer");

        // click a staff overla
        clickOverlay();

        // TODO check fetchEvents called initially, when click new date period, and per refresh, and per staff selected

        // TODO check ms events not editable/clickable, only ad-hoc
        // TODO check ad-hoc pops up, closes only fetchEvents if saved
    });

    it("calendar on client", () => {
        expectedContactIds = 2;
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CalendarWithStaffOverlay serviceRecipientId={99} hideBackToFile={true} />
            </TestServicesContextProvider>
        );
        cy.contains("alldayer");

        clickOverlay();
    });
});
