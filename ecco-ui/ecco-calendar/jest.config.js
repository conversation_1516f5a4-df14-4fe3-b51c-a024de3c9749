// This should be specified in Configurations -> Templates -> Jest as the config file
module.exports = {
    globals: {
        "ts-jest": {
            tsconfig: "tsconfig.json"
        }
    },
    transform: {
        "^.+\\.tsx?$": "ts-jest"
    },
    testRegex: ".*/__tests__/.*([Tt]est|[Ss]pec)\\.(tsx?)$",
    setupFiles: ["./__tests__/setupJest.ts"],
    moduleFileExtensions: ["ts", "tsx", "js", "json", "node"],
    moduleNameMapper: {
        bowser: "<rootDir>/__mocks__/bowser"
    },
    modulePaths: ["<rootDir>"],
    snapshotSerializers: ["enzyme-to-json/serializer"]
    // collectCoverage: true,
    // mapCoverage: true
};
