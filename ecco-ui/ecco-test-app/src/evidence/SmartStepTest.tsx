import * as React from "react";
import {FC, useState} from "react";
import {Paper} from "@eccosolutions/ecco-mui";
import {
    SmartStep,
    SmartStepInit,
    SmartStepState,
} from "ecco-evidence";
import {testSmartStepInit} from "./testUtils";

/**
 * Layout of the SmartStep with debug info
 */
const SmartStepTestLayout: FC = () => {
    // create/hold the props
    const init: SmartStepInit = testSmartStepInit();
    const [state, setState] = useState<SmartStepState>(init.initState);
    const stateSetter = (update: Partial<SmartStepState>) => {
        setState({...state, ...update});
    };

    return (
        <>
            <Paper elevation={2}>
                <SmartStep
                    init={init}
                    state={state}
                    stateSetter={stateSetter}
                    remove={() => {}}
                />
            </Paper>
            <Paper elevation={2}>
                <p>state: {JSON.stringify(state)}</p>
            </Paper>
        </>
    );
};

/**
 * NB See SmartStepCommandForm for reasons we're not testing that here - we'd need a context
 */
export const SmartStepTest: FC = () => {
    return <>
        <SmartStepTestLayout/>
    </>
}
