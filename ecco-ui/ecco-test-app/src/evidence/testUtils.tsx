import {
    ActionDefDto,
    ActionGroupDto,
    ConfigResolverDefault,
    EvidenceGroup,
    EvidencePageType,
    FeatureSetDto,
    Messages,
    OutcomeDto,
    Question,
    QuestionAnswerFree,
    QuestionGroup,
    ServiceType,
    ServiceTypeDto,
    SessionData,
    SessionDataDto
} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {QuestionGroupParametersDto, TaskDefinition} from "ecco-dto/service-config-dto";
import {
    ActionInstanceControlDataSetup,
    ActionInstanceFeatures,
    EvidenceContext,
    EvidenceDef,
    SmartStepData,
    SmartStepInit,
    SmartStepState,
    SmartStepStatusTransitions
} from "ecco-evidence";

const freetext1: QuestionAnswerFree = {
    id: 1,
    // unused: minimum,maximum
    valueType: "textarea" // "textarea" | "text" | "number" | "checkbox" | "money" | "integer";
};
export const question1: Question = {
    id: 1,
    name: "question1",
    disabled: false,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [freetext1], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [], //QuestionAnswerChoice[],
    parameters: {}
};

export const questiongroup1: QuestionGroup = {
    id: 1,
    name: "questiongroup1",
    disabled: false,
    questions: [question1],
    parameters: {} as QuestionGroupParametersDto
};

export const action1: ActionDefDto = {
    id: 1,
    //uuid?: string;
    name: "action1",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
    //statusChangeReasonListName?: string;
};
export const action2: ActionDefDto = {
    id: 2,
    name: "Clean bedroom",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
};
export const actiongroup1: ActionGroupDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "actiongroup1",
    orderby: 1,
    disabled: false,
    actions: [action1, action2]
};
export const outcome1: OutcomeDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "be healthy",
    disabled: false,
    actionGroups: [actiongroup1]
};

export const dummyText = `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Id nibh tortor id aliquet. Sed lectus vestibulum mattis ullamcorper velit sed. Nec nam aliquam sem et tortor. Elit eget gravida cum sociis natoque. Ipsum a arcu cursus vitae. Massa sapien faucibus et molestie ac feugiat sed. Turpis egestas maecenas pharetra convallis posuere morbi leo. Duis at consectetur lorem donec massa sapien faucibus et molestie. Purus sit amet luctus venenatis lectus. Pellentesque nec nam aliquam sem et tortor consequat id porta. Sapien et ligula ullamcorper malesuada. Fringilla ut morbi tincidunt augue interdum velit euismod. Vitae turpis massa sed elementum tempus egestas sed. Netus et malesuada fames ac turpis egestas integer eget aliquet. Blandit cursus risus at ultrices mi tempus imperdiet nulla malesuada. Aliquam eleifend mi in nulla posuere sollicitudin aliquam ultrices sagittis.
Nisi est sit amet facilisis. Vitae congue eu consequat ac felis. Facilisis mauris sit amet massa vitae tortor condimentum lacinia. Nisl condimentum id venenatis a condimentum. Rutrum tellus pellentesque eu tincidunt tortor. Semper auctor neque vitae tempus quam. Pellentesque adipiscing commodo elit at imperdiet dui accumsan sit. Mi ipsum faucibus vitae aliquet nec ullamcorper sit. Enim lobortis scelerisque fermentum dui faucibus. Volutpat ac tincidunt vitae semper quis lectus nulla at.
Pellentesque id nibh tortor id aliquet lectus proin. Blandit cursus risus at ultrices mi. Iaculis eu non diam phasellus vestibulum lorem. Et odio pellentesque diam volutpat commodo. Sodales ut etiam sit amet nisl purus in. Adipiscing elit ut aliquam purus sit. Bibendum enim facilisis gravida neque convallis a cras. Duis at consectetur lorem donec massa sapien faucibus et molestie. Massa tincidunt nunc pulvinar sapien et ligula ullamcorper malesuada proin. Non odio euismod lacinia at quis risus sed vulputate odio. Vel turpis nunc eget lorem dolor sed viverra ipsum. A lacus vestibulum sed arcu.
Odio aenean sed adipiscing diam. Volutpat ac tincidunt vitae semper quis lectus. Tristique magna sit amet purus. Euismod in pellentesque massa placerat duis. Tellus molestie nunc non blandit massa enim nec dui nunc. Faucibus pulvinar elementum integer enim neque volutpat ac tincidunt vitae. Tempus quam pellentesque nec nam aliquam sem et tortor consequat. Ac felis donec et odio pellentesque diam volutpat commodo sed. Orci sagittis eu volutpat odio. Purus faucibus ornare suspendisse sed nisi lacus. Nulla facilisi etiam dignissim diam quis enim lobortis scelerisque. Habitant morbi tristique senectus et netus et. Porta non pulvinar neque laoreet suspendisse. Nunc scelerisque viverra mauris in aliquam sem fringilla ut morbi. Tellus integer feugiat scelerisque varius. Pulvinar pellentesque habitant morbi tristique senectus et netus et malesuada. Pharetra et ultrices neque ornare aenean euismod. Proin sagittis nisl rhoncus mattis rhoncus.
Nisi quis eleifend quam adipiscing vitae. Eget mauris pharetra et ultrices neque ornare aenean euismod elementum. Non arcu risus quis varius quam quisque id diam. Pharetra magna ac placerat vestibulum. Lobortis feugiat vivamus at augue eget arcu dictum varius duis. Sed id semper risus in hendrerit gravida. Ultrices eros in cursus turpis massa. Non tellus orci ac auctor augue mauris augue. Arcu vitae elementum curabitur vitae nunc. Convallis a cras semper auctor neque vitae tempus quam. Varius quam quisque id diam vel.`;

const serviceTypeDto: ServiceTypeDto = {} as ServiceTypeDto;
serviceTypeDto.supportOutcomes = [outcome1];
serviceTypeDto.riskAreas = [];
serviceTypeDto.questionGroups = [questiongroup1];
serviceTypeDto.taskDefinitionEntries = [];
export const serviceType = new ServiceType(serviceTypeDto, {} as Messages, () => outcome1);

const sessionDataDto: SessionDataDto = {} as SessionDataDto;
sessionDataDto.featureSets = {global: {} as FeatureSetDto};
sessionDataDto.listDefinitions = {};
const taskDefNeedsAssessment: TaskDefinition = {
    id: 1,
    type: "EVIDENCE_SUPPORT",
    name: "needsAssessment",
    description: "does a needs assessment",
    display: true,
    displayOverview: true,
    internal: false,
    metadata: {}
};
sessionDataDto.taskDefinitions = [taskDefNeedsAssessment];
sessionDataDto.serviceTypesById = {};

sessionDataDto.serviceTypesById["1"] = serviceTypeDto;
sessionDataDto.supportOutcomes = [outcome1];
sessionDataDto.listDefinitions["listName"] = [
    {
        id: 188,
        name: "success",
        listName: "listName",
        businessKey: "188",
        disabled: false,
        defaulted: false,
        order: null,
        metadata: {iconClasses: "fa-check-circle", value: "0", displayName: ""}
    }
];

export const sessionData = new SessionData(sessionDataDto);

export const configResolver = () =>
    ConfigResolverDefault.fromLegacyServiceType(serviceType, sessionData);

/**
 * Default init data.
 * NB We could create a SmartStepData (or use a real API) and use createSmartStepInit.
 */
export function testSmartStepInit(): SmartStepInit {
    // const outcome = new Outcome(outcome1);
    // const actionGroup = new ActionGroup(actiongroup1, outcome);
    // const action = new Action(action1, actionGroup);

    const srId = 99;

    const evidenceDef = new EvidenceDef(
        "needsAssessment",
        EvidencePageType.assessment,
        EvidenceGroup.needs,
        undefined
    );

    const smartStepData: SmartStepData = {
        snapshot: null,
        actionDefId: action1.id,
        sessionData: sessionData,
        evidenceDef: evidenceDef,
        configResolver: configResolver(),
        getWorkUuid: () => Uuid.randomV4(),
        serviceRecipientId: srId,
        readOnly: false
    };

    const smartStepStateBase = ActionInstanceControlDataSetup.fromEmpty(false, null);
    const smartStepState: SmartStepState = {
        ...smartStepStateBase,
        actionDefId: action1.id,
        controlUuid: Uuid.randomV4(),
        // parentControlUuid
        related: false,
        relatedDirectClick: false,
        editing: false
    };

    return {
        initData: smartStepData,
        initState: smartStepState,
        context: {} as EvidenceContext,
        controlFeatures: {} as ActionInstanceFeatures,
        statusTransitions: new SmartStepStatusTransitions(evidenceDef.getEvidencePageType()!, false)
    };
}
