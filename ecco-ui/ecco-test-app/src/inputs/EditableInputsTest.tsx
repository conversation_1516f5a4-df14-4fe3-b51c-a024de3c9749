import {Avatar, Grid, Typography} from "@eccosolutions/ecco-mui/index";
import {EccoTextInput} from "ecco-components-core";
import {AssignmentOutlined, PersonIcon} from "@eccosolutions/ecco-mui-controls";
import {ClickToEditTextField} from "ecco-components/inputs/clickToEditFields";
import * as React from "react";
import {useState} from "react";
import {ButtonMenu} from "ecco-components/menu/ButtonMenu";
import {useDebounce} from "ecco-components-core/hooks/useDebounce";

export const EditableInputsTest = () => {
    const [text, setText] = useState<string | null>(null);
    const [num, setNum] = useState<string | null>("420");
    const debounce3Secs = useDebounce(text, 3000)

    const [selectedIndex, setSelectedIndex] = useState<number|null>(null);
    const options = [
        <><Avatar style={{marginRight: 8}}><PersonIcon/></Avatar><Typography><PERSON></Typography></>,
        <><Avatar style={{marginRight: 8}}><AssignmentOutlined/></Avatar><Typography>Jane Staff</Typography></>
    ];

    const optionNames = ["Jim Staff", "Jane Staff"];

    return <Grid container>
        <Grid item>
            <ClickToEditTextField
                label="label"
                value={text}
                onChange={value => {
                    console.log(value);
                    setText(value);
                }}
            />
        </Grid>
        <Grid item>
            <ClickToEditTextField
                label="age in years"
                value={num}
                type="number"
                onChange={value => {
                        console.log(value);
                        setNum(value);
                }}
            />
        </Grid>
        <Grid item>
            <ButtonMenu
                selectedIndex={selectedIndex}
                onClick={(i) => alert("Allocated " + optionNames[i!])}
                onSelection={setSelectedIndex}
                options={options}
            >
                {selectedIndex !== null ? <em>Allocate: {optionNames[selectedIndex]}</em> : "Allocate..."}
            </ButtonMenu>
        </Grid>
        <Grid item>
            <EccoTextInput propertyKey="comment" label="comment (auto-save)" value={text} onChange={setText} type="textarea" rows={8} autoSaveKey="comment"/>
        </Grid>
        <Grid item>
            debounce by 3 secs: {debounce3Secs}
        </Grid>
    </Grid>;
};