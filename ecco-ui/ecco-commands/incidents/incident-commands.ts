import {EccoDate} from "@eccosolutions/ecco-common";
import {PrefixType} from "ecco-dto/service-recipient-dto";
import {BooleanChange, CommandDto, IncidentDto, NumberChangeOptional, StringChangeOptional} from "ecco-dto";
import {CommandDtoServer} from "ecco-dto/evidence-dto";
import {
    BaseServiceRecipientTaskUpdateCommand,
    CreateServiceRecipientCommand
} from "../referral/commands";
import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";

export interface CreateIncidentCommandDto extends CommandDto, CommandDtoServer {
    prefix: PrefixType;
    incidentViewModel: IncidentDto;
}

export class CreateIncidentCommand extends CreateServiceRecipientCommand {
    private static prefix: PrefixType = "i";
    public static discriminator = "createSvcRec"; // as per CreateServiceRecipientCommand

    constructor(protected incident: IncidentDto) {
        super(
            CreateIncidentCommand.prefix,
            `service-recipient/command/create/${CreateIncidentCommand.prefix}/`
        );
    }

    public toDto(): CreateIncidentCommandDto {
        return {
            commandName: CreateIncidentCommand.discriminator,
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            prefix: this.prefix,
            incidentViewModel: this.incident
        };
    }
}

// used to create the above IncidentDetailCommandViewModel
export class IncidentDetailCommand extends BaseServiceRecipientTaskUpdateCommand {
    receivedDateChange: StringChangeOptional;
    reportedByChange: StringChangeOptional;
    reportedByContactChange: StringChangeOptional;
    categoryIdChange: NumberChangeOptional;
    emergencyServicesInvolvedChange: BooleanChange;
    hospitalisationInvolvedChange: BooleanChange;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "incidentDetails", taskHandle);
    }

    public changeReportedBy(from: string, to: string) {
        this.reportedByChange = this.asStringChange(from, to);
        return this;
    }
    public changeReportedByContact(from: string, to: string) {
        this.reportedByContactChange = this.asStringChange(from, to);
        return this;
    }

    public changeReceivedDate(from: EccoDate | null, to: EccoDate | null) {
        this.receivedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changeCategoryId(from: number, to: number) {
        this.categoryIdChange = this.asNumberChange(from, to);
        return this;
    }

    public changeEmergencyServicesInvolved(from: boolean, to: boolean) {
        this.emergencyServicesInvolvedChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeHospitalisationInvolved(from: boolean, to: boolean) {
        this.hospitalisationInvolvedChange = this.asBooleanChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.receivedDateChange != null ||
            this.reportedByChange != null ||
            this.reportedByContactChange != null ||
            this.categoryIdChange != null ||
            this.emergencyServicesInvolvedChange != null ||
            this.hospitalisationInvolvedChange != null
        );
    }

    public toDto(): IncidentDetailCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            receivedDate: this.receivedDateChange,
            reportedBy: this.reportedByChange,
            reportedByContact: this.reportedByContactChange,
            categoryId: this.categoryIdChange,
            hospitalisationInvolved: this.hospitalisationInvolvedChange,
            emergencyServicesInvolved: this.emergencyServicesInvolvedChange
        };
    }
}
export interface IncidentDetailCommandDto extends ServiceRecipientTaskBaseCommandDto {
    receivedDate: StringChangeOptional;
    reportedBy: StringChangeOptional;
    reportedByContact: StringChangeOptional;
    categoryId: NumberChangeOptional;
    hospitalisationInvolved: BooleanChange;
    emergencyServicesInvolved: BooleanChange;
}
