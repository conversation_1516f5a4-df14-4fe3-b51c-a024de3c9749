
export type SubscriptionStateReducerActions<T> =
    | {type: "initial"; initial: ReadonlyArray<[string, T]>}
    | {type: "add"; key: string; value: T}
    | {type: "modify"; key: string; mutator: (prev: T) => T}
    | {type: "delete"; key: string};

/** Tracks the latest (e.g. draft or offline) version of an entity alongside the version last retrieved */
export type SubscriptionStateEntry<T> = {readonly current: T; readonly committed: T};

export type CollectionSubscriptionState<T> = Map<string, SubscriptionStateEntry<T>> | null;

const initialValue = <T extends any>(obj: T) => ({current: obj, committed: obj});

function toInitialEntry<T>(entry: [string, T]): [string, SubscriptionStateEntry<T>] {
    return [entry[0], initialValue(entry[1])];
}

function updatedValue<T>(committed: T, current: T) {
    return {current, committed};
}

export function subscriptionStateReducer<T>(
    prev: CollectionSubscriptionState<T>,
    action: SubscriptionStateReducerActions<T>
) {
    const copy = prev == null ? new Map<string, SubscriptionStateEntry<T>>() : new Map(prev)
    switch (action.type) {
        case "initial":
            return new Map(action.initial.map(toInitialEntry));
        case "add":
            return copy.set(action.key, initialValue(action.value));
        case "modify":
            const prev = copy.get(action.key)!;
            console.debug("modifying", prev)
            const updated = action.mutator(prev.current);
            const result = copy.set(action.key, updatedValue(prev.committed, updated));
            console.debug("modify result=", result)
            return result;
        case "delete":
            copy.delete(action.key);
            return copy;
    }
}
