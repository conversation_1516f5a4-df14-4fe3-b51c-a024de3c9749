
// typically we had:
//      return Promise.all(promises.toArray()).then(() => ras);
//      where reduce was an oft-used pattern to replace Promise.all
// typically we had:
//      eg resolve all the promises (which work on the original object, which is then returned)
//      const allQ = promises.reduce((chainedQ: Promise<void>, currQ: Promise<void>) =>
//        chainedQ.then(() => currQ.then())
//        , Promise.resolve(null)); // resolve to anything, since it's not used
//      return allQ.then(() => ras);
// BUT this still whizzes through throwing out all the promises instantly
// because we need a promise-generating loop, which this method provides.
//      eg chainedQ.then(() => generateQ())
// what we need is recursion, 'return processItem(index + 1)'

/**
 * Promise.all floods requests to the browser, as does reduce, unless a method generates the promise (see d37ab938)
 * This processes promises truly sequentially (ie it doesn't throw all the promises to the browser).
 * Most examples give promises sequentially, but it's important to have recursion to be sensible.
 * See above notes, and JIRA, eg https://css-tricks.com/why-using-reduce-to-sequentially-resolve-promises-works/.
 * NB its important 'items' is not passed Promise<>[], otherwise the promises have already been created - nullifying the benefit
 */
export function sequentialMapAll<T, R>(items: T[], asyncOperation: (item: T) => Promise<R>): Promise<R[]> {
    const results: R[] = [];

    // NB its important 'items' is not a Promise<>[], otherwise the promises have already been created - nullifying the benefit of this method
    if (items.length > 0 && isPromise(items[0])) {
        console.warn("sequentialMapAll will not be effective in this call");
    }

    const processItem = (index: number): Promise<R[]> => {
        // if we're called from chunkArray then items could consist of an empty array
        // so check we have something in it otherwise we create a request with nothing
        const noItems = items.length == 0 || (Array.isArray(items[0]) && items[0].length == 0);
        if (noItems) {
            return Promise.resolve(results);
        }
        return index < items.length
            ? asyncOperation(items[index]) // if we return void, note that we also 'results.push' nothing and return an array of nothing
                  .then(result => {
                      results.push(result);
                      return processItem(index + 1);
                  })
                  .catch(e => {
                      console.error(e);
                      throw e;
                  })
            : Promise.resolve(results);
    };
    return processItem(0);
}

export function parallelMapAll<T, R>(items: T[], asyncOperation: (item: T) => Promise<R>) {
    const promises = items
        .map(asyncOperation)
        .map(promise => promise
            .catch(e => {
                console.error(e);
                return e; // Adds error to the results
            }));
    return Promise.all(promises);
}

export function isPromise(obj: any): obj is Promise<any> {
    return (<Promise<any>>obj).then !== undefined;
}
