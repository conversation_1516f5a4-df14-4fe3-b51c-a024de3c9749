import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceGroup} from "ecco-dto";
import {BaseServiceRecipientCommandDto} from "ecco-dto/evidence-dto";
import {BaseUpdateCommand} from "../cmd-queue/commands";

function assertNotNull(thing: any, msg: string) {
    if (!thing) {
        throw new Error("NPE: " + msg);
    }
}

/** Command-based data-transfer object representing a command to send an email.
 *
 * Must match the Java class com.ecco.webApi.email.SendEmailCommandViewModel. */
export interface SendEmailCommandDto extends BaseServiceRecipientCommandDto {
    workUuid: string;

    /** The subject line */
    subject: string;

    /** The body - plain text */
    body: string;

    /** ids/keys of recipients - for now this is expected to be from a ListDef list against which email addresses
     *  are listed.  Null means to just use default fallback email address */
    recipientListIds?: number[];
}

/** For building SendEmailCommandDto */
export class SendEmailCommand extends BaseUpdateCommand {
    public static discriminator = "sendEmail";

    constructor(
        private workUuid: Uuid,
        private serviceRecipientId: number,
        taskName: string,
        evidenceGroup: EvidenceGroup,
        private subject: string,
        private body: string
    ) {
        super(
            `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/${taskName}/emails/`
        );
    }

    public toDto(): SendEmailCommandDto {
        assertNotNull(this.getUuid(), "SendEmailCommand.uuid");
        assertNotNull(this.workUuid, "SendEmailCommand.workUuid");
        return {
            commandName: SendEmailCommand.discriminator,
            uuid: this.getUuid().toString(),
            workUuid: this.workUuid.toString(),
            serviceRecipientId: this.serviceRecipientId,
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            subject: this.subject,
            body: this.body
        };
    }
}
