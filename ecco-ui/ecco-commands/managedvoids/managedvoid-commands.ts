import {EccoDate} from "@eccosolutions/ecco-common";
import {PrefixType} from "ecco-dto/service-recipient-dto";
import {CommandDto, ManagedVoidDto, StringChangeOptional} from "ecco-dto";
import {CommandDtoServer} from "ecco-dto/evidence-dto";
import {
    BaseServiceRecipientTaskUpdateCommand,
    CreateServiceRecipientCommand
} from "../referral/commands";
import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";

export interface CreateManagedVoidCommandDto extends CommandDto, CommandDtoServer {
    prefix: PrefixType;
    managedVoidViewModel: ManagedVoidDto;
}

export class CreateManagedVoidCommand extends CreateServiceRecipientCommand {
    private static prefix: PrefixType = "mv";
    public static discriminator = "createSvcRec"; // as per CreateServiceRecipientCommand

    constructor(protected managedVoid: ManagedVoidDto) {
        super(
            CreateManagedVoidCommand.prefix,
            `service-recipient/command/create/${CreateManagedVoidCommand.prefix}/`
        );
    }

    public toDto(): CreateManagedVoidCommandDto {
        return {
            commandName: CreateManagedVoidCommand.discriminator,
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            prefix: this.prefix,
            managedVoidViewModel: this.managedVoid
        };
    }
}

// used to create the above ManagedVoidDetailCommandViewModel
export class ManagedVoidDetailCommand extends BaseServiceRecipientTaskUpdateCommand {
    receivedDateChange: StringChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "managedVoidDetails", taskHandle);
    }

    public changeReceivedDate(from: EccoDate | null, to: EccoDate | null) {
        this.receivedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.receivedDateChange != null;
    }

    public toDto(): ManagedVoidDetailCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            receivedDate: this.receivedDateChange
        };
    }
}
export interface ManagedVoidDetailCommandDto extends ServiceRecipientTaskBaseCommandDto {
    receivedDate: StringChangeOptional;
}
