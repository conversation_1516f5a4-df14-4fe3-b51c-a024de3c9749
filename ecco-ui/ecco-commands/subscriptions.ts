import {Change, CommandEffect, Delete, New} from "./commands";
import {ConnectableObservable, Observable} from "rxjs";
import {DomainObject} from "./DomainObject";

function logError(e: unknown) {
    console.error("Error: %o", e);
}

export interface ObjectSubscription<TDomainObject extends DomainObject> {
    getSnapshot(): Promise<TDomainObject | null>;
    getEffects(): Observable<CommandEffect<TDomainObject>>;
}

export interface CollectionSubscription<TDomainObject extends DomainObject> {
    getSnapshots(): Promise<ReadonlyArray<[string, TDomainObject]>>;
    getEffects(): ConnectableObservable<CommandEffect<TDomainObject>>;
}

export function objectSubscription<TDomainObject extends DomainObject>(
    objectKey: string,
    snapshot: Promise<TDomainObject | null>,
    ...effects: readonly Observable<CommandEffect<any>>[]
): ObjectSubscription<TDomainObject> {
    if (objectKey == null) {
        throw new TypeError("Object key must not be null.");
    }

    if (snapshot == null) {
        throw new TypeError("Promise for snapshot must not be null.");
    }

    const objectEffects = Observable.concat(...effects)
            .filter(effect => objectKey == effect.getObjectKey());

    return {
        getSnapshot: () => snapshot,
        getEffects: () => objectEffects
    };
}

/**
 *
 * @param shouldAddToThisSubscription called when an effect is <code>New</code> to check if it applies to this
 * subscription.  This is required because potentially all effects for the system are seen by this subscription.
 * @param snapshots
 * @param effects
 */
export function collectionSubscription<TDomainObject extends DomainObject>(
    shouldAddToThisSubscription: (newDomainObject: TDomainObject) => boolean,
    snapshots: Promise<ReadonlyArray<TDomainObject>>,
    ...effects: readonly Observable<CommandEffect<any>>[]
): CollectionSubscription<TDomainObject> {
    if (shouldAddToThisSubscription == null) {
        throw new TypeError("shouldAddToThisSubscription must not be null.");
    }

    if (snapshots == null) {
        throw new TypeError("snapshot must not be null.");
    }

    if (effects == null) {
        throw new TypeError("effects must not be null.");
    }

    function effectCreatesNewRelevantObject(effect: CommandEffect<any>): boolean {
        const result =
            effect instanceof New && shouldAddToThisSubscription(effect.getDomainObject());
        if (result) {
            console.debug("effectCreatesNewRelevantObject = y : %o", effect);
        }
        return result;
    }

    function effectIsRelevant(effect: CommandEffect<any>): boolean {
        return (
            effectCreatesNewRelevantObject(effect) ||
            effect instanceof Change ||
            effect instanceof Delete
        );
    }

    let allEffects = Observable.concat(...effects)
        .do(e => {
            console.debug("allEffects: item %o", e);
        })

    let relevantEffects = allEffects
            .filter(effectIsRelevant)
            .do(e => {
                console.debug("relevantEffects: item %o", e);
            }, logError)
            .publish();

    console.debug("collectionSubscription: %o", shouldAddToThisSubscription);
    return {
        // filter or investigate why non-null on delete evidence requests
        getSnapshots: () => snapshots.then(snaps => snaps.filter(w => w).map(t => [t.getKey(), t])),
        getEffects: () => relevantEffects
    };
}
