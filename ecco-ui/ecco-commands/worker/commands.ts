import {PrefixType} from "ecco-dto/service-recipient-dto";
import {CommandDto, NumberChangeOptional, StaffJobDto, StringChangeOptional} from "ecco-dto";
import {CommandDtoServer} from "ecco-dto/evidence-dto";
import {
    BaseServiceRecipientTaskUpdateCommand,
    CreateServiceRecipientCommand
} from "../referral/commands";
import {EccoDate} from "@eccosolutions/ecco-common";
import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";

export interface CreateWorkerJobCommandDto extends CommandDto, CommandDtoServer {
    prefix: PrefixType;
    workerJobViewModel: StaffJobDto;
}

export class CreateWorkerJobCommand extends CreateServiceRecipientCommand {
    private static prefix: PrefixType = "w";
    public static discriminator = "createSvcRec"; // as per CreateServiceRecipientCommand

    constructor(protected workerJob: StaffJobDto) {
        super(
            CreateWorkerJobCommand.prefix,
            `service-recipient/command/create/${CreateWorkerJobCommand.prefix}/`
        );
    }

    public toDto(): CreateWorkerJobCommandDto {
        return {
            commandName: CreateWorkerJobCommand.discriminator,
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            prefix: this.prefix,
            workerJobViewModel: this.workerJob
        };
    }
}

// used to create the above WorkerJobDetailCommandViewModel
export class WorkerJobDetailCommand extends BaseServiceRecipientTaskUpdateCommand {
    codeChange: StringChangeOptional;
    startDateChange: StringChangeOptional;
    endDateChange: StringChangeOptional;
    contractedWeeklyHours: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "jobDetails", taskHandle);
    }

    public changeCode(from: string, to: string) {
        this.codeChange = this.asStringChange(from, to);
        return this;
    }

    public changeStartDate(from: EccoDate, to: EccoDate) {
        this.startDateChange = this.asDateChange(from, to);
        return this;
    }

    public changeEndDate(from: EccoDate, to: EccoDate) {
        this.endDateChange = this.asDateChange(from, to);
        return this;
    }

    public changeContractedWeeklyHours(from: number, to: number) {
        this.contractedWeeklyHours = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.codeChange != null ||
            this.startDateChange != null ||
            this.endDateChange != null ||
            this.contractedWeeklyHours != null
        );
    }

    public toDto(): WorkerJobDetailCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            code: this.codeChange,
            startDate: this.startDateChange,
            endDate: this.endDateChange,
            contractedWeeklyHours: this.contractedWeeklyHours
        };
    }
}
export interface WorkerJobDetailCommandDto extends ServiceRecipientTaskBaseCommandDto {
    code: StringChangeOptional;
    startDate: StringChangeOptional;
    endDate: StringChangeOptional;
    contractedWeeklyHours: NumberChangeOptional;
}
