import {SignWorkCommandDto} from "ecco-dto/evidence-dto";
import {Signature} from "./Signature"
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceCommand} from "./EvidenceCommand";
import {CommandEffect, New} from "../commands";
import {AttachSignatureChange} from "./AttachSignatureChange";
import {EvidenceGroup} from "ecco-dto";

export class SignWorkCommand extends EvidenceCommand {
    public static discriminator = "signWork";

    private readonly serviceRecipientId: number;

    private readonly evidenceGroup: string;
    private readonly evidenceTask: string;

    private workUuids: Uuid[];

    private signature: New<Signature>;

    constructor(uuid: Uuid, serviceRecipientId: number, evidenceGroup: string, evidenceTask: string, workUuids: Uuid[], signature: New<Signature>) {
        let effects: CommandEffect<any>[] = [];
        effects.push(signature);
        effects = effects.concat(workUuids
                .map(workUuid => new AttachSignatureChange(workUuid, Uuid.parse(signature.getDomainObject().getKey()))));

        // uri to match SignatureCommandController.signWork()
        // TODO: This should change or be removed as signatures are done on history
        const uri = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup}/${evidenceTask}/signatures/`;

        super(uuid, effects, uri, SignWorkCommand.discriminator);

        this.serviceRecipientId = serviceRecipientId;
        this.workUuids = workUuids;
        this.signature = signature;
        this.evidenceGroup = evidenceGroup;
        this.evidenceTask = evidenceTask;
    }

    public static create(serviceRecipientId: number, evidenceGroup: EvidenceGroup, evidenceTask: string, workUuids: Uuid[], signature: New<Signature>) {
        return new SignWorkCommand(Uuid.randomV4(), serviceRecipientId, evidenceGroup.name, evidenceTask, workUuids, signature);
    }
    public static fromDto(dto: SignWorkCommandDto): SignWorkCommand {

        let signature = new Signature(Uuid.parse(dto.signatureUuid),
                dto.svgXml,
                EccoDateTime.parseIso8601Utc(dto.signedDate));

        let workUuids = dto.workUuids.map(uuid => Uuid.parse(uuid));

        return new SignWorkCommand(Uuid.parse(dto.uuid), dto.serviceRecipientId, dto.evidenceGroup, dto.evidenceTask!, workUuids, new New<Signature>(signature));
    }

    protected getCommandDto(): SignWorkCommandDto {
        return {
            commandName: SignWorkCommand.discriminator,
            commandUri: this.commandUri,
            uuid: this.uuid.toString(),
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            evidenceGroup: this.evidenceGroup,
            evidenceTask: this.evidenceTask,
            workUuids: this.workUuids.map(workUuid => workUuid.toString()),
            signatureUuid: this.signature.getDomainObject().getKey().toString(),
            svgXml: this.signature.getDomainObject().getSvgXml(),
            signedDate: this.signature.getDomainObject().getSignedDateTime().formatIso8601()
        };
    }
}
