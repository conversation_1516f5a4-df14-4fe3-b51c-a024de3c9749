import {Uuid} from "@eccosolutions/ecco-crypto";
import {Change} from "../commands";
import {SupportWork} from "./domain";

export class AttachSignatureChange extends Change<SupportWork> {
    constructor(workUuid: Uuid, signatureUuid: Uuid) {
        super(workUuid.toString(), work => {
            const update = {...work.getDto()};
            update.signatureId = signatureUuid.toString();
            return new SupportWork(workUuid, update);
        });
    }
}

