import {ApiClient} from "../web-api";
import {Agency, Contact, Individual} from "../contact-dto";
import {ContactChangeDto} from "./commands";
import {Result, SparseArray} from "@eccosolutions/ecco-common";


export class ContactsAjaxRepository {
    private cache: SparseArray<Promise<Individual>> = {};
    private cacheAgency: SparseArray<Promise<Agency>> = {};

    constructor(private apiClient: ApiClient) {}

    findOneContact(contactId: number): Promise<Contact> {
        var contact = this.cache[contactId];
        if (!contact) {
            contact = this.apiClient.get<Individual>(`contacts/${contactId}/`);
            this.cache[contactId] = contact;
        }
        return contact;
    }

    findAllContactsByContactId(contactIds: number[] | string[]): Promise<Contact[]> {
        return this.apiClient.get<Contact[]>("contacts/", {
            query: {
                ids: contactIds.join(",")
            }
        });
    }

    findAllCommandsByContactId(contactId: number): Promise<ContactChangeDto[]> {
        return this.apiClient.get<ContactChangeDto[]>(`contacts/${contactId}/commands/`);
    }

    findOneAgency(agencyId?: number | null | undefined): Promise<Agency | null> {
        if (!agencyId) return Promise.resolve(null);

        var agency = this.cacheAgency[agencyId];
        if (!agency) {
            agency = this.apiClient.get<Agency>(`agencies/${agencyId}/`);
            this.cacheAgency[agencyId] = agency;
        }
        return agency;
    }

    /**
     * @param contextId The serviceRecipientId used to find the agencies around it (ie via service allocation)
     */
    findAllAgencies(contextId?: number | undefined): Promise<Agency[]> {
        const options = !contextId ? undefined : {query: {contextId: `${contextId}`}};
        return this.apiClient.get<Agency[]>("agencies/", options);
    }

    findAllIndividualsByCompanyId(companyId: number): Promise<Individual[]> {
        if (!companyId) return Promise.resolve([]);

        return this.apiClient.get<Individual[]>("individuals/", {
            query: {companyId: companyId.toString()}
        });
    }

    findAllIndividualsByIndividualId(individualIds: number[] | string[]): Promise<Individual[]> {
        return this.apiClient.get<Individual[]>("individuals/", {
            query: {
                id: individualIds.join(",")
            }
        });
    }

    /**
     * @param contextId The serviceRecipientId used to find the professionals around it (ie via service allocation)
     */
    findAllProfessionals(contextId?: number | undefined): Promise<Individual[]> {
        const options = !contextId ? undefined : {query: {contextId: `${contextId}`}};
        return this.apiClient.get<Individual[]>("individuals/professionals/", options);
    }

    findOneIndividual(individualId?: number | null | undefined): Promise<Individual | null> {
        if (!individualId) return Promise.resolve(null);

        return this.apiClient.get<Individual>(`individuals/${individualId}/`);
    }

    saveIndividual(individual: Individual): Promise<{id: number}> {
        return this.apiClient.post<{id: number}>("individuals/", individual);
    }

    // See UserController
    createUserFromContact(contactId: number, username: string): Promise<Result> {
        return this.apiClient.post(`individuals/${contactId}/user/${username}/`, {});
    }

    saveAgency(agency: Agency): Promise<{id: number}> {
        return this.apiClient.post<{id: number}>("agencies/", agency);
    }
}
