/**
 * @deprecated (and duplicated in scripts). To be migrated to URL
 */
declare module "URI" {
    function URIStatic(): URIStatic.URI;
    function URIStatic(uri: string): URIStatic.URI;
    function URIStatic(uri: URIStatic.URI): URIStatic.URI;
    function URIStatic(uri: URIStatic.URIParts): URIStatic.URI;
    // function URIStatic(relativeUri: string, baseUri: string): URIStatic.URI;
    // function URIStatic(relativeUri: string, baseUri: URIStatic.URI): URIStatic.URI;
    // function URIStatic(relativeUri: string, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URI, baseUri: string): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URI, baseUri: URIStatic.URI): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URI, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URIParts, baseUri: string): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URIParts, baseUri: URIStatic.URI): URIStatic.URI;
    // function URIStatic(relativeUri: URIStatic.URIParts, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;

    module URIStatic {
        export interface URIParts {
            protocol?: string | undefined;
            username?: string | undefined;
            password?: string | undefined;
            hostname?: string | undefined;
            port?: string | undefined;
            path?: string | undefined;
            query?: string | undefined;
            fragment?: string | undefined;
        }

        export interface AbsoluteURIParts extends URIParts {
            protocol: string;
            hostname: string;
        }

        export interface URI {
            clone(): URI;
            href(): string;
            href(href: string): URI;
            toString(): string;
            valueOf(): string;
            scheme(): string;
            scheme(scheme: string): URI;
            // protocol(): string;
            // protocol(protocol: string): URI;
            // username(): string;
            // username(username: string): URI;
            // password(): string;
            // password(password: string): URI;
            // hostname(): string;
            // hostname(hostname: string): URI;
            // port(): string;
            // port(port: string): URI;
            // host(): string;
            // host(host: string): URI;
            // userinfo(): string;
            // userinfo(userinfo: string): URI;
            authority(): string;
            authority(authority: string): URI;
            // domain(): string;
            // domain(secondLevelDomain: boolean): string;
            // domain(domain: string): URI;
            // subdomain(): string;
            // subdomain(subdomain: string): URI;
            // tld(): string;
            // tld(secondLevelDomain: boolean): string;
            // tld(tld: string): URI;
            path(): string;
            path(decode: boolean): string;
            path(path: string): URI;
            // pathname(): string;
            // pathname(decode: boolean): string;
            // pathname(pathname: string): URI;
            // directory(): string;
            // directory(decode: boolean): string;
            // directory(directory: string): URI;
            // filename(): string;
            // filename(decode: boolean): string;
            // filename(filename: string): URI;
            // suffix(): string;
            // suffix(decode: boolean): string;
            // suffix(suffix: string): URI;
            segment(): string[];
            segment(i: number): string;
            segment(segments: string[]): URI;
            segment(segment: string): URI;
            segment(i: number, segment: string): URI;
            segmentCoded(): string[];
            segmentCoded(i: number): string;
            segmentCoded(segments: string[]): URI;
            segmentCoded(segment: string): URI;
            segmentCoded(i: number, segment: string): URI;
            query(): string;
            query(decode: boolean): Object;
            query(query: string): URI;
            query(query: Object): URI;
            // query(mutator: (query: Object) => void): URI;
            // query(mutator: (query: Object) => Object): URI;
            search(): string;
            search(decode: boolean): { [key: string]: string };
            search(search: string): URI;
            search(search: Object): URI;
            // search(mutator: (search: Object) => void): URI;
            // search(mutator: (search: Object) => Object): URI;
            fragment(): string;
            fragment(fragment: string): URI;
            hash(): string;
            hash(hash: string): URI;
            // resource(): string;
            // resource(resource: string): URI;
            is(query: string): boolean;
            setQuery(key: string, value: string): URI;
            setQuery(key: string, value: string[]): URI;
            setQuery(query: Object): URI;
            setSearch(key: string, value: string): URI;
            setSearch(key: string, value: string[]): URI;
            setSearch(search: Object): URI;
            addQuery(key: string, value: string): URI;
            addQuery(key: string, value: string[]): URI;
            addQuery(query: Object): URI;
            addSearch(key: string, value: string): URI;
            addSearch(key: string, value: string[]): URI;
            addSearch(search: Object): URI;
            removeQuery(key: string): URI;
            removeQuery(key: string, value: string): URI;
            removeQuery(key: string, value: string[]): URI;
            removeQuery(key: string[]): URI;
            removeQuery(query: Object): URI;
            removeSearch(key: string): URI;
            removeSearch(key: string, value: string): URI;
            removeSearch(key: string[]): URI;
            removeSearch(search: Object): URI;
            hasQuery(key: string): boolean;
            hasQuery(key: string, value: string): boolean;
            hasQuery(key: string, value: string[]): boolean;
            hasQuery(key: string, match: RegExp): boolean;
            // hasQuery(key: string, value: string, inList: boolean): boolean;
            // hasQuery(key: string, value: string[], inList: boolean): boolean;
            // hasQuery(key: string, match: RegExp, inList: boolean): boolean;
            // hasQuery(key: string, predicate: (value: string) => boolean): boolean;
            // hasQuery(key: string, predicate: (value: string, name: string) => boolean): boolean;
            // hasQuery(key: string, predicate: (value: string, name: string, query: Object) => boolean): boolean;
            hasSearch(key: string): boolean;
            hasSearch(key: string, value: string): boolean;
            hasSearch(key: string, value: string[]): boolean;
            hasSearch(key: string, match: RegExp): boolean;
            hasSearch(key: string, value: string, inList: boolean): boolean;
            hasSearch(key: string, value: string[], inList: boolean): boolean;
            hasSearch(key: string, match: RegExp, inList: boolean): boolean;
            // hasSearch(key: string, predicate: (value: string) => boolean): boolean;
            // hasSearch(key: string, predicate: (value: string, name: string) => boolean): boolean;
            // hasSearch(key: string, predicate: (value: string, name: string, search: Object) => boolean): boolean;
            normalize(): URI;
            // normalizeProtocol(): URI;
            // normalizeHostname(): URI;
            // normalizePort(): URI;
            // normalizePath(): URI;
            // normalizePathname(): URI;
            // normalizeQuery(): URI;
            // normalizeSearch(): URI;
            // normalizeFragment(): URI;
            // normalizeHash(): URI;
            // iso8859(): URI;
            // unicode(): URI;
            // readable(): string;
            relativeTo(baseUri: string): URI;
            relativeTo(baseUri: URI): URI;
            relativeTo(baseUri: AbsoluteURIParts): URI;
            absoluteTo(baseUri: string): URI;
            absoluteTo(baseUri: URI): URI;
            absoluteTo(baseUri: AbsoluteURIParts): URI;
            equals(uri: string): boolean;
            equals(uri: URI): boolean;
            equals(uri: URIParts): boolean;
            duplicateQueryParameters(duplicateQueryParameters: boolean): URI;
        }

        // export class URITemplate {
        //     constructor(template: string);
        //
        //     expand(values: Object): URI;
        //     expand(callback: (key: string) => string): URI;
        // }

        export var defaultPorts: Object;
        export var duplicateQueryParameters: boolean;

        export function parse(uri: string): URIStatic.URIParts;
        export function parseAuthority(uri: string, parts: URIStatic.URIParts): string;
        export function parseUserinfo(uri: string, parts: URIStatic.URIParts): string;
        export function parseHost(uri: string, parts: URIStatic.URIParts): string;
        export function parseQuery(uri: string): { [key: string]: any; };
        export function build(parts: URIStatic.URIParts): URIStatic.URI;
        export function buildAuthority(parts: URIStatic.URIParts): string;
        export function buildUserinfo(parts: URIStatic.URIParts): string;
        export function buildHost(parts: URIStatic.URIParts): string;
        export function buildQuery(data: Object, duplicates?: boolean): string;
        export function encode(value: string): string;
        export function decode(value: string): string;
        export function encodeReserved(value: string): string;
        export function encodeQuery(value: string): string;
        export function decodeQuery(value: string): string;
        export function addQuery(query: Object, key: string, value: string): void;
        export function addQuery(query: Object, key: string, value: string[]): void;
        export function addQuery(query: Object, values: Object): void;
        export function removeQuery(query: Object, key: string): void;
        export function removeQuery(query: Object, key: string, value: string): void;
        export function removeQuery(query: Object, key: string, value: string[]): void;
        export function removeQuery(query: Object, key: string[]): void;
        export function removeQuery(query: Object, values: Object): void;
        export function commonPath(path1: string, path2: string): string;
        export function withinString(text: string, callback: (uri: URIStatic.URI) => string): string;
        // export function iso8859(): void;
        // export function unicode(): void;
        // export function expand(uriTemplate: string, values: Object): URIStatic.URI;
        // export function expand(uriTemplate: string, callback: (key: string) => string): URIStatic.URI;
    }

    // function new URIStatic (): URIStatic.URI;
    // function new URIStatic (uri: string): URIStatic.URI;
    // function new URIStatic (uri: URIStatic.URI): URIStatic.URI;
    // function new URIStatic (uri: URIStatic.URIParts): URIStatic.URI;
    // function new URIStatic (relativeUri: string, baseUri: string): URIStatic.URI;
    // function new URIStatic (relativeUri: string, baseUri: URIStatic.URI): URIStatic.URI;
    // function new URIStatic (relativeUri: string, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URI, baseUri: string): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URI, baseUri: URIStatic.URI): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URI, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URIParts, baseUri: string): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URIParts, baseUri: URI): URIStatic.URI;
    // function new URIStatic (relativeUri: URIStatic.URIParts, baseUri: URIStatic.AbsoluteURIParts): URIStatic.URI;

    export = URIStatic;
}
