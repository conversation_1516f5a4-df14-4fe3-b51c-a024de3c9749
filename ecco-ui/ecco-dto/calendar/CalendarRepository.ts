
import {EventResourceDto} from "../calendar-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";

export interface CalendarRepository {
    fetchCalendarsByContactIds(
        contactIds: number[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]>;

    /** Fetch nearby (-3 days -> +2 wks) calendar events for specifiedcalendar */
    nearby(calendarId: string, nearby?: EccoDate | undefined): Promise<EventResourceDto[]>;

    /** Similar to nearby, but allows a range (NB this isn't cached for 15 secs) */
    fetchEventsByCalendarIdAndDate(
        calendarIds: string[],
        start: EccoDate,
        end: EccoDate
    ): Promise<EventResourceDto[]>;

    fetchEventsById(eventIds: string[]): Promise<EventResourceDto[]>;

    fetchEventsByCalendarId(
        calendarIds: string[],
        start: EccoDateTime,
        end: EccoDateTime
    ): Promise<EventResourceDto[]>;
}
