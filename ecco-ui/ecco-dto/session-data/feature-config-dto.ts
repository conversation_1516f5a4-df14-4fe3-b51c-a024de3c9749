import {
    AppointmentTypeDto,
    OutcomeDto,
    ProjectDto,
    QuestionGroup,
    RiskAreaDto,
    ServiceCategorisation,
    ServiceDto,
    ServiceTypeDto,
    TaskDefinition
} from "../service-config-dto";
import {
    IdName,
    IdNameDisabled,
    StringToObjectMap,
    StringToStringMap
} from "@eccosolutions/ecco-common";
import {PersonUserSummary} from "../contact-dto";
import {Messages} from "../messages/Messages";
import {FormDefinition} from "../form-definition-dto";

export type LoginProviders = "azure" | "google"
export interface PublicConfigDto {
    loginProviders: {[key in LoginProviders]?: boolean}
}

export interface UserSessionDataPlainFields {
    /** e.g. fnightingale */
    username: string;
    userId: number;

    /** CalendarId so we can request nearby appointments for this user */
    calendarId: string;

    /**
     * Unique reference to the native object of the user (calendarId) who created the event.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    calendarIdUserReferenceUri: string;
}

/* This interface must match the Java class com.ecco.webApi.viewModels.ServicesViewModel. */
interface ServicesDto {
    services: ServiceDto[];
}


/** top-level groups - those with ROLE_USER */
export type UserGroup =
    | "staff"
    | "manager"
    | "senior manager"
    | "reports"
    | "rota"
    | "site sysadmin"
    | "carer"
    | "daily checks"
    | "client"
    | "commissioner"
    | "hr"
    | "security"
    | "useradmin"
    | "sysadmin"
    | "hr-volunteers";

export interface UserSessionDataSecretFields {
    groups: UserGroup[];

    /** e.g. ROLE_STAFF */
    roles: string[];

    /**
     * Deprecated, since we have combined the approaches into restrictedServiceCategorisations
     */
    //restrictedServicesProjects: ServicesDto;

    /**
     * A client side representation of what the user has access to.
     * Can be used for both serviceProject-customers and serviceCategorisation-customers.
     * NB In making it compatible, we allow null projects to come through.
     */
    restrictedServiceCategorisations: ServiceCategorisation[];

    individualUserSummary: PersonUserSummary;
}

interface UnitOfMeasurementDto extends IdName {
    description: string;
    unitMeasurement: string;
    units: number;
}

export interface GlobalConfigSecretFields {
    messages: Messages;

    featureSets: {
        global: FeatureSet;
    };

    softwareModulesEnabled: StringToObjectMap<SoftwareModule>;

    listDefinitions: StringToObjectMap<ListDefinitionEntryDto[]>;

    taskDefinitions: TaskDefinition[];

    serviceTypesById: StringToObjectMap<ServiceTypeDto>;

    formDefinitionsById: StringToObjectMap<FormDefinition>;

    services: ServiceDto[];

    projects: ProjectDto[];

    allocatedWorkerContacts?: IdNameLegacy[] | undefined;

    serviceCategorisations: ServiceCategorisation[];

    appointmentTypes: AppointmentTypeDto[];

    fundingSources: IdNameDisabled[];

    // Note was this before strict type checks    settings: {namespace: string; key: string; value: string}[];
    /** lookup as value = settings[namespace + ":" + key] */
    settings: StringToStringMap;

    supportOutcomes: OutcomeDto[];

    /** The outcome terminology for a threat */
    riskAreas: RiskAreaDto[];

    questionGroups: QuestionGroup[];

    unitOfMeasurements: UnitOfMeasurementDto[];
}

export type SessionDataSecretFields = UserSessionDataSecretFields & GlobalConfigSecretFields;

export interface SessionDataPlainFields extends UserSessionDataPlainFields {}


/** This interface must match the Java class com.ecco.webApi.featureConfig.UserSessionDataViewModel. */
export type UserSessionDataDto = UserSessionDataPlainFields & UserSessionDataSecretFields;

/** This interface must match the Java class com.ecco.webApi.featureConfig.GlobalConfigViewModel. */
export interface GlobalConfig extends GlobalConfigSecretFields {}

/** This interface must match the Java class com.ecco.webApi.featureConfig.SessionDataViewModel. */
export interface SessionDataDto extends UserSessionDataDto, GlobalConfig {}

export interface ListDefinitionEntryDto {
    id: number;
    /** The name/key. This shouldn't be changed */
    name: string;
    listName: string;
    businessKey: string;
    disabled: boolean;
    defaulted: boolean;
    order: number | null;
    /** value of the parent id - null if this does not have a parent */
    parentId?: number | undefined;
    metadata?:
        | {
              iconClasses: string;
              colour?: string | undefined;
              formDefUuid?: string | undefined;
              value: string; // used by lookupScoreValue for ProgressLineChart and now clientStatusOkay if 'okay'
              /** text to override name with for display */
              displayName: string;
              /** concept that this (external) flag is also turning on another (internal) flag */
              proxyForId?: number | undefined;
              /** for the incidents for the INCIDENT_CATEGORY_LISTNAME */
              significantIncident?: boolean | undefined;
          }
        | undefined;
}

/** This interface must match the Java class com.ecco.webApi.featureConfig.FeatureViewModel. */
export interface Feature {
    name: string;
    description: string;
    defaultVote: "ENABLED_BY_DEFAULT" | "DISABLED_BY_DEFAULT" | "ABSTAIN" | "ALWAYS_ENABLED" | "ALWAYS_DISABLED";
}

/** This interface must match the Java class com.ecco.webApi.featureConfig.SoftwareModuleViewModel. */
export interface SoftwareModule {
    name: string;
    enabled: boolean;
}

/** This interface must match the Java class com.ecco.webApi.featureConfig.FeatureSetViewModel. */
export interface FeatureSet {
    featureVotes: StringToObjectMap<Feature>;
}

/**
 * @deprecated Use the one in ecco-common
 */
export interface IdNameLegacy{
    id: number;
    name: string;
}
