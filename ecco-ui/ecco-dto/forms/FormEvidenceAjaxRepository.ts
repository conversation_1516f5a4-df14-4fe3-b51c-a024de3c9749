import {EccoDateTime, Slice} from "@eccosolutions/ecco-common";
import {CustomFormFields, EvidenceGroup, FormEvidence} from "../evidence-dto";
import {FormDefinition} from "../form-definition-dto";
import {FormEvidenceRepository} from "./FormEvidenceRepository";
import {ApiClient, handle404AsNullResult} from "../web-api";


export class FormEvidenceAjaxRepository implements FormEvidenceRepository {
    constructor(private apiClient: ApiClient) {}

    /** @deprecated Use SessionDataDto.formDefinitions[uuid] */
    public findFormDefinition(uuid: string): Promise<FormDefinition | null> {
        if (!uuid) {
            return Promise.resolve(null);
        }
        const apiPath = `formDef/${uuid.toString()}/`;
        return this.apiClient.get<FormDefinition>(apiPath).then(formDef => {
            if (!formDef) {
                throw new Error("FormDefinition invalid for: " + uuid);
            }
            return formDef;
        });
    }

    /** /service-recipients/{serviceRecipientId}/evidence/form/{evidenceGroupKey}/snapshots/latest/ */
    public findLatestFormEvidenceSnapshotByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FormEvidence<any> | null> {
        let apiPath = `service-recipients/${serviceRecipientId}/evidence/form/${evidenceGroup.name}/snapshots/latest/`;

        return this.apiClient
            .get<FormEvidence<CustomFormFields>>(apiPath)
            .catch(handle404AsNullResult);
    }

    /** /service-recipients/{serviceRecipientId}/evidence-form/{evidenceGroupKey}/{snapshotWorkUuid}/ */
    public findOneFormEvidenceWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        snapshotWorkUuid: string,
        findLastSignedSnapshot?: boolean | undefined
    ): Promise<FormEvidence<any> | null> {
        let apiPath = `service-recipients/${serviceRecipientId}/evidence-form/${evidenceGroup.name}/${snapshotWorkUuid}/`;

        // there is no reason to limit findLastSigned to a snapshot, but that's all we need
        const options = !findLastSignedSnapshot
            ? undefined
            : {query: {findLastSignedSnapshot: "true"}};

        return this.apiClient
            .get<FormEvidence<CustomFormFields>>(apiPath, options)
            .catch(handle404AsNullResult);
    }

    // for getting evidence notes between two points - used to add notes to a print-out for a previously signed custom form
    public findAllBetweenFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        fromDateTime: EccoDateTime,
        toDateTime: EccoDateTime
    ): Promise<FormEvidence<any>[]> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence-form/${evidenceGroup.name}/`;
        const options = {
            query: {
                fromDateTime: fromDateTime.formatIso8601(),
                toDateTime: toDateTime.formatIso8601()
            }
        };

        return this.apiClient
            .get<Slice<FormEvidence<any>>>(apiPath, options)
            .catch(handle404AsNullResult)
            .then(slice => {
                return slice ? slice.content : [];
            });
    }

    /** For the purposes of custom form history */
    public findAllFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup?: EvidenceGroup | undefined,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined
    ): Promise<FormEvidence<any>[]> {
        let apiPath = !!evidenceGroup
            ? `service-recipients/${serviceRecipientId}/evidence-form/${evidenceGroup.name}/`
            : `service-recipients/${serviceRecipientId}/evidence-form/`;

        if (attachmentsOnly) {
            apiPath = apiPath + "attachments/";
        }

        const options = !pageNumber ? undefined : {query: {page: pageNumber.toString()}};

        return this.apiClient
            .get<Slice<FormEvidence<any>>>(apiPath, options)
            .catch(handle404AsNullResult)
            .then(slice => {
                return slice ? slice.content : [];
            });
    }

    // deleteByWorkUuidWithServiceRecipientId(workUuid, referral.serviceRecipientId))
    public deleteByWorkUuidWithServiceRecipientId(workUuid: string, serviceRecipientId: number) {
        return this.apiClient.del(`evidence/form/${workUuid}/`, {
            serviceRecipientId: serviceRecipientId
        });
    }
}
