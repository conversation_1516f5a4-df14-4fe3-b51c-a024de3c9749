// const config = require("@softwareventures/webpack-config");
//
// module.exports = config({
//     title: "Ecco Commands",
//     vendor: "ecco",
//     entry: "./index.ts"
// });

const path = require("path");
const TerserJsPlugin = require("terser-webpack-plugin");

const projectDir = __dirname;
const distDir = path.resolve(projectDir, "./dist/");
const debugDir = path.resolve(projectDir, "./debug/");

function configuration(mode) {
    const configuration = {
        mode: mode,
        name: mode === "production" ? "prod" : "dev",
        entry: "./index.ts",
        externals: [
            "@material-ui/core", // exclude these as they should be consumed via ecco-mui which should change less
            "@material-ui/styles",
            // we allow @material-ui/icons here for now,
            "application-properties",
            "bowser",
            /ecco-[a-z-]+$/, // works for @eccosolutions/ecco- too, but don't want .css .png etc to be externalised
            "lodash", // We actually shouldn't have this, but webpack will put all of it in if we allow it to
            "moment",
            "qr-scanner", // we include via require-boot since the dynamic load of the 'worker' module fails via war / jar
            "react",
            "react-async",
            "react-bootstrap",
            "react-dom",
            "react-router",
            "react-router-dom"
        ],
        module: {
            rules: [
                {
                    test: /\.js$/,
                    loader: "esbuild-loader",
                    options: {
                        // presets: ["@babel/preset-env"]
                        target: "es2015"
                    }
                },
                {
                    test: /\.css$/i,
                    use: ["style-loader", "css-loader"] // style- is for putting stuff into a <style> tag, css-loader is for reading the .css file
                },
                {
                    // COPIED from Create-React-App react-scripts/config/webpack.config.js
                    // "url" loader works like "file" loader except that it embeds assets
                    // smaller than specified limit in bytes as data URLs to avoid requests.
                    // A missing `test` is equivalent to a match.
                    test: [/\.png$/],
                    loader: require.resolve("url-loader"),
                    options: {
                        limit: true // Unlimited - don't use file loader
                        // name: 'static/media/[name].[hash:8].[ext]',
                    }
                },
                {
                    test: /\.svg$/,
                    use: {
                        loader: "svg-url-loader",
                        options: {
                            encoding: "base64"
                        }
                    }
                },
                {
                    test: /\.tsx?$/,
                    loader: "esbuild-loader",
                    options: {
                        loader: "tsx", // Or 'ts' if you don't need tsx
                        target: "es2015"
                    },
                    exclude: /(^|\/)node_modules\//
                }
            ]
        },
        resolve: {
            extensions: [".tsx", ".ts", ".js", ".png", ".svg"]
        },
        output: {
            path: mode === "production" ? distDir : debugDir,
            filename: "ecco-components-core.js",
            libraryTarget: "umd",
            devtoolModuleFilenameTemplate: "[resource-path]?[loaders]"
        }
    };

    if (mode === "production") {
        configuration.optimization = {
            minimizer: [
                new TerserJsPlugin({
                    parallel: true,
                    terserOptions: {
                        compress: {
                            passes: 2
                        }
                    }
                })
            ]
        };
    } else {
        configuration.devtool = "inline-source-map";
    }

    return configuration;
}

module.exports = [configuration("production"), configuration("development")];
