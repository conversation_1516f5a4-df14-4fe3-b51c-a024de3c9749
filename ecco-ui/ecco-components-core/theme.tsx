import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    create<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC} from "react";
import {hashCode} from "ecco-dto";

// directly from https://stackoverflow.com/questions/11120840/hash-string-into-rgb-color
export function stringToColorRGB(val: string) {
    const hash = hashCode(val);
    const r = (hash & 0xFF0000) >> 16;
    const g = (hash & 0x00FF00) >> 8;
    const b = hash & 0x0000FF;
    return {r, g, b}
}

// directly from https://github.com/mui/material-ui/issues/12700
// NB adapted for stringAvatar backgrounColor (mui 4)
// or https://stackoverflow.com/questions/11120840/hash-string-into-rgb-color
export function stringToColor(string: string) {
    let hash = 0;
    let i;

    /* eslint-disable no-bitwise */
    for (i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
        const value = (hash >> (i * 8)) & 0xff;
        color += `00${value.toString(16)}`.slice(-2);
    }
    /* eslint-enable no-bitwise */

    return color;
}

const colourOverride = localStorage.getItem("colourOverride") || undefined;
const muiThemeBase = {
    props: {
        MuiInputLabel: {
            shrink: true
        },
        MuiUseMediaQuery: {
            // see https://github.com/mui-org/material-ui/issues/21142
            noSsr: true
        },
        MuiFormControl: {
            // e.g. Select
            variant: "outlined",
            size: "small"
        },
        MuiTextField: {
            fullWidth: true,
            variant: "outlined",
            size: "small",
            margin: "none" // Use <Grid> for layout with spacing as set below
        } /*,
        MuiGrid: {
            spacing: 2
        }*/
    },
    breakpoints: {
        values: {
            xs: 0,
            sm: 600,
            md: 1025, // so we get iPad landscape as sm
            lg: 1280,
            xl: 1920
        }
    },
    palette: {
        background: {
            default: colourOverride || "#fafafa",
            paper: colourOverride || "#fff"
        },
        primary: {
            main: "#0d83ca" // and can do diff for light and dark
        },
        type: "light" // which base theme
    },
    overrides: {
        MuiCardHeader: {
            title: {fontSize: 17} // Think this is ignored
        },
        MuiDialogContent: {
            root: {
                maxWidth:
                    "100vw" /* So that we don't size content according to width of scrolling components behind */
            }
        },
        MuiDrawer: {
            paperAnchorLeft: {
                width: 256
            },
            paperAnchorDockedLeft: {
                width: 256,
                // MUI default has changed from boxShadow to borderRight which makes white background show through
                borderRight: "initial",
                boxShadow: "rgba(0, 0, 0, 0.12) 0px 1px 6px, rgba(0, 0, 0, 0.12) 0px 1px 4px"
            }
        },
        MuiFormControl: {
            root: {}
        },
        MuiFormLabel: {
            root: {
                background: colourOverride || "white"
                // color: "#064f7bcc"
            },
            asterisk: {
                color: "red"
            }
        },
        MuiLinearProgress: {
            bar1Determinate: {
                color: "orange"
            }
        },
        MuiFilledInput: {
            root: {
                // could do: backgroundColor: "#0d83ca17"
            }
        },
        MuiInputBase: {
            root: {
                "&$disabled": {color: "rgba(0, 0, 0, 0.7)"}
            }
        },
        MuiTab: {
            root: {
                "backgroundColor": colourOverride || "white",
                "&$selected": {color: "#0d83ca"},
                // tab width - see https://stackoverflow.com/a/53262952
                "minWidth": 0,
                "@media (min-width: 0px)": {
                    minWidth: 0
                }
            },
            textColorPrimary: {color: "rgba(13, 131, 202, 0.7)"}
        },
        MuiTypography: {
            h5: {
                // for CardHeader title and subtitle
                fontSize: "1.4em"
            },
            subtitle1: {
                color: "darkgray"
            }
        }
    }
    // textField: {floatingLabelColor: "rgba(0,0,0,0.6)"},
    // tabs: {backgroundColor: "white", selectedTextColor: "#0d83ca", textColor: "rgba(13, 131, 202, 0.7)" }
};

const muiThemeNormal = {
    ...muiThemeBase, // shallow copy
    props: {
        ...muiThemeBase.props,
        MuiGrid: {
            spacing: 2
        }
    }
};

// needs casting to ThemeOptions from "@eccosolutions/ecco-mui
// @ts-ignore
const muiTheme = createMuiTheme(muiThemeNormal);

// @ts-ignore
export const muiPagePlanTheme = createMuiTheme(muiThemeBase);

export const darkTheme = createMuiTheme({
    props: muiTheme.props,
    breakpoints: muiTheme.breakpoints,
    overrides: {
        MuiToolbar: {
            root: {
                color: colourOverride || "white",
                height: 64 // Default is minHeight 64 (or 56 for <600px width),
            },
            gutters: {
                paddingLeft: 8,
                paddingRight: 8
            }
        }
    },
    palette: {
        primary: {
            main: "#0d83ca" // and can do diff for light and dark
        },
        type: "dark"
    }
});

/** Use this to avoid a clash with other JSS themes that have also defaulted to jss prefix in production */
export const EccoTheme: FC<{prefix?: string | undefined}> = props => {
    const gen = createGenerateClassName({
        // disableGlobal: true, // should prevents ecco clashing with itself but seems broken
        productionPrefix: props.prefix || "ecco" // prevents ecco-clashing with other apps (and itself when we have disconnected ReactDOM.renders)
    });
    return (
        <StylesProvider generateClassName={gen}>
            <ThemeProvider theme={muiTheme}>{props.children}</ThemeProvider>
        </StylesProvider>
    );
};
EccoTheme.displayName = "EccoTheme";
