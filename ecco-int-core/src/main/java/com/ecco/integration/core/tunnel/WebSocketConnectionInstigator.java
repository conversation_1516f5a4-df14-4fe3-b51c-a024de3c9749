package com.ecco.integration.core.tunnel;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import javax.websocket.DeploymentException;
import javax.websocket.Session;
import javax.websocket.WebSocketContainer;

import com.ecco.integration.core.EccoSettings;

/**
 * Instigate a web socket connection.
 *
 * @since 26/08/2016
 */
@Slf4j
public class WebSocketConnectionInstigator {
    private static final long TICK_MILLIS = 60000; // ms, so 60 seconds = 1 minute
    static final int ATTEMPTS_PER_TICK = 3;

    // around 30 client match results gives us roughly 16k (double the default), so 100 results (max) being 750k should be plenty
    // NB we could also configure via some settings.getInstance().getTunnel().getWebSocketMessageSize();
    // NB cloned in WebSocketConfig - so the server and client (this) match
    public static final int BUFFER_SIZE = (int) Math.round(0.75*1024*1024);

    static WebSocketConnectionInstigator instance;
    private final URI webSocketUrl;
    private final WebSocketContainer webSocketContainer;
    private Session currentSession;

    public WebSocketConnectionInstigator(EccoSettings settings, WebSocketContainer webSocketContainer) throws URISyntaxException {
        this.webSocketContainer = webSocketContainer;
        URI baseUrl = settings.getInstance().getBaseUrl();
        // If the baseUrl is configured as http[s], then use ws[s] instead.
        if (baseUrl.getScheme().startsWith("http")) {
            final String wsScheme = baseUrl.getScheme().replace("http", "ws");
            baseUrl = new URI(wsScheme, baseUrl.getUserInfo(), baseUrl.getHost(), baseUrl.getPort(),
                    baseUrl.getPath(), baseUrl.getQuery(), baseUrl.getFragment());
        }
        final URI webSocketPath = settings.getInstance().getTunnel().getWebSocketPath();
        this.webSocketUrl = baseUrl.resolve(webSocketPath);

        // NB cloned in WebSocketConfig - so the server and client (this) match
        webSocketContainer.setDefaultMaxSessionIdleTimeout(0L);
        // also see webSocketContainer.setAsyncSendTimeout();

        // we can see '408 Request Timeout: [no body]' in the ui external source tab on client matches
        // this will show on the agent as:
        //      2022-12-14 09:48:21.498 DEBUG 3384 --- [lient-AsyncIO-2] o.apache.tomcat.websocket.WsFrameClient  : WebSocket frame received. fin [true], rsv [0], OpCode [8], payload length [111]
        //      2022-12-14 09:48:21.499  INFO 3384 --- [lient-AsyncIO-2] c.e.i.c.tunnel.HttpTunnelSocketClient    : Connection closed. Attempting to auto-reconnect...
        //      2022-12-14 09:48:21.501  INFO 3384 --- [lient-AsyncIO-2] c.e.i.c.t.WebSocketConnectionInstigator  : Establishing web socket connection to: ws://92.15.235.36:8888/ecco-war/api/externalsystems/ql-tls/httpTunnel
        // this will show on the server (with trace) as:
        //      2022-12-13 16:30:22,089 DEBUG [org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator]                 - StandardWebSocketSession[id=1b6c0a10-f340-f8e8-226c-dd70f7614316, uri=ws://92.15.235.36/ecco-war/api/externalsystems/ql-tls/httpTunnel]
        //              closed with CloseStatus[code=1009, reason=The decoded text message was too big for the output buffer and the endpoint does not support partial messages]
        // where CloseStatus[code=1009 is due to exceeding the default message size of 8*1024 bytes (in WsWebSocketContainer impl for Constants.DEFAULT_BUFFER_SIZE)
        // and we need to setMessageSizeLimit and setSendBufferSizeLimit in the server and client
        // NB some sources have the buffer set as 'MAX_TEXT_MESSAGE_SIZE * 5';
        //      - https://docs.spring.io/spring-framework/docs/current/reference/html/web.html#websocket-server-runtime-configuration
        //      - also see:
        //              - https://docs.spring.io/spring-framework/docs/current/javadoc-api/org/springframework/web/socket/config/annotation/WebSocketTransportRegistration.html
        //              - https://stackoverflow.com/questions/34343235/stomp-spring-websocket-message-exceeds-size-limit
        //              - https://stackoverflow.com/questions/21730566/how-to-increase-output-buffer-for-spring-sockjs-websocket-server-implementation
        webSocketContainer.setDefaultMaxBinaryMessageBufferSize(BUFFER_SIZE);
        webSocketContainer.setDefaultMaxTextMessageBufferSize(BUFFER_SIZE);
    }

    @EventListener(ContextRefreshedEvent.class) // TODO: Ensure this only gets called for this app Contxt
    protected void connectAtStartup() {
        instance = this;
        establishConnection();
    }

    @Scheduled(fixedDelay = TICK_MILLIS, initialDelay = TICK_MILLIS)
    protected void checkSession() {
        synchronized (this) {
            if (currentSession == null || !currentSession.isOpen()) {
                establishConnection();
            }
            // we could have its own slower hearbeat but its going to be very minimal traffic
            // NB we send a valid json object, otherwise the server (jackson) complains which closes the connection
            // technically we should use a 'tunnel.client.idle-timeout=110000' (this one just shy of 2 minutes)
            // which can set the above webSocketContainer.setDefaultMaxSessionIdleTimeout(0L);

            // check again, because we don't want to see an error when establishConnection fails above
            if (currentSession != null && currentSession.isOpen()) {
                this.currentSession.getAsyncRemote().sendText("null");
            }
        }
    }

    public void reconnect() {
        synchronized (this) {
            if (currentSession != null && currentSession.isOpen()) {
                currentSession = null;
            }
            establishConnection();
        }
    }

    private void establishConnection() {
        log.info("Establishing web socket connection to: " + webSocketUrl);
        int i = 0;
        do {
            try {
                currentSession = webSocketContainer.connectToServer(HttpTunnelSocketClient.class, webSocketUrl);
                if (currentSession != null) {
                    log.info("Session created with maxTextMessageBufferSize (kb): " + currentSession.getMaxTextMessageBufferSize() / 1024);
                    log.info("Session created with maxBinaryMessageBufferSize (kb): " + currentSession.getMaxBinaryMessageBufferSize() / 1024);
                }
            } catch (DeploymentException | IOException e) {
                currentSession = null;
                if (i >= ATTEMPTS_PER_TICK - 1) {
                    log.warn("Failed to establish web socket connection. Retrying in (s) " + TICK_MILLIS / 1000, e);
                }
            }
        } while ((currentSession == null || !currentSession.isOpen()) && ++i < ATTEMPTS_PER_TICK);
    }
}
