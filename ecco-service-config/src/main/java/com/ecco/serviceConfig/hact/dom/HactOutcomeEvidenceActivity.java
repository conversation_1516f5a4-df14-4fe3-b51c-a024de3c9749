package com.ecco.serviceConfig.hact.dom;

import org.jspecify.annotations.NonNull;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;

/**
 * The evidence needed by the HACT outcomes
 */
@Entity
@Table(name = "hactoutcomeevidenceactivities")
public class HactOutcomeEvidenceActivity extends AbstractIntKeyedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * The HACT outcome code
     */
    @NonNull
    @Column(nullable = false)
    private String hactOutcomeDefCode;

    /**
     * The HACT description of how the question achieves the outcome
     */
    @Column
    private String evidenceDescription;

    /**
     * The id of the Action (can be more than one row to a hactOutcomeDefCode)
     */
    @Column
    private long actionDefId;

    // for hibernate
    private HactOutcomeEvidenceActivity() {
    }

    public String getHactOutcomeDefCode() {
        return hactOutcomeDefCode;
    }

    public void setHactOutcomeDefCode(String hactOutcomeDefCode) {
        this.hactOutcomeDefCode = hactOutcomeDefCode;
    }

    public String getEvidenceDescription() {
        return evidenceDescription;
    }

    public void setEvidenceDescription(String evidenceDescription) {
        this.evidenceDescription = evidenceDescription;
    }

    public long getActionDefId() {
        return actionDefId;
    }

    public void setActionDefId(long actionDefId) {
        this.actionDefId = actionDefId;
    }

}
