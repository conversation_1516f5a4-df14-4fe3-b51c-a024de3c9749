package com.ecco.serviceConfig.dom;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Data;

@Data
@Entity
@Table(name = "questionanswerfrees")
public class QuestionAnswerFree extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    String valueType; // the type of the value (string or integer)
    Integer minimum; // string length, or integer range
    Integer maximum;
}
