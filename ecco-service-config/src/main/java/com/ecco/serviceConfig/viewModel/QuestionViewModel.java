package com.ecco.serviceConfig.viewModel;

import com.ecco.serviceConfig.dom.QuestionAnswerChoiceView;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QuestionViewModel {

    public Integer id;

    public String name;

    public Boolean disabled;

    public String answerType;

    public Boolean answerRequired;

    /** Order within its questiongroup */
    public Integer orderby;

    public List<QuestionAnswerChoiceView> choices = new ArrayList<>();

    public List<QuestionAnswerFreeViewModel> freeTypes = new ArrayList<>();

    public Map<String, Object> parameters;
}
