package com.ecco.dom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProjectAclId implements IdName<Long> {

    /* Special project IDs used within ACLs service
     * @see com.ecco.service.EntityRestrictionServiceImpl */
    /** When set as a project ACL, this means the user has access to all projects including unassigned */
    public static final long ACCESS_ALL_PROJECTS = -1;
    /** When set as a project ACL this means the user has access to entities where the project is not yet assigned
     *  (i.e. the projectId is null) */
    public static final long ACCESS_NULL_PROJECTS = -2;

    public static final ProjectAclId accessAllProjectsFakeProject = new ProjectAclId(ACCESS_ALL_PROJECTS, "all projects");
    public static final ProjectAclId accessNullProjectsFakeProject = new ProjectAclId(ACCESS_NULL_PROJECTS, "no project assigned");

    static {
        // If you change the package, a database script to update ACLs will be needed.
        Assert.state(ProjectAclId.class.getName().equals("com.ecco" + ".dom.ProjectAclId")); // + avoids refactor tools changing this
    }

    public ProjectAclId(int id, String name) {
        this.id = (long) id;
        this.name = name;
    }

    public Long id;

    public String name;

}
