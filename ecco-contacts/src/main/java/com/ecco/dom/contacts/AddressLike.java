package com.ecco.dom.contacts;

import org.springframework.util.StringUtils;

public interface AddressLike {

    String getLine1();

    String getLine2();

    String getLine3();

    String getTown();

    String getCounty();

    String getPostCode();

    String getCountry();

    String toCommaSepString();

    static String formatPostcode(String orig) {
        if (orig == null) {
            return null;
        }
        String postcode = StringUtils.trimAllWhitespace(orig);
        int length = postcode.length();
        if (length > 7) {
            return orig; // assume it's a comment
        }
        postcode = postcode.toUpperCase();
        if (length < 5) { // must be at least S1 1AA (5 non-whitespace chars) for formatting
            return postcode; // e.g. CB11 could be CB1 1xx or CB11 nxx so just strip spaces
        }
        return postcode.substring(0, length - 3) + " " + postcode.substring(length - 3);
    }

    default boolean matches(AddressLike address) {
        return org.apache.commons.lang3.StringUtils.equalsIgnoreCase(getPostCode(), address.getPostCode()) // TODO: check if we have consistent formatting e.g. with formatPostcode above
            && org.apache.commons.lang3.StringUtils.equalsIgnoreCase(getLine1(), address.getLine1());
    }
}