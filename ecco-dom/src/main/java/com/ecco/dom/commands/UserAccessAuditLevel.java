package com.ecco.dom.commands;

import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;

public enum UserAccessAuditLevel {
    STATUS("status"), SHORT_HISTORY("short-history"), FULL_HISTORY("full-history");

    private final String value;

    UserAccessAuditLevel(String value) {
        this.value = value;
    }


    @Override
    public String toString() {
        return value;
    }

    public static UserAccessAuditLevel fromString(String value) {
        for (UserAccessAuditLevel candidate : values()) {
            if (StringUtils.equalsIgnoreCase(candidate.value, value)) {
                return candidate;
            }
        }
        return null;
    }

    public static class Converter implements AttributeConverter<UserAccessAuditLevel, String> {
        @Override
        public String convertToDatabaseColumn(UserAccessAuditLevel attribute) {
            return attribute.toString();
        }

        @Override
        public UserAccessAuditLevel convertToEntityAttribute(String dbData) {
            return UserAccessAuditLevel.fromString(dbData);
        }
    }
}
