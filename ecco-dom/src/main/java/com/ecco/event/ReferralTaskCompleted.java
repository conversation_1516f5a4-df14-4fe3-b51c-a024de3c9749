package com.ecco.event;

import com.ecco.dom.ClientDetail;
import com.ecco.dom.Referral;
import org.springframework.context.ApplicationEvent;

/**
 * @since 19/12/14
 */
public class ReferralTaskCompleted extends ApplicationEvent {
    private final Referral referral;

    // TODO: add data about the task that completed
    public ReferralTaskCompleted(Object source, Referral referral) {
        super(source);
        this.referral = referral;
    }

    public String getReferralCode() {
        return referral.getCode();
    }

    public ClientDetail getClient() {
        return referral.getClient();
    }

    public String getSupportWorkerCode() {
        return referral.getSupportWorker().getCode();
    }

    public String getSupportWorkerName() {
        return referral.getSupportWorker().getName();
    }
}