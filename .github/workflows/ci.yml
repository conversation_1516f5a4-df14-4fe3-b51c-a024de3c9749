name: CI
on:
  push:
    paths-ignore:
      - 'docs/**'
      - 'docker/**'
  workflow_dispatch:
    inputs:
      GRADLE_ARGS:
        description: 'Gradle args'
        required: false
        default: '-PskipITs=true -Ptest=x'
env:
  BROWSER: FIREFOX
  #BROWSER: CHROME
  #SELENIUM_VERSION_ARG: -Dselenium.version=4.0.0 # If want to override
  HOST_URL: http://localhost:8899
  TEST_RETRIES: 2
  # Use GRADLE_ARGS: --tests "{JavaClassName}" to run just a single test
  NX_BRANCH: ${{github.event.number}}
  NX_RUN_GROUP: ${{github.run_id}}
jobs:
  parameters:
    name: Determine parameters
    runs-on: ubuntu-latest
    outputs:
      # from step 'build-and-test'
      build-and-test-name: ${{steps.build-and-test.outputs.name}}
      test-report-name: ${{steps.build-and-test.outputs.test-report-name}}
      acceptance-test-profiles: ${{steps.build-and-test.outputs.acceptance-test-profiles}}
      # from step 'deploy'
      deploy: ${{steps.deploy.outputs.deploy}}
      deploy-path: ${{steps.deploy.outputs.path}} # NB branch name
      release-name: ${{steps.deploy.outputs.release-name}}
    steps:
      - name: Determine build and test parameters
        id: build-and-test
        # search ${skipITs}
        run: |
          ref="${{github.ref}}"
          echo "${{github.event.inputs.GRADLE_ARGS}}"
          if [[ "${ref}" = "refs/heads/ci-no-tests" ]]; then
            echo "name=No Tests" >> $GITHUB_OUTPUT
            echo "test-report-name=No Test Report" >> $GITHUB_OUTPUT
            echo "acceptance-test-profiles=-PskipITs" >> $GITHUB_OUTPUT
          elif [[ "${ref}" = "refs/heads/ci-int-tests" ]]; then
            echo "name=Integration Tests" >> $GITHUB_OUTPUT
            echo "test-report-name=Integration Test Report" >> $GITHUB_OUTPUT
            echo "acceptance-test-profiles=-Pskip.yarn" >> $GITHUB_OUTPUT
          else
            echo "name=API Tests" >> $GITHUB_OUTPUT
            echo "test-report-name=API Test Report" >> $GITHUB_OUTPUT
            echo "acceptance-test-profiles=-PFIXME -PskipITs" >> $GITHUB_OUTPUT
          fi
      - name: Determine deploy parameters
        id: deploy
        run: |
          ref="${{github.ref}}"
          date_now="$(date +'%Y%m%d')"
          echo "release-name=${ref#refs/heads/}-${date_now}-${{github.run_number}}" >> $GITHUB_OUTPUT
          if [[
            "${{github.event_name}}" != "push"
            && "${{github.event_name}}" != "workflow_dispatch"
          ]]; then
            echo "deploy=false" >> $GITHUB_OUTPUT
            echo "path=" >> $GITHUB_OUTPUT
          elif [[ "${ref}" = "refs/heads/main" ]]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "path=/" >> $GITHUB_OUTPUT
          elif [[
            "${ref}" = "refs/heads/test-branch"
            || "${ref}" = refs/heads/*-maint
            || "${ref}" = "refs/heads/nightly"
            || "${ref}" = "refs/heads/ci-no-tests"
          ]]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "path=/${ref#refs/heads/}" >> $GITHUB_OUTPUT
          else
            echo "deploy=false" >> $GITHUB_OUTPUT
            echo "path=" >> $GITHUB_OUTPUT
          fi
  log-build-parameters:
    name: Log build parameters
    runs-on: ubuntu-latest
    needs: parameters
    steps:
      - name: Log build parameters
        run: |
          echo "::stop-commands::yDvCQrxk"
          cat <<"EOF"
          ${{toJson(needs.parameters.outputs)}}
          EOF
          echo "::yDvCQrxk"
  build-and-test:
    name: ${{needs.parameters.outputs.build-and-test-name}}
    runs-on: ubuntu-latest
    needs: parameters
    timeout-minutes: 60
    permissions:
      contents: read
      issues: read
      checks: write
      pull-requests: write
    services:
      mysql:
        image: mysql:5.7
          # NOTE: mysql:5.7 is buried in the options line below, so image above is effectively ignored
        options: |
          --name mysql_container --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=5 -e MYSQL_ROOT_PASSWORD=password -e MYSQL_USER=ecco -e MYSQL_PASSWORD=ecco -e MYSQL_DATABASE=acctest --entrypoint sh mysql:5.7 -c "exec docker-entrypoint.sh mysqld --default-authentication-plugin=mysql_native_password --lower_case_table_names=1 --character-set-server=utf8 --collation-server=utf8_general_ci"
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: acctest
        ports:
          - 3306:3306
    steps:
      - name: docker ps
        run: docker ps
      - name: Show mysql version
        run: mysql --version
      - name: Verify MySQL connection port=${{ job.services.mysql.ports['3306'] }}
        run: mysql --protocol=tcp --host=localhost --port ${{ job.services.mysql.ports['3306'] }} -uroot -ppassword -e "SHOW DATABASES"
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 50
      # NB counting commits may be useful one day - less so here as we trigger on a commit. Usage: if: ${{ env.NEW_COMMIT_COUNT > 0 }}
      #- name: New commits
      #  run: echo "NEW_COMMIT_COUNT=$(git log --oneline --since '24 hours ago' | wc -l)" >> $GITHUB_ENV
      - name: Set up OpenJDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 17
          cache: 'gradle'
      - name: Find yarn cache
        id: find-yarn-cache
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
      - name: Cache yarn dependencies
        uses: actions/cache@v4
        timeout-minutes: 10
        with:
          path: ${{steps.find-yarn-cache.outputs.dir}}
          key: "${{runner.os}}-yarn-${{hashFiles('**/yarn.lock')}}"
          restore-keys: ${{runner.os}}-yarn-
      - name: Configure access token for GitHub Packages
        run: echo "//npm.pkg.github.com/:_authToken=${{secrets.PERSONAL_ACCESS_READ}}" > ~/.npmrc
      - name: Fetch main branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2023-12-31 origin main:main
      - name: Deepen this branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2023-12-31 origin ${{github.ref}}
      - name: build
        uses: burrunan/gradle-cache-action@v1
        with:
          arguments: |
            build
            ${{github.event.inputs.GRADLE_ARGS}}
      - name: unzip acctest.zip
        run: unzip ./setup/acctest.zip -d ./setup
      - name: load acctest.sql
        run: mysql --protocol=tcp --host=localhost --port ${{ job.services.mysql.ports['3306'] }} --user=ecco --password=ecco --database=acctest < ./setup/acctest.sql
      - name: acceptance tests
        timeout-minutes: 45
        run: |
          xvfb-run --server-args="-ac -screen 0 1920x1080x24" --auto-servernum \
            ./scripts/run-acctests-against-bootjar.sh ${{github.event.inputs.GRADLE_ARGS}}
      - name: Report test results
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2.18.0
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          check_name: ${{needs.parameters.outputs.test-report-name}}
          files: "**/build/test-results/test/TEST*.xml"
      - name: Upload reports
        uses: actions/upload-artifact@v4
        with:
          name: reports
          path: "**/build/reports/**"
          if-no-files-found: warn
          retention-days: 4
      - name: Upload ecco-webapi build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ecco-webapi
          path: ecco-webapi-boot/build/libs/*.jar
          if-no-files-found: error
          retention-days: 3 # failsafe, as this would still allow many releases to build up over 3 days
      - name: Upload ecco-int-oh build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ecco-int-oh
          path: ecco-int-oh/build/libs/*.jar
          if-no-files-found: error
          retention-days: 1
      - name: Upload Acceptance Tests logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco-acceptance-tests/logs/*
          if-no-files-found: ignore
      - name: Upload Ecco logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco/logs/ecco.log
          if-no-files-found: ignore
  release:
    name: Release
    runs-on: ubuntu-latest
    needs:
      - parameters
      - build-and-test
    # only release if we've determined we should push the code out
    if: success() && needs.parameters.outputs.deploy == 'true'
    #strategy:
    #    matrix: ${{fromJson(needs.parameters.outputs.deploy-matrix)}}
    steps:
      - name: Download ecco-api build artifact
        uses: actions/download-artifact@v4
        with:
          name: ecco-webapi
      #- name: Zip
      #  uses: thedoctor0/zip-release@0.7.1
      #  with:
      #    filename: 'releases.zip'
      - name: Display structure of downloaded files
        run: ls -R
      - name: Create Release ${{needs.parameters.outputs.release-name}}
        id: create-new-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
          tag_name: ${{needs.parameters.outputs.release-name}}
          release_name: Release ${{needs.parameters.outputs.release-name}}
      - name: Upload ecco-webapi to GitHub Release run-${{github.run_number}} # Upload ${{matrix.artifact}} to ...
        uses: actions/upload-release-asset@v1
        env:
            GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
            upload_url: ${{steps.create-new-release.outputs.upload_url}}
            asset_path: ecco-webapi-boot-1.0.0.CI-SNAPSHOT.jar
            asset_name: ${{needs.parameters.outputs.release-name}}.jar
            asset_content_type: application/java-archive

#  upload-logs:
#    name: Upload logs of failed build and test
#    runs-on: ubuntu-latest
#    needs:
#      - build-and-test
#    if: failure() && needs.parameters.outputs.deploy == 'true'
#    steps:
#      - name: Download build logs
#        uses: actions/download-artifact@v4
#        with:
#          name: logs
#      - name: Upload logs
#        uses: garygrossgarten/github-action-scp@v0.6.0
#        with:
#          local: .
#          remote: /home/<USER>/github-actions-failure/
#          host: 732939-www2.eccosolutions.co.uk
#          port: 7011
#          username: ${{secrets.DEPLOY_USER}}
#          #password: ${{secrets.DEPLOY_PASS}}
#          privateKey: ${{secrets.DEPLOY_PRIVKEY}}
