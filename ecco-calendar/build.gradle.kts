/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

dependencies {
    implementation(project(":ecco-buildings"))
    implementation(project(":ecco-calendar-core"))
    implementation(project(":ecco-config"))
    implementation(project(":ecco-dao"))
    implementation(project(":ecco-dom"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-service"))

    implementation("com.fasterxml.jackson.core:jackson-core")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-joda")
    api("org.springframework.cloud:spring-cloud-square-okhttp:0.4.0-M1")
    api("org.springframework.cloud:spring-cloud-square-retrofit:0.4.0-M1")
    api("org.springframework.security:spring-security-oauth2-client")
    implementation("org.springframework.hateoas:spring-hateoas")
    implementation("org.springframework:spring-web")
    implementation("org.springframework:spring-webmvc")
    api("com.squareup.okhttp3:okhttp:4.9.3")
    implementation("com.squareup.retrofit2:converter-jackson:2.1.0")

}

description = "ecco-calendar"
