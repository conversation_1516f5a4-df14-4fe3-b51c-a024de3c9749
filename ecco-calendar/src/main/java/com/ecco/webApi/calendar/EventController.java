package com.ecco.webApi.calendar;

import com.ecco.calendar.CombinedEntry;
import com.ecco.calendar.msgraph.MsEventToEventResource;
import com.ecco.calendar.msgraph.MsGraphCalendarService;
import com.ecco.calendar.msgraph.Response;
import com.ecco.calendar.msgraph.data.Event;
import com.ecco.dom.ContactImpl;
import com.ecco.dom.Individual;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.SecurityUtil;
import com.ecco.security.event.AppointmentNotificationEvent;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.EventService;
import com.ecco.calendar.core.webapi.EventResource;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.context.ApplicationEvent;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.ecco.infrastructure.web.RequestUtils.cacheForXSecs;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.joda.time.DateTimeZone.UTC;
import static org.springframework.format.annotation.DateTimeFormat.ISO.DATE;

@RestController
@WebSlice("api")
@RequestMapping("/calendar/event")
@CrossOrigin
public class EventController {

    private static final Function<ContactImpl, String> CONTACT_CALENDAR_ID_FN = (contact) -> contact == null ? null : contact.getCalendarId();

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private final EventService eventService;

    private final IndividualRepository individualRepository;

    private final MsGraphCalendarService msGraphCalendarService;

    private final SecurityUtilProvider securityUtilProvider;

    private final EventResourceAssembler eventResourceAssembler;

    private final MessageBus<ApplicationEvent> messageBus;

    // to allow for testing
    public interface SecurityUtilProvider {
        Long getAuthenticatedUserContactId();
        boolean isAuthenticatedOAuth2();
        String oAuth2Issuer();
        String getAuthenticatedUserCalendarId();
    }

    private static class DefaultSecurityUtilProvider implements SecurityUtilProvider {
        @Override
        public Long getAuthenticatedUserContactId() {
            return SecurityUtil.getAuthenticatedUser().getContact().getId();
        }

        @Override
        public boolean isAuthenticatedOAuth2() {
            return SecurityUtil.isAuthenticatedOAuth2();
        }

        @Override
        public String oAuth2Issuer() {
            return SecurityUtil.oAuth2Issuer();
        }

        @Override
        public String getAuthenticatedUserCalendarId() {
            return SecurityUtil.getAuthenticatedUserCalendarId();
        }
    }

    public EventController(
            EventService eventService,
            IndividualRepository individualRepository,
            MsGraphCalendarService msGraphCalendarService,
            SecurityUtilProvider securityUtilProvider,
            EventResourceAssembler eventResourceAssembler, MessageBus<ApplicationEvent> messageBus) {
        this.eventService = eventService;
        this.individualRepository = individualRepository;
        this.msGraphCalendarService = msGraphCalendarService;
        this.securityUtilProvider = securityUtilProvider != null ? securityUtilProvider : new DefaultSecurityUtilProvider();
        this.eventResourceAssembler = eventResourceAssembler;
        this.messageBus = messageBus;
    }

    // TODO: temp. Triggers an event. Delete.
    @GetJson("/notify")
    public ResponseEntity<Void> triggerNotificationEvent(@RequestParam @DateTimeFormat(iso = DATE) LocalDate start,
                                                         @RequestParam @DateTimeFormat(iso = DATE) LocalDate end,
                                                         @RequestParam("contactId") Long[] contactIdsArr) {
        // could use attributePaths={""} or projection to reinstate id, firstName, lastName, calendarId, email
        final List<Individual> users = individualRepository.findAllById(Arrays.asList(contactIdsArr));

        for (Individual user : users) {
            fireAppointmentNotificationEvent(user,
                    new Interval(start.toDateTimeAtStartOfDay(UTC), end.toDateTimeAtStartOfDay(UTC)));
        }
        return ResponseEntity.ok(null);
    }

    private void fireAppointmentNotificationEvent(Individual contact, Interval interval) {
        messageBus.publishBeforeTxEnd(new AppointmentNotificationEvent(this, contact, interval));
    }

    /**
     * NB Used from a 'calendar' links - should provide start (ISO date) or startTime (ISO dateTime)
     * @param start inclusive
     * @param end inclusive of the whole day - we go to the end of the day
     * @param startTime inclusive
     * @param endTime inclusive
     */
    @GetJson("/search")
    public List<EventResource> findCalendarEventsByContactIdsIso8601(
            @RequestParam(required = false) @DateTimeFormat(iso = DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DATE) LocalDate end,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS") LocalDateTime endTime,
            @RequestParam(value = "contactId", required = false) Long[] contactIdsArr,
            @RequestParam(value = "calendarId", required = false) List<String> calendarIds
    ) {

        // TODO: Get nailed for varying browser timezones and DST
        DateTime startInstant = start != null ? start.toDateTimeAtStartOfDay(UTC) : startTime.toDateTime(UTC);
        // end should be exclusive already... see comments below on findCalendarEventsByCalendarIds
        DateTime endInstant = end != null ? end.plusDays(1).toDateTimeAtStartOfDay(UTC).minusMillis(1) : endTime.toDateTime(UTC);
        return findEventsByTimePeriod(contactIdsArr, startInstant, endInstant, calendarIds);
    }
    // loads a calendar page
    // NB this the same as /search
    @GetJson("/list")
    public List<EventResource> findCalendarEventsByContactIds(@RequestParam("start") long startTimestamp, // FIXME: rename
                                                              @RequestParam("end") long endTimestamp,
                                                              @RequestParam("contactId") Long[] contactIdsArr,
                                                              @RequestParam(value = "calendarId", required = false) List<String> calendarIds) {
        DateTime start = new DateTime(startTimestamp*1000, UTC);
        DateTime end = new DateTime(endTimestamp*1000, UTC);
        return findEventsByTimePeriod(contactIdsArr, start, end, calendarIds);
    }

    // NB /nearby and /calendarIds do very similar job (and /search is for calendar code)
    @GetJson("/calendarIds")
    public List<EventResource> findCalendarEventsByCalendarIds(@RequestParam @DateTimeFormat(iso = DATE) LocalDate start,
                                                               @RequestParam @DateTimeFormat(iso = DATE) LocalDate end,
                                                               @RequestParam("calendarIds") List<String> calendarIds) {
        // end date should be exclusive - see CosmoCalendarRecurringService#findRecurrenceItems
        // and cosmo's StandardItemFilterProcessor - where start has to be after end
        var entries = eventService.getCalendars(calendarIds, start.toDateTimeAtStartOfDay(UTC), end.toDateTimeAtStartOfDay(UTC));
        return eventResourceAssembler.toResourceFromEntries(calendarIds, entries);
    }

    // NB /nearby and /calendarIds do very similar job (and /search is for calendar code)
    @GetJson("/nearby")
    public List<EventResource> findNearbyCalendarEvents(
            HttpServletResponse response,
            @RequestParam String calendarId,
            @RequestParam(required=false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) java.time.LocalDate date) {
        // EventServiceImpl returns the underlying Entry (calendar interface), with minimal decoration from ecco's Event
        // EventResourceAssembler.toModel creates a new the EventResource dto interface, clobbered with made up Event properties
        cacheForXSecs(15, response);
        return eventResourceAssembler.toResourceFromEntries(singletonList(calendarId), eventService.getNearbyCalendarEventsForContact(calendarId, date));
    }

    /**
     * Find events from either contactIdsArr or calendarIds. calendarIds was added for care-runs etc, see 6c433a66.
     * Includes the ability to load the users external calendars if logged in via 365.
     *
     * @param contactIdsArr List of contacts to get their EventResource from (optional with calendarIds)
     * @param calendarIds List of calendarIds to get their EventResource from (optional with contactIdsArr)
     * @return List of EventResource which combines both i (cosmo) and ecco properties to create calendar events
     */
    private List<EventResource> findEventsByTimePeriod(Long[] contactIdsArr, DateTime start, DateTime end, List<String> calendarIds) {
        // it seems the order of returned items from cosmo is correct as to the contactIds presented
        // except where there is a calendar with blank entries - which then appears first
        // thereby mis-matching the top calendar names with the entries
        // we set the order as the user has asked for - so the colour at the top of the calendar matches the colour in the calendars
        final Set<CombinedEntry> result = new LinkedHashSet<>();
        List<String> resultCalendarIds = new ArrayList<>();
        var containsUserCalendar = false;

        // get the calendarId from the contactIds
        // unfortunately, without spending some time knowing if CalendarEntries can be sorted by contactId we just loop them
        if (contactIdsArr != null) {
            for (Long contactId : contactIdsArr) {
                if (contactId.equals(securityUtilProvider.getAuthenticatedUserContactId())) {
                    containsUserCalendar = true;
                }
                final List<Individual> individuals = individualRepository.findAllById(List.of(contactId));
                List<String> contactCalendarIds = individuals.stream() // should be just one user
                        .map(CONTACT_CALENDAR_ID_FN)
                        .collect(toList());
                result.addAll(eventService.getCalendars(contactCalendarIds, start, end));
                resultCalendarIds.addAll(contactCalendarIds);
            }
        }

        if (calendarIds != null) {
            if (calendarIds.contains(securityUtilProvider.getAuthenticatedUserCalendarId())) {
                containsUserCalendar = true;
            }
            result.addAll(eventService.getCalendars(calendarIds, start, end));
            resultCalendarIds.addAll(calendarIds);
        }

        var resources = eventResourceAssembler.toResourceFromEntries(resultCalendarIds, result);

        if (msGraphCalendarService != null) {
            if (containsUserCalendar
                    && securityUtilProvider.isAuthenticatedOAuth2()
                    && securityUtilProvider.oAuth2Issuer().contains("login.microsoftonline.com")) {
                // the user is always the first calendar
                var conv = new MsEventToEventResource(securityUtilProvider.getAuthenticatedUserContactId(), Collections.singletonList("calendar1"));
                var items = getMsCalendar(start, end);
                resources.addAll(items.stream().map(conv).collect(toList()));
            }
        }

        return resources;
    }

    private List<Event> getMsCalendar(DateTime start, DateTime end) {
        retrofit2.Response<Response<List<Event>>> response = null;
        try {
            response = msGraphCalendarService
                    .calendarView(JodaToJDKAdapters.instantFromJoda(start.toInstant()), JodaToJDKAdapters.instantFromJoda(end.toInstant()))
                    .execute();
        } catch (IOException e) {
            // TODO do something sensible here.
            throw new RuntimeException(e);
        }
        if (response.isSuccessful()) {
            var payload = response.body();
            return payload != null ? payload.value : null;
        } else {
            // TODO do something sensible here.
            throw new RuntimeException(String.valueOf(response.errorBody()));
        }
    }

    // get the id - with hateos the whole URL is the id
    // but for the purposes of retrofitting, getting the id like this is acceptable
    // NB see https://github.com/spring-projects/spring-hateoas/issues/66
    // NB and http://tommyziegler.com/how-to-expose-the-resourceid-with-spring-data-rest/
    public static final Function<String, String> FIRST_FINDEVENTSID = (href) -> {
        MultiValueMap<String, String> queryParams =
                UriComponentsBuilder.fromUriString(href).build().getQueryParams();
        List<String> eventId = queryParams.get("eventIds");
        return eventId.get(0);
    };

    @GetJson("/ids/")
    public Stream<EventResource> findEventsByIds(
            @RequestParam(name = "eventIds") List<String> eventIds) {

        // NB: return ...
        // RotaAppointmentViewModel could be a return type
        // Entry could be a return type (see ItemController.findCalendarItems
        // (CalendarEntries is used as an intermediate object, not a return type)

        // there is no current method to get many items at once by id - see CosmoCalendarService
        return eventIds.stream().map(entryId ->
                eventResourceAssembler.toModel(eventService.getEntry(entryId)));
    }

}
