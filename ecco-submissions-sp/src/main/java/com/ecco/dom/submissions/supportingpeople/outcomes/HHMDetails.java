//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2011.08.27 at 07:14:34 PM BST
//


package com.ecco.dom.submissions.supportingpeople.outcomes;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * Houshold Member Details. This group is applied to each Household Member.
 *
 * <p>Java class for HHMDetails complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="HHMDetails">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Age" type="{https://supportingpeople.st-andrews.ac.uk}AgeRange"/>
 *         &lt;element name="Sex" type="{https://supportingpeople.st-andrews.ac.uk}Sex"/>
 *         &lt;element name="Relationship" type="{https://supportingpeople.st-andrews.ac.uk}Relationship"/>
 *         &lt;element name="EconomicStatus" type="{https://supportingpeople.st-andrews.ac.uk}EconomicStatus"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HHMDetails", propOrder = {
    "age",
    "sex",
    "relationship",
    "economicStatus"
})
public class HHMDetails
    implements Serializable
{

    private final static long serialVersionUID = 100L;
    @XmlElement(name = "Age")
    protected int age;
    @XmlElement(name = "Sex", required = true)
    protected String sex;
    @XmlElement(name = "Relationship", required = true)
    protected String relationship;
    @XmlElement(name = "EconomicStatus")
    protected int economicStatus;

    /**
     * Gets the value of the age property.
     *
     */
    public int getAge() {
        return age;
    }

    /**
     * Sets the value of the age property.
     *
     */
    public void setAge(int value) {
        this.age = value;
    }

    /**
     * Gets the value of the sex property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSex() {
        return sex;
    }

    /**
     * Sets the value of the sex property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSex(String value) {
        this.sex = value;
    }

    /**
     * Gets the value of the relationship property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRelationship() {
        return relationship;
    }

    /**
     * Sets the value of the relationship property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRelationship(String value) {
        this.relationship = value;
    }

    /**
     * Gets the value of the economicStatus property.
     *
     */
    public int getEconomicStatus() {
        return economicStatus;
    }

    /**
     * Sets the value of the economicStatus property.
     *
     */
    public void setEconomicStatus(int value) {
        this.economicStatus = value;
    }

}
