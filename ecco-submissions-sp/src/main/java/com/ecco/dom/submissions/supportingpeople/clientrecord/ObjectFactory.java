//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2011.08.27 at 06:07:52 PM BST
//


package com.ecco.dom.submissions.supportingpeople.clientrecord;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.ecco.dom.submissions.supportingpeople.clientrecord package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 *
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.ecco.dom.submissions.supportingpeople.clientrecord
     *
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ClientRecords }
     *
     */
    public ClientRecords createClientRecords() {
        return new ClientRecords();
    }

    /**
     * Create an instance of {@link ClientRecords.ClientRecord }
     *
     */
    public ClientRecords.ClientRecord createClientRecordsClientRecord() {
        return new ClientRecords.ClientRecord();
    }

    /**
     * Create an instance of {@link ClientRecords.ClientRecord.ClientDetails }
     *
     */
    public ClientRecords.ClientRecord.ClientDetails createClientRecordsClientRecordClientDetails() {
        return new ClientRecords.ClientRecord.ClientDetails();
    }

    /**
     * Create an instance of {@link ClientRecords.SubmissionDetails }
     *
     */
    public ClientRecords.SubmissionDetails createClientRecordsSubmissionDetails() {
        return new ClientRecords.SubmissionDetails();
    }

    /**
     * Create an instance of {@link HHMDetails }
     *
     */
    public HHMDetails createHHMDetails() {
        return new HHMDetails();
    }

    /**
     * Create an instance of {@link ClientRecords.ClientRecord.ProviderServiceDetails }
     *
     */
    public ClientRecords.ClientRecord.ProviderServiceDetails createClientRecordsClientRecordProviderServiceDetails() {
        return new ClientRecords.ClientRecord.ProviderServiceDetails();
    }

    /**
     * Create an instance of {@link ClientRecords.ClientRecord.ClientDetails.HouseholdMembers }
     *
     */
    public ClientRecords.ClientRecord.ClientDetails.HouseholdMembers createClientRecordsClientRecordClientDetailsHouseholdMembers() {
        return new ClientRecords.ClientRecord.ClientDetails.HouseholdMembers();
    }

}
