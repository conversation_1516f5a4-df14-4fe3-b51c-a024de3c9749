<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2011 rel. 2 sp1 (http://www.altova.com) by <PERSON><PERSON> (University of St Andrews) -->
<xs:schema xmlns="https://supportingpeople.st-andrews.ac.uk" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="https://supportingpeople.st-andrews.ac.uk" elementFormDefault="qualified" attributeFormDefault="qualified">
    <xs:simpleType name="ClientRecordID">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="10000"/>
            <xs:maxInclusive value="99999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NationalID">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="10000000"/>
            <xs:maxInclusive value="10009999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData4">
        <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
            <xs:minLength value="2"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData8">
        <xs:restriction base="xs:string">
            <xs:maxLength value="8"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData10">
        <xs:restriction base="xs:string">
            <xs:maxLength value="10"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData12">
        <xs:restriction base="xs:string">
            <xs:maxLength value="12"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData30">
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData50">
        <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData255">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear0910">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2009-04-01"/>
            <xs:maxInclusive value="2010-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear1011">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2010-04-01"/>
            <xs:maxInclusive value="2011-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear1112">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2011-04-01"/>
            <xs:maxInclusive value="2012-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoDK">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Homeless">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AgeRange">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="110"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Sex">
        <xs:restriction base="xs:string">
            <xs:enumeration value="_"/>
            <xs:enumeration value="M"/>
            <xs:enumeration value="F"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Relationship">
        <xs:restriction base="xs:string">
            <xs:enumeration value="_"/>
            <xs:enumeration value="P"/>
            <xs:enumeration value="C"/>
            <xs:enumeration value="X"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="EconomicStatus">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="-1"/>
            <xs:maxInclusive value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceType">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="EthnicOrigin">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ClientGroups">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="5"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="16"/>
            <xs:enumeration value="17"/>
            <xs:enumeration value="18"/>
            <xs:enumeration value="19"/>
            <xs:enumeration value="20"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReferralSource">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReferralType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Years">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="110"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Months">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Days">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AccomTypeCRF">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="16"/>
            <xs:enumeration value="17"/>
            <xs:enumeration value="18"/>
            <xs:enumeration value="19"/>
            <xs:enumeration value="20"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="26"/>
            <xs:enumeration value="27"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Email">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="(_{1}|([0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\w]*[0-9a-zA-Z]\.)+[a-zA-Z]{2,9}))"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NINumber">
        <xs:restriction base="xs:string">
            <xs:maxLength value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ONSCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="(0{1}|[0-9]{2}[A-Z]{2})"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PostcodePart1">
        <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
            <xs:pattern value="(_{1}|[A-Z]{1,2}[0-9R][0-9A-Z]?)"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PostcodePart2">
        <xs:restriction base="xs:string">
            <xs:maxLength value="3"/>
            <xs:pattern value="(_{1}|[0-9][A-Z]{2})"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoMissing">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Religion">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="10"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceTypeShort">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceTypeLong">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="5"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="13"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AccomType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="30"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1a">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1c">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2a_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2a_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2c">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2d_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2d_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_3_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_3_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="38"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4a">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4a_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="38"/>
            <xs:enumeration value="40"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4c_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4c_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_5">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoNAMissing">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HHMDetails">
        <xs:annotation>
            <xs:documentation>Houshold Member Details. This group is applied to each Household Member.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Age" type="AgeRange">
                <xs:annotation>
                    <xs:documentation>Age of the Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Sex" type="Sex">
                <xs:annotation>
                    <xs:documentation>Sex of the Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Relationship" type="Relationship">
                <xs:annotation>
                    <xs:documentation>Relationship of Household Member to the Client. Enter '_' for the Client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="EconomicStatus" type="EconomicStatus">
                <xs:annotation>
                    <xs:documentation>Economic Status of Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="ClientRecords">
        <xs:annotation>
            <xs:documentation>A collection of Client Record Forms.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="SubmissionDetails" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Information about the person who completed this Client Record Form.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="Forename" type="StringData50">
                                <xs:annotation>
                                    <xs:documentation>Forename of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Surname" type="StringData50">
                                <xs:annotation>
                                    <xs:documentation>Surname of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Telephone" type="StringData12">
                                <xs:annotation>
                                    <xs:documentation>Telephone No of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Email" type="Email">
                                <xs:annotation>
                                    <xs:documentation>Email address of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="DateCompleted" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Date that this Client Record Form was completed.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="ClientRecord" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>A single Client Record Form. Descriptions of the values used in custom datatypes can be found in the reference document located at http://www.spclientrecord.org.uk/xml/crf/docs/</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ProviderServiceDetails">
                                <xs:annotation>
                                    <xs:documentation>Information relating to the Supporting People Service Provider.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ClientRecordID" type="ClientRecordID">
                                            <xs:annotation>
                                                <xs:documentation>National Client Record Provider ID. Issued by the Client Record Office.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="NationalID" type="NationalID">
                                            <xs:annotation>
                                                <xs:documentation>National Provider ID. Issued by Communities and Local Government.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OrgName" type="StringData255">
                                            <xs:annotation>
                                                <xs:documentation>Name of  the Service Provider.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceName" type="StringData255">
                                            <xs:annotation>
                                                <xs:documentation>Name of the Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceID" type="StringData30">
                                            <xs:annotation>
                                                <xs:documentation>ID from the SP Contract for this Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="AdminAuthority" type="StringData4">
                                            <xs:annotation>
                                                <xs:documentation>Administering Authority area where the Service is being provided.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceType" type="ServiceType">
                                            <xs:annotation>
                                                <xs:documentation>Type of Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Partnership" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Does this Service work in Partnership with other Agencies.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipHealth" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Health Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipSocial" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Social Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipHousing" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Housing Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipDrug" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Drug or Alocohol Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipPolice" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Police or Probation Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipYOT" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Youth Offending Teams.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipEducation" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Education or Training Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipBenefits" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Benefit Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipDebt" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Debt Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipEmployment" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Employment Agencies or Job Centre.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipOther" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Partnership with Other Services.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ClientDetails">
                                <xs:annotation>
                                    <xs:documentation>Information for the Client and other Household Members.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ClientBudget" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Does the Client have an individual budget.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientBudgetUsed" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Did the Client use their individual budget to purchase this Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="RentDepositScheme" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Was the Clients accomodation secured through a rent deposit scheme.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SupportStart" type="ReportingYear1112">
                                            <xs:annotation>
                                                <xs:documentation>Start Date of Support.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientCode" type="StringData12">
                                            <xs:annotation>
                                                <xs:documentation>Unique reference for this Client.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="InterviewRefused" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Did the Client refuse Interview?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="HouseholdMembers">
                                            <xs:annotation>
                                                <xs:documentation>Household Members. Client and 5 others are allowed.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="Client" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Client i.e. The individual receiving support.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="Person2" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Second Houshold Member.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="Person3" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Third Houshold Member.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="Person4" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Fourth Houshold Member.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="Person5" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Fifth Houshold Member.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="Person6" type="HHMDetails">
                                                        <xs:annotation>
                                                            <xs:documentation>The Sixth Houshold Member.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="NINumber" type="NINumber">
                                            <xs:annotation>
                                                <xs:documentation>National Insurance Number of the Client.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="NIUnknown" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Does the Client know their NI Number.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="NIRefused" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Did the Client refuse to disclose their NI Number.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="NoNINo" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>The Client does not have their NI Number.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Disabled" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Does the Client have a disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityMobility" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Mobility related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityVisual" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Visual Impairment related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityHearing" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Hearing related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityChronic" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Progressive Disability or Illness.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityMental" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Mental Health related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityLearning" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Learning related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityAutistic" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Autistic related Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityOther" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Other Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityRefused" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Client does not wish to disclose Disability.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="EthnicOrigin" type="EthnicOrigin">
                                            <xs:annotation>
                                                <xs:documentation>Ethnic Origin of the Client.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="UserDefinedEthnic" type="StringData10">
                                            <xs:annotation>
                                                <xs:documentation>User Defined Ethinic Origin. For use when more specific classification is required.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Religion" type="Religion">
                                            <xs:annotation>
                                                <xs:documentation>Religion of the Client.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PrimaryClientGroup" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Primary Client Group by which the Client is defined.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup1" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>First Secondary Client Group by which the Client is defined.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup2" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Second Secondary Client Group by which the Client is defined.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup3" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Third Secondary Client Group by which the Client is defined.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="CareManagement" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Client requires services under the Care Management statutory framework.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="secondMentalHealth" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Client requires services under the Care Programme Approach statutory framework.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ProbationYouthOffending" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Client requires services under the Probation or Youth offending Team statutory framework.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DrugInterventions" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Client requires services under the Drug Interventions Programme statutory framework.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Homeless" type="Homeless">
                                            <xs:annotation>
                                                <xs:documentation>Prior to receiving support, what was the Client's Homeless status.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="CPA" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Has the Client been assessed as higher risk under Care Programme Approach.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="MAPPA" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Has the Client been assessed as higher risk under Multi Agency Public Protection Arrangements.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="MARAC" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Has the Client been assessed as higher risk under Multi Agency Risk Assessment Conference.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="HasASBO" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Is the Client subject to an ASBO.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ReferralSource" type="ReferralSource">
                                            <xs:annotation>
                                                <xs:documentation>Source of Referral</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ReferralType" type="ReferralType">
                                            <xs:annotation>
                                                <xs:documentation>Type of Referral</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="FloatStartAccom" type="AccomTypeCRF">
                                            <xs:annotation>
                                                <xs:documentation>Type of Accomodation occupied when starting to receiving the Floating Support Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousTenure" type="AccomTypeCRF">
                                            <xs:annotation>
                                                <xs:documentation>Type of Accomodation occupied immediately prior to receiving Service.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="CurrentTenure" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Does the Client continue to live in this Accomodation.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousLACode" type="ONSCode">
                                            <xs:annotation>
                                                <xs:documentation>ONS LA Code of the Accomodation.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousPostcode1" type="PostcodePart1">
                                            <xs:annotation>
                                                <xs:documentation>Postcode (Part 1) of the Accomodation.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousPostcode2" type="PostcodePart2">
                                            <xs:annotation>
                                                <xs:documentation>Postcode (Part 2) of the Accomodation.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="TempAccomodation" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Was the Accomodation temporary, or the Postcode unknown.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceAAYears" type="Years">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: How many years has the Client lived in the AA Area where the Service is being provided.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceAAMonths" type="Months">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: How many months has the Client lived in the AA Area where the Service is being provided.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceAADays" type="Days">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: How many days has the Client lived in the AA Area where the Service is being provided.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Under6MonthsLACode" type="ONSCode">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: If the Client has lived in the AA Area less than 6 months, what is the ONS LA Code of the previous area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Under6MonthsLAYears" type="Years">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: If the Client has lived in the AA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Under6MonthsLAMonths" type="Months">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: If the Client has lived in the AA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Under6MonthsLADays" type="Days">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: If the Client has lived in the AA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="HostDurationUnknown" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>HOST REFERRALS: If duration information is unknown, please indicate.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousLAYears" type="Years">
                                            <xs:annotation>
                                                <xs:documentation>NON-HOST REFERRALS: If the Client has lived in the LA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousLAMonths" type="Months">
                                            <xs:annotation>
                                                <xs:documentation>NON-HOST REFERRALS: If the Client has lived in the LA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PreviousLADays" type="Days">
                                            <xs:annotation>
                                                <xs:documentation>NON-HOST REFERRALS: If the Client has lived in the LA Area less than 6 months, how many years did they live in the previous AA Area.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="NonHostDurationUnknown" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>NON-HOST REFERRALS: If duration information is unknown, please indicate.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
