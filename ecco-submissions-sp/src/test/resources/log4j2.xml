<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="syncStdout" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n"/>
        </Console>
        <Async name="stdout" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncStdout"/>
        </Async>
    </appenders>

    <Loggers> <!-- NOTE: We could make all loggers async - see https://logging.apache.org/log4j/2.x/manual/async.html
or for an individual logger we could use asyncLogger or asyncRoot -->

        <logger name="org.springframework.ws.client.MessageTracing" level="TRACE"/>

        <Root level="INFO">
            <AppenderRef ref="stdout"/>
        </Root>
    </Loggers>
</configuration>
