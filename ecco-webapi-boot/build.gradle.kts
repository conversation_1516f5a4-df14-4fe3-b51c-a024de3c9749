import org.springframework.boot.gradle.plugin.SpringBootPlugin
import org.springframework.boot.gradle.tasks.run.BootRun

plugins {
    id("org.eccosolutions.owasp-check")
}

apply<SpringBootPlugin>()

tasks.named<BootRun>("bootRun") {
    systemProperty("user.timezone","UTC")
    systemProperty("env", "dev")

    /* REMOTE ACCESS */
        /* DISABLE */
        systemProperty("liquibase", "CREATE")
        systemProperty("db.schema", "acctest")
        systemProperty("db.extraContexts", "acceptanceTests")
        /* ENABLE */
        /*
         -- ssh -i <file-for-ip>.pem azureuser@<ip> -L 3307:<db>.mysql.database.azure.com:3306
         systemProperty("liquibase", "DISABLED")
         systemProperty("jdbc.url", "***************************************************************************************************************************************************************************************************")
         systemProperty("jdbc.username", "eccoadmin")
         systemProperty("jdbc.password", "...")
        */

    systemProperty("cookie.insecure", "true")
    systemProperty("cookie.samesite", "strict")
    systemProperty("azure.activedirectory.client-id", "asdf")
    systemProperty("azure.activedirectory.client-secret", "asdf")

}


dependencies {
    // TODO: Should be developmentOnly - see https://stackoverflow.com/a/76138565/1998186
    runtimeOnly("org.springframework.boot:spring-boot-devtools")

    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-offline"))
    implementation(project(":ecco-web"))
    implementation(project(":ecco-web-api"))

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation(project(":test-support"))

    testImplementation("com.azure.spring:azure-spring-boot-starter-active-directory")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

description = "ecco-webapi-boot"
