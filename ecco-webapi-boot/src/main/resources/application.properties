
# NOTE: SEE ALSO: application.yml which is used for the discoverable entries

# This might need moving to -D params for JVM
user.timezone=UTC
java.locale.providers=JRE,SPI

ecco.api.basePath=/api
ecco.mvc.basePath=/nav
ecco.mvc.resourcesPath=/r


# TODO: Define/expose some beans so we can auto-complete our props
env=dev
liquibase=CREATE

log4j.configuration=log4j-dev.xml
# logging.level.org.springframework.web.client.RestTemplate=DEBUG
#logging.level.org.springframework.transaction=TRACE
#logging.level.org.hibernate.SQL=TRACE
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

#logging.level.org.hibernate.event.def.DefaultFlushEntityEventListener=TRACE

# Additional logging to identify dirty fields in FixedContainer
#logging.level.org.hibernate.event.internal.DefaultFlushEntityEventListener=TRACE
#logging.level.org.hibernate.event.internal.AbstractFlushingEventListener=TRACE
#logging.level.org.hibernate.engine.internal.StatefulPersistenceContext=TRACE
#logging.level.org.hibernate.persister.entity.AbstractEntityPersister=TRACE
#logging.level.org.hibernate.type=TRACE
#logging.level.org.hibernate.engine.spi.EntityEntry=TRACE
#logging.level.org.hibernate.event.internal.DefaultDirtyCheckEventListener=TRACE

# Enable dirty checking logging specifically
#logging.level.org.hibernate.engine.internal.Versioning=TRACE
#logging.level.org.hibernate.engine.internal.EntityEntryContext=TRACE

# Log custom UserType operations
#logging.level.com.ecco.infrastructure.hibernate=TRACE
#logging.level.com.ecco.buildings.hibernate=TRACE
