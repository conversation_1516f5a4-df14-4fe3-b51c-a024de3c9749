package com.ecco.rota.service;

import com.ecco.service.exceptions.RotaException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class RotaDelegator {

    private List<RotaHandler> rotaHandlers;

    public RotaDelegator(List<RotaHandler> rotaHandlers) {
        this.rotaHandlers = rotaHandlers;
    }

    public RotaHandler selectHandler(String resourceFilter, String demandFilter) {
        for (RotaHandler handler : rotaHandlers) {
            if (handler.canHandle(resourceFilter, demandFilter)) {
                log.info("Handling " + resourceFilter +", " + demandFilter + " with "
                        + handler.getClass().getSimpleName());
                return handler;
            }
        }
        throw new RotaException("No rota handler found to handle: " + resourceFilter + ", "
                + demandFilter);
    }

}
