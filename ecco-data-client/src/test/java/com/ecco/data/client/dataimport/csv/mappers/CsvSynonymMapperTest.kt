package com.ecco.data.client.dataimport.csv.mappers

import kotlin.test.*

class CsvSynonymMapperTest {
    @Test
    fun illegalUntrimmedSynonym() {
        assertFailsWith<IllegalArgumentException> {
            CsvSynonymMapper(mapOf("  not trimmed  " to "NotTrimmed"))
        }
    }

    @Test
    fun illegalUppercaseLettersInSynonym() {
        assertFailsWith<IllegalArgumentException> {
            CsvSynonymMapper(mapOf("containsUppercase" to "ContainsUppercase"))
        }
    }

    @Test
    fun emptySynonyms() {
        val mapper = CsvSynonymMapper(emptyMap())
        val result = mapper(listOf("a", " b", "c ", " d ", "E"))
        assertEquals(listOf("a", "b", "c", "d", "E"), result)
    }

    @Test
    fun synonyms() {
        val mapper = CsvSynonymMapper(
            mapOf(
                "injection" to "distinct",
                "overall" to "Fire",
                "radical" to "  JUDGEMENT   ",
            ),
        )
        val result = mapper(
            listOf(
                "a",
                "b",
                "injection",
                " radical",
                "overall ",
                " c ",
                " injection ",
                "raDICal",
                " Overall ",
                "d overall",
            ),
        )
        assertEquals(
            listOf(
                "a",
                "b",
                "distinct",
                "  JUDGEMENT   ",
                "Fire",
                "c",
                "distinct",
                "  JUDGEMENT   ",
                "Fire",
                "d overall",
            ),
            result,
        )
    }
}