<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appenders>
        <RandomAccessFile name="syncFile" fileName="logs/data-client.log" append="false">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </RandomAccessFile>
        <Console name="syncStdout" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n" />
        </Console>

        <Async name="file" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncFile"/>
        </Async>
        <Async name="stdout" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncStdout"/>
        </Async>
    </appenders>
    <!--
     NOTE: This file is not detected automatically and requires log4j.configuration=log4j.xml to
     override the defaults of finding log4j2.xml
     DO NOT, however, rename this file as it's also in our full app
    -->

    <logger name="com.ecco.data.client" additivity="false" level="DEBUG">
        <appender-ref ref="stdout" />
    </logger>

    <root>
        <priority value="WARN" />
<!--         <appender-ref ref= "file" /> -->
        <appender-ref ref="stdout" />
    </root>
</configuration>
