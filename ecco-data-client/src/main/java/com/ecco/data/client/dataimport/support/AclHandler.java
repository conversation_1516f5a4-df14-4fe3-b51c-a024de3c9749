package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.WebApiSettings;
import com.ecco.webApi.acls.AclEntriesViewModel;
import com.ecco.webApi.acls.AclEntryViewModel;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

public class <PERSON>clH<PERSON>ler extends AbstractHandler<AclEntryViewModel> {

    protected static final String apiPath = "/api/";
    protected static boolean addAllProjects = true;
    protected static boolean setExactlyAsProvided = false;

    public AclHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /**
     * Ensures the acl information is consistent on entities which provie security (eg services/projects)
     */
    public void ensureAcls() {
        postForOK(WebApiSettings.APPLICATION_URL + apiPath + "acls/autoAcls/");
    }

    @Override
    protected void processEntity(ImportOperation<AclEntryViewModel> entity) {
    }

    public void processBatchedEntities(List<ImportOperation<AclEntryViewModel>> operations) {
        List<AclEntryViewModel> acls = operations.stream().map(operation -> operation.record).collect(Collectors.toList());
        AclEntriesViewModel vm = new AclEntriesViewModel();
        vm.setAcls(acls);
        if (addAllProjects) {
            addAllProjects(vm);
        }
        // either the incoming data is exactly what we want, or we simply add
        if (this.setExactlyAsProvided) {
            postAcceptingCreatedOrUnprocessableEntity(operations.get(0).baseUri + apiPath + "acls/entries/", vm);
        } else {
            postAcceptingCreatedOrUnprocessableEntity(operations.get(0).baseUri + apiPath + "acls/entries/add/", vm);
        }
        System.err.println(operations.get(0).record.getUsername());
    }

    /**
     * This allows us to specify an import file based on service only
     * and is useful because creating an import file by username order means the project -1
     * means inserting at specific points in the file, which is troublesome.
     */
    private void addAllProjects(AclEntriesViewModel vm) {
        Set<String> usernames = vm.getAcls().stream()
                .map(AclEntryViewModel::getUsername).collect(Collectors.toSet());
        Set<AclEntryViewModel> allProjects = usernames.stream()
                .map(username -> new AclEntryViewModel(username, -1L, "com.ecco.dom.Project", 1)).collect(Collectors.toSet());
        vm.getAcls().addAll(allProjects);
    }
}
