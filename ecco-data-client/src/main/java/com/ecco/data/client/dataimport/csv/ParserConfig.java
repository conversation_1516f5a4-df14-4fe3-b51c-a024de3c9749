package com.ecco.data.client.dataimport.csv;

/**
 * Bean which provides the parsing configuration (also used as target to read configuration into).
 * Line numbers start at 1 so that when viewing a CSV file as a spreadsheet, the numbers make sense.
 */
class ParserConfig {

    public String baseUrl;

    private int mappingRow = 1;

    private boolean spELOperationsEnabled = false;

    private boolean skipRowsEnabled = false;

    private String skipRowsColumnName = null;

    private int firstRow = 2;

    private int lastRow = Integer.MAX_VALUE;



    ParserConfig() {
    }

    public ParserConfig(int mappingRow, int firstRow, int lastRow) {
        super();
        this.mappingRow = mappingRow;
        this.firstRow = firstRow;
        this.lastRow = lastRow;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getMappingRow() {
        return mappingRow;
    }

    public void setMappingRow(int mappingRow) {
        this.mappingRow = mappingRow;
    }

    public int getFirstRow() {
        return firstRow;
    }

    public void setFirstRow(int firstRow) {
        this.firstRow = firstRow;
    }

    public int getLastRow() {
        return lastRow;
    }

    public void setLastRow(int lastRow) {
        this.lastRow = lastRow;
    }

    public boolean isSpELOperationsEnabled() {
        return spELOperationsEnabled;
    }

    public void setSpELOperationsEnabled(boolean hasOperationsRow) {
        this.spELOperationsEnabled = hasOperationsRow;
    }

    public boolean isSkipRowsEnabled() {
        return skipRowsEnabled;
    }

    public void setSkipRowsEnabled(boolean skipRowsEnabled) {
        this.skipRowsEnabled = skipRowsEnabled;
    }

    public String getSkipRowsColumnName() {
        return skipRowsColumnName;
    }

    public void setSkipRowsColumnName(String skipRowsColumnName) {
        this.skipRowsColumnName = skipRowsColumnName;
    }

}
