# This is ecco-web-api spring.factories for spring-boot.

# See SpringFactoriesLoader.loadFactories calls to find what is configured from this file

# ServletRootConfig was extracted from RootConfig to be shared between here and web.xml contextConfigLocation
# WebApiEndpointSecurityConfigurer here allows login/logout from ecco-webapi-boot (also see WebSecurityConfigurer)
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  com.ecco.infrastructure.config.root.ServletRootConfig,\
  com.ecco.webApi.config.WebApiEndpointSecurityConfigurer

# We did configure:
#   org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer = com.ecco.webApi.config.HttpSecurityConfig (which is now WebApiEndpointSecurityConfigurer)
# but wanted spring-boot defaults for many things, so moved specifically to ecco-war's spring.factories - but was later removed since not spring-boot (May 2021, 5ae31f09)
