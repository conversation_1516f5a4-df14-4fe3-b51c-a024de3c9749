package com.ecco.webApi.clients;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.commands.DeleteRequestServiceRecipientCommand;
import com.ecco.dom.commands.DeleteServiceRecipientCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.dom.servicerecipients.commands.ServiceRecipientCommandArchive;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.security.SecurityUtil;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.viewModels.DeleteRequestServiceRecipientCommandViewModel;
import com.ecco.webApi.viewModels.DeleteServiceRecipientCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import kotlin.Pair;
import org.hibernate.Session;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Table;
import javax.sql.DataSource;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Component
public class DeleteServiceRecipientCommandHandler extends ServiceRecipientCommandHandler<
        DeleteServiceRecipientCommandViewModel, DeleteServiceRecipientCommand, @NonNull DeleteServiceRecipientParams> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;
    @NonNull
    private final ServiceRecipientCommandRepository serviceRecipientCommandRepository;

    private final ConcurrentHashMap<String, Set<String>> columns = new ConcurrentHashMap<>();
    private NamedParameterJdbcTemplate jdbcTemplate;
    private String serviceRecipientCommandTableName;
    private String serviceRecipientCommandArchiveTableName;

    @Autowired
    public void setDataSource(DataSource dataSource) {
        this.jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
    }

    @Autowired
    public DeleteServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                                @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                @NonNull ServiceRecipientRepository serviceRecipientRepository) {
        super(objectMapper, serviceRecipientCommandRepository, DeleteServiceRecipientCommandViewModel.class);
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.serviceRecipientCommandRepository = serviceRecipientCommandRepository;
    }

    /**
     * Reverse the order by saving the command first, then handling the delete.
     * This allows the delete to include the delete command, and avoids errors referencing
     * a srId which no longer exists.
     */
    @NonNull
    @Override
    protected Pair<DeleteServiceRecipientCommand, Optional<CommandResult>> handleInternalAndSaveCommand(@NonNull Authentication authentication, @NonNull DeleteServiceRecipientParams params, @NonNull String requestBody, @NonNull DeleteServiceRecipientCommandViewModel viewModel, long userId) {
        final DeleteServiceRecipientCommand command = createCommand(null, params, requestBody, viewModel, userId);
        DeleteServiceRecipientCommand savedCommand = persistCommand(command);
        CommandResult link = handleInternal(authentication, params, viewModel);
        return new Pair<>(savedCommand, Optional.ofNullable(link));
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull DeleteServiceRecipientParams params,
                                           @NonNull DeleteServiceRecipientCommandViewModel viewModel) {

        SecurityUtil.assertAuthority("ROLE_DELETEREFERRAL");

        DeleteRequestServiceRecipientCommand deleteRequestCmd = (DeleteRequestServiceRecipientCommand) this.serviceRecipientCommandRepository.findOneByUuid(viewModel.requestDeletionUuid);
        Assert.notNull(deleteRequestCmd, "delete request not found");
        Assert.state(deleteRequestCmd.getServiceRecipientId() == viewModel.serviceRecipientId, "delete request is not for the same entity: " + deleteRequestCmd.getServiceRecipientId());

        DeleteRequestServiceRecipientCommandViewModel deleteRequestVm = getDeleteRequestViewModel(deleteRequestCmd);
        if (deleteRequestVm.revoke) {
            throw new IllegalArgumentException("Cannot delete using a delete-request marked as revoke: " + deleteRequestCmd.getUuid());
        }

        // ensure the latest command entered is not a revoke
        List<DeleteRequestServiceRecipientCommand> cmdVms = serviceRecipientCommandRepository.findDeleteRequestsByServiceRecipientId(viewModel.serviceRecipientId);
        DeleteRequestServiceRecipientCommand latestDeleteRequestCmd = cmdVms.stream()
                .reduce((prev, curr) -> prev.getCreated().isAfter(curr.getCreated()) ? prev : curr).get();
        DeleteRequestServiceRecipientCommandViewModel latestDeleteRequestVm = getDeleteRequestViewModel(latestDeleteRequestCmd);
        if (latestDeleteRequestVm.revoke) {
            throw new IllegalArgumentException("latest delete request is marked as revoke: " + latestDeleteRequestCmd.getUuid());
        }

        BaseServiceRecipientEvidence sr = this.serviceRecipientRepository.findEvidenceCapableById(params.serviceRecipientId).orElseThrow();
        moveCommandsToArchive(params.serviceRecipientId);
        Integer siblingsCount = sr.getTargetEntity().countSiblings();
        this.serviceRecipientRepository.delete(sr);
        if (viewModel.deleteParentIfPossible && (siblingsCount != null && siblingsCount == 1)) {
            entityManager.remove(sr.getTargetEntity().getGrandParent());
        }
        return null;
    }

    private DeleteRequestServiceRecipientCommandViewModel getDeleteRequestViewModel(DeleteRequestServiceRecipientCommand deleteRequest) {
        DeleteRequestServiceRecipientCommandViewModel deleteRequestVm;
        try {
            deleteRequestVm = objectMapper.readValue(deleteRequest.getBody(), DeleteRequestServiceRecipientCommandViewModel.class);
            Assert.state(deleteRequestVm.valid(), "Nonnull requirements not met");
        } catch (IOException e) {
            throw new RuntimeException("Cannot convert to view model on delete-request: " + deleteRequest.getUuid(), e);
        }
        return deleteRequestVm;
    }

    @NonNull
    @Override
    protected DeleteServiceRecipientCommand createCommand(Serializable targetId, @NonNull DeleteServiceRecipientParams params,
                                                          @NonNull String requestBody,
                                                          @NonNull DeleteServiceRecipientCommandViewModel viewModel,
                                                          long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        return new DeleteServiceRecipientCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId);
    }

    /**
     * This approach uses jdbc to perform an 'insert into _table_ (columns) select ..'
     */
    private void moveCommandsToArchive(int serviceRecipientId) {

        // ensure the delete command is inserted early, else at tx end it can't find the serviceRecipient
        // also ensure all the commands are there before moving to archive
        entityManager.flush();

        // get the columns (once) since this is needed for a cross-platform insert statement
        primeTableAndColumnsOfServiceRecipientCommandTables();

        // execute the insert into select
        jdbcMoveCommands(serviceRecipientId);
    }

    /**
     * Do an insert using select. This should have no performance problems and is cross platform
     *  - see http://stackoverflow.com/a/25971
     * If we needed batching...
     * see http://frightanic.com/software-development/jpa-batch-inserts/
     * and we can ignore hibernate.order_inserts && hibernate.order_updates because we're just insert into one table
     * we could set hibernate.jdbc.batch_size but can also jua do manually
     */
    private void jdbcMoveCommands(int serviceRecipientId) {
        Set<String> columns = this.columns.get(this.serviceRecipientCommandTableName);
        String fieldsSql = columns.stream().reduce((cumCols, col) -> cumCols.concat(
                cumCols.length() > 0
                        ? ", " + col
                        : col))
                .get();
        String idName = "serviceRecipientId";
        String sql = "insert into " + this.serviceRecipientCommandArchiveTableName + " (" + fieldsSql + ") select "
                + fieldsSql + " from " + this.serviceRecipientCommandTableName +
                " where " + idName + "=:srId";
        Map<String, Object> namedParameters = new HashMap<>();
        namedParameters.put("srId", serviceRecipientId);
        this.jdbcTemplate.update(sql, namedParameters);
    }

    /**
     * Get the columns of the table.
     * This approach uses Hibernate SessionFactory since JPA does not provide a robust approach to getting columns names.
     * Column names are used as a cross database approach for native insert statements which avoid creating and maintaining
     * duplicate class hierarchies, just for the table name to change to the archive version. See ECCO-2262, and the
     * alternative pro/cons mentioned at the end.
     */
    private void primeTableAndColumnsOfServiceRecipientCommandTables() {
        if (this.columns.size() > 0) {
            return;
        }

        this.serviceRecipientCommandTableName = getCommandTableName(ServiceRecipientCommand.class);
        this.serviceRecipientCommandArchiveTableName = getCommandTableName(ServiceRecipientCommandArchive.class);

        String tableName = this.serviceRecipientCommandTableName;
        Session s = (Session)entityManager;

        // use the last entry to get the columns - there will be a last entry since we just flushed one
        Number maxId = (Number) s.createSQLQuery("select max(id) from " + tableName).uniqueResult();
        Map<String,Object> result = (Map<String,Object>) s.createSQLQuery(
                "select * from " + tableName + " where id = :id")
                .setParameter("id", maxId)
                .setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE)
                .uniqueResult();

        Set<String> columns = result.keySet();
        this.columns.put(tableName, columns);
    }

    private String getCommandTableName(Class<?> clazz) {
        Table table = clazz.getAnnotation(Table.class);
        return table.name();
    }
    /*
    // get columns from annotations
    private String getColumnsFromAnnotatedClass(Class<?> clazz) {
        ClassMetadata classMetadata = this.sessionFactory.getClassMetadata(ServiceRecipientTaskCommand.class);
        String[] propertyNames = classMetadata.getPropertyNames();
        System.out.print(propertyNames);

        for (String property : propertyNames) {
            Configuration configuration = sessionFactoryBean.getConfiguration();
            PersistentClass persistentClass = configuration
                    .getClassMapping(Details.class.getName());
            String columnName = ((Column) persistentClass.getProperty(property)
                    .getColumnIterator().next()).getName();
        }
     }
     */
}
