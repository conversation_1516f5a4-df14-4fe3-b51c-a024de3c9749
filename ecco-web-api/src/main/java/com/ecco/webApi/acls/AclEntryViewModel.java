package com.ecco.webApi.acls;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.util.Assert;

@Data
@NoArgsConstructor
public class AclEntryViewModel {

    private String username;
    //public String userDisplayName;
    private Long secureObjectId;
    @NonNull
    private String clazz;
    private Integer permissionMask;
    // isGranting - only really useful to specify false when using inheritance

    public AclEntryViewModel(String username, long secureObjectId, String clazz, int permissionMask) {
        this.username = username;
        this.secureObjectId = secureObjectId;
        this.clazz = clazz;
        this.permissionMask = permissionMask;
//        Assert.hasText(this.username);  We allow username to be null in UserActor
        Assert.hasText(this.clazz);
        Assert.isTrue(permissionMask >= 1);
    }
}
