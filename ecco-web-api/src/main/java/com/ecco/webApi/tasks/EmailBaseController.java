package com.ecco.webApi.tasks;

import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.UriUtils;
import com.ecco.messaging.EmailService;
import com.ecco.security.repositories.UserRepository;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.TaskStatusViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;


public abstract class EmailBaseController extends BaseWebApiController {

    protected final EmailService emailService;
    protected final ServiceTypeService serviceTypeService;
    protected final TaskDefinitionRepository taskDefinitionRepository;
    protected final ServiceRecipientRepository serviceRecipientRepository;
    protected final ServiceCategorisationRepository serviceCategorisationRepository;
    protected final UserRepository userRepository;
    protected final MessageSourceAccessor messageSource;
    protected final ApplicationProperties appConfig;

    public EmailBaseController(@NonNull EmailService emailService,
                               @NonNull ServiceTypeService serviceTypeService,
                               @NonNull TaskDefinitionRepository taskDefinitionRepository,
                               @NonNull ServiceCategorisationRepository serviceCategorisationRepository,
                               @NonNull ServiceRecipientRepository serviceRecipientRepository,
                               @NonNull UserRepository userRepository,
                               @NonNull MessageSource messageSource,
                               @NonNull ApplicationProperties appConfig) {
        super();
        this.emailService = emailService;
        this.serviceTypeService = serviceTypeService;
        this.taskDefinitionRepository = taskDefinitionRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.userRepository = userRepository;
        this.messageSource = new MessageSourceAccessor(messageSource);
        this.appConfig = appConfig;
    }

    protected void sendEmail(List<TaskStatusViewModel> tasks, String email, String subject) {

        // nothing to send
        if (tasks.isEmpty() || !StringUtils.hasLength(email)) {
            // ? email service for unassigned tasks
            //Service service = serviceRepository.findById(serviceId).orElseThrow(NullPointerException::new);
            //String emailAddrs = service.getParameterAsString(Service.PARAM_EMAIL);
            //Assert.hasText(emailAddrs, "You must configure an email address (email.notification) for this service");
            return;
        }

        String bodyHtml = generateEmailTemplate(tasks);

        // SYNC is probably okay, its meant to be a quiet time
        emailService.sendMessage(email, subject, bodyHtml);
    }

    @NonNull
    protected abstract String getSubject(List<TaskStatusViewModel> tasks);

    protected LocalDateTime getNow() {
        return LocalDate.now(ZoneId.of("UTC")).atStartOfDay();
    }

    private String generateEmailTemplate(List<TaskStatusViewModel> tasks) {
        StringBuilder stringBuilder = generateHead();
        var now = getNow();

        // "<th>id</th><th>due date</th><th>service</th><th>task</th><th>assigned</th>"
        tasks.forEach(task -> {
            var svcCat = this.serviceCategorisationRepository.findById(task.serviceAllocationId).orElseThrow();
            String serviceName = svcCat.description();

            stringBuilder.append("<tr>");

            // link to file
            stringBuilder.append("<td>")
                    .append("<a target=\"_blank\" href=\"")
                    .append(linkToParentFile(task.serviceRecipientId))
                    .append("\">")
                    .append(task.serviceRecipientParentId)
                    .append("</a>")
                    .append("</td>");

            // due date
            if (task.dueDate != null) {
                var dueDate = task.dueDate.format(DateTimeFormatter.ISO_DATE);
                if (task.dueDate.isBefore(now)) {
                    stringBuilder.append("<td style=\"color: red\">").append(dueDate).append("</td>");
                } else {
                    stringBuilder.append("<td>").append(dueDate).append("</td>");
                }
            } else {
                stringBuilder.append("<td>&nbsp;</td>");
            }

            // service name
            stringBuilder.append("<td>").append(serviceName).append("</td>");

            // task name
            stringBuilder.append("<td>").append(getTaskName(svcCat, task)).append("</td>");

            // assignee
            stringBuilder.append("<td>").append(getAssigneeName(task)).append("</td>");

            stringBuilder.append("</tr>");
        });

        generateFooter(stringBuilder);

        return stringBuilder.toString();
    }

    private String getAssigneeName(TaskStatusViewModel task) {
        return this.userRepository.findById(task.assignee).orElseThrow().getContact().getDisplayName();
    }

    private String getTaskName(ServiceCategorisation svcCat, TaskStatusViewModel task) {

        // find the task def
        if (task.taskDefinitionId != null) {
            var serviceTypeId = svcCat.loadServiceTypeId(); // TODO should be svcRec.loadConfigServiceTypeId
            var serviceType = serviceTypeService.findOneDto(Long.valueOf(serviceTypeId).intValue());
            var taskDef = serviceType.taskDefinitionEntries.stream()
                    .filter(td -> td.taskDefId == task.taskDefinitionId).findFirst();
            return taskDef.isPresent() ? getTaskDefinitionDisplayName(taskDef.get()) : "unknown";

        } else {
            return "ad hoc";
        }
    }

    // clone of service-config-domain.ts getDisplayName()
    private String getTaskDefinitionDisplayName(TaskDefinitionEntryViewModel serviceTypeTaskDef) {
        if (serviceTypeTaskDef.settings.get("titleRaw") != null) {
            return serviceTypeTaskDef.settings.get("titleRaw");
        }

        // check from existing task list
        var messageKey = "referralView." + serviceTypeTaskDef.name;
        try {
            return messageSource.getMessage(messageKey);
        } catch (NoSuchMessageException ignored) {
        }

        // check from breadcrumb in the wizard
        messageKey = "referralBreadcrumb." + serviceTypeTaskDef.name;
        try {
            return messageSource.getMessage(messageKey);
        } catch (NoSuchMessageException ignored) {
        }

        // we would return friendlyName here, but we don't need another thing to deal with
        var taskDef = taskDefinitionRepository.findOneById(serviceTypeTaskDef.taskDefId);
        // NB ideal would be to match ts camel case - as per StringUtils.camelToSpaces(this.getName());
        return taskDef != null ? taskDef.getName() : "";
    }

    private LinkBuilder linkToParentFile(Integer srId) {
        // if we move this controller to ecco-war so we can linkTo the sr file, we lose access to EmailService
        // - although that could possibly be moved to ecco-inftastructure
        try {
            // 'nav/sr/{serviceRecipientId}' is from OnlinePageController which goes to the right file for us
            // see SRRedirectController
            // NB context not appended to eccoWebsiteUrl because the website is assumed to point to the context already
            //var contextNoSlash = UriUtils.stripSlahes(this.appConfig.getApplicationRootPath());
            final UriComponentsBuilder subdomain = UriComponentsBuilder.fromHttpUrl(this.appConfig.getEccoWebsiteUrl())
                    .pathSegment("nav").pathSegment("r")
                    .pathSegment("main").pathSegment("sr2")
                    .pathSegment(srId.toString());
            return new UriUtils.ExplicitLinkBuilder(subdomain.build());
        } catch (IllegalArgumentException e) {
            return new UriUtils.ExplicitLinkBuilder(UriComponentsBuilder.fromPath("-please-set-ecco.websiteUrl-").build());
        }
    }

    private StringBuilder generateHead() {
        StringBuilder stringBuilder = new StringBuilder();

        return stringBuilder.append("<head>")
                .append("</head>")
                .append("<body>")
                .append("<table style=\"border-collapse: collapse;\" cellspacing=\"10\" cellpadding=\"10\">")
                .append("<tr>")
                .append("<th>id</th><th>due date</th><th>service</th><th>task</th><th>assigned</th>")
                .append("</tr>");
    }

    private void generateFooter(StringBuilder stringBuilder) {
        stringBuilder.append("</table></body>");
    }
}
