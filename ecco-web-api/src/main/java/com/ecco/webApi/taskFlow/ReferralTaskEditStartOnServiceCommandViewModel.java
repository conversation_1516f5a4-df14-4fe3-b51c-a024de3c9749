package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.Nullable;

import org.joda.time.LocalDate;

import com.ecco.dto.ChangeViewModel;

public class ReferralTaskEditStartOnServiceCommandViewModel extends ReferralTaskEditAllocateWorkerCommandViewModel {

    public static String TASK_NAME = "start"; // NB is 'startOnService'

    /** start date */
    @Nullable
    public ChangeViewModel<LocalDate> receivingServiceDate;


    ReferralTaskEditStartOnServiceCommandViewModel() {
        super();
    }

    public ReferralTaskEditStartOnServiceCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
    }
}