package com.ecco.webApi.taskFlow;

import com.ecco.dto.ChangeViewModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.joda.time.LocalDate;

import org.jspecify.annotations.Nullable;


@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
public class ReferralTaskClientResidenceCommandDto extends ServiceRecipientTaskCommandViewModel {

    static String TASK_CLIENT_RESIDENCE = "accommodation";

    @Nullable
    public ChangeViewModel<Integer> residence;

    @Nullable
    public ChangeViewModel<LocalDate> startDate;

    @Nullable
    public ChangeViewModel<LocalDate> endDate;


    public ReferralTaskClientResidenceCommandDto(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_CLIENT_RESIDENCE, taskHandle);
    }
}
