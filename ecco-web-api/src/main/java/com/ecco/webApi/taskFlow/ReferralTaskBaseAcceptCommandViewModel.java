package com.ecco.webApi.taskFlow;

import com.ecco.servicerecipient.AcceptState;
import com.ecco.dto.ChangeViewModel;
import org.joda.time.LocalDate;

import org.jspecify.annotations.Nullable;


public abstract class ReferralTaskBaseAcceptCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    @Nullable
    public ChangeViewModel<LocalDate> acceptedDate;

    @Nullable
    public ChangeViewModel<AcceptState> acceptedState;

    @Nullable
    public ChangeViewModel<Long> signpostedAgency;

    @Nullable
    public ChangeViewModel<Boolean> signpostedBack;

    @Nullable
    public ChangeViewModel<String> signpostedComment;

    @Nullable
    public ChangeViewModel<Integer> signpostedReason;

    @Deprecated
    public ReferralTaskBaseAcceptCommandViewModel() {}

    public ReferralTaskBaseAcceptCommandViewModel(int serviceRecipientId, String taskName, String taskHandle) {
        super(serviceRecipientId, taskName, taskHandle);
    }

}
