package com.ecco.webApi.taskFlow;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.hr.Worker;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class StaffTaskPrimaryLocationCommandHandler
        extends ServiceRecipientTaskCommandHandler<StaffTaskPrimaryLocationCommandDto> {

    @NonNull
    private final WorkerRepository workerRepository;
    @NonNull
    private final WorkerJobRepository workerJobRepository;

    @Autowired
    public StaffTaskPrimaryLocationCommandHandler(ObjectMapper objectMapper,
                                                  @NonNull WorkflowTaskController workflowTaskController,
                                                  @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                  @NonNull WorkerRepository workerRepository,
                                                  @NonNull WorkerJobRepository workerJobRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, StaffTaskPrimaryLocationCommandDto.class);

        this.workerRepository = workerRepository;
        this.workerJobRepository = workerJobRepository;
    }


    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               StaffTaskPrimaryLocationCommandDto vm) {

        Worker c = workerJobRepository.findByServiceRecipient_Id(params.serviceRecipientId).get().getWorker();

        if (vm.startDate != null) {
            // ignore for now
        }

        if (vm.endDate != null) {
            // ignore for now
        }

        if (vm.location != null) {
            Integer to = vm.location.to;
            c.setPrimaryLocationId(to);
        }

        return null;
    }
}
