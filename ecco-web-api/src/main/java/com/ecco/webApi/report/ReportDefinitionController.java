package com.ecco.webApi.report;

import com.ecco.dao.commands.ReportDefinitionCommandRepository;
import com.ecco.dao.commands.ReportDefinitionRepository;
import com.ecco.dom.ReportDefinition;
import com.ecco.dom.commands.ReportDefinitionCommand;
import com.ecco.dom.commands.ReportDefinitionUpdateCommand;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.ecco.security.SecurityUtil.getUser;

/**
 * web api for everything related to report definitions and commands
 */
@PreAuthorize("hasAnyRole('ROLE_REPORTS')")
@RestController
public class ReportDefinitionController extends BaseWebApiController {

    @NonNull
    private final ReportDefinitionRepository reportDefinitionRepository;

    @NonNull
    private final ReportDefinitionCommandRepository reportDefinitionCommandRepository;

    @NonNull
    private final ReportDefinitionToViewModel toViewModel = new ReportDefinitionToViewModel();

    @NonNull
    private final ObjectMapper objectMapper;


    @Autowired
    public ReportDefinitionController(
            @NonNull ReportDefinitionRepository reportDefinitionRepository,
            @NonNull ReportDefinitionCommandRepository reportDefinitionCommandRepository,
            @NonNull ObjectMapper objectMapper) {
        this.reportDefinitionRepository = reportDefinitionRepository;
        this.reportDefinitionCommandRepository = reportDefinitionCommandRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * Retrieve the list of report definitions
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/reportDef/")
    public List<ReportDefinitionViewModel> findReports() {
        Iterable<ReportDefinition> reportDefinitions = reportDefinitionRepository.findAll();
        return StreamSupport.stream(reportDefinitions.spliterator(), false).map(toViewModel).collect(Collectors.toList());
    }

    /**
     * Retrieve a single report definition
     */
    // TODO required for 'activity' tab, but would be better existing in separate api entry point
    @PreAuthorize("hasAnyRole('ROLE_STAFF', 'ROLE_REPORTS')")
    @GetJson("/reportDef/{reportDefUuid}")
    public ReportDefinitionViewModel findReport(
            @NonNull @PathVariable UUID reportDefUuid) {
        ReportDefinition reportDefinition = reportDefinitionRepository.findById(reportDefUuid).orElseThrow(NullPointerException::new);
        return toViewModel.apply(reportDefinition);
    }


    @PostJsonReturningJson("/reportDef/command/")
    public Result updateReport(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        ReportDefinitionUpdateCommandViewModel updateVm = objectMapper.readValue(requestBody, ReportDefinitionUpdateCommandViewModel.class);

        ReportDefinitionCommand duplicate = reportDefinitionCommandRepository.findOneByUuid(updateVm.uuid);
        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }

        long userId = getUser(authentication).getId();

        updateOrCreateReportDefinition(updateVm.reportDefUuid, userId, updateVm);

        ReportDefinitionUpdateCommand saved = reportDefinitionCommandRepository.save(
                new ReportDefinitionUpdateCommand(
                        updateVm.uuid,
                        updateVm.timestamp,
                        userId,
                        requestBody,
                        updateVm.reportDefUuid));

        return new Result(Result.COMMAND_APPLIED, saved.getId());
    }

    private void updateOrCreateReportDefinition(UUID reportDefUuid, long userId, ReportDefinitionUpdateCommandViewModel viewModel) {
        ReportDefinition repDef = reportDefinitionRepository.findById(reportDefUuid).orElse(null);
        if (repDef == null) {
            Assert.notNull(viewModel.name, "Name change must be supplied on new report definition");
            Assert.notNull(viewModel.orderby, "Order by must be supplied on new report definition");
            Assert.notNull(viewModel.definition, "Definition change must be supplied on new report definition");
            repDef = new ReportDefinition(reportDefUuid, viewModel.name.to,
                    viewModel.friendlyName == null ? null : viewModel.friendlyName.to,
                    viewModel.orderby.to, userId, viewModel.definition.to);
            repDef.setCreated(DateTime.now());
        }

        // Hide if flagged as delete
        if (viewModel.deleted != null) {
            if (viewModel.deleted.to) {
                repDef.hide();
            }
            else { // i.e. deleted.to == false
                repDef.unHide();
            }
        }

        if (viewModel.name != null) {
            repDef.setName(viewModel.name.to);
        }

        if (viewModel.showOnDashboardManager != null) {
            repDef.setShowOnDashboardManager(viewModel.showOnDashboardManager.to);
        }

        if (viewModel.showOnDashboardFile != null) {
            repDef.setShowOnDashboardFile(viewModel.showOnDashboardFile.to);
        }

        if (viewModel.orderby != null) {
            repDef.setOrderby(viewModel.orderby.to.intValue());
        }

        if (viewModel.friendlyName != null) {
            repDef.setFriendlyName(viewModel.friendlyName.to);
        }

        if (viewModel.definition != null) {
            repDef.setBody(viewModel.definition.to);
        }

        reportDefinitionRepository.save(repDef);
    }
}
