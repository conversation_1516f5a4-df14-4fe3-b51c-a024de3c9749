package com.ecco.webApi.viewModels;

import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Data-transfer object representing a task.
 * Also see WorkflowTaskViewModel.
 */
public class TaskStatusViewModel { //extends RepresentationModel<TaskStatusViewModel> {

    /** instant the task was created (currently server side) */
    public Instant created;

    public int serviceRecipientId;

    public Integer serviceRecipientParentId;

    public UUID taskInstanceUuid;

    @Nullable
    public Long taskDefinitionId;

    public int serviceAllocationId;

    @Nullable
    public String taskDefName;

    /**
     * Indication of why this task is no longer available (eg sr is closed).
     */
    public Integer completedStatusId;

    /** The task has been actioned */
    public java.time.Instant completed;

    public String description;

    /** Due date for the task, if it has one. */
    @Nullable
    public LocalDateTime dueDate;

    /** The id of the user assigned to the task */
    @Nullable
    public Long assignee;

    @Nullable
    public String assigneeDisplayName;

    @Nullable
    public String assigneeEmail;

    /** The security group of users that this task is relevant for */
    @Nullable
    public Long relevantGroup;

}
