package com.ecco.webApi.notifications;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

/**
 * View model for notifications to be sent to the client
 */
@Data
@NoArgsConstructor
public class NotificationViewModel {

    /**
     * Unique identifier for the notification
     */
    private UUID id;

    /**
     * The ID of the user who should receive this notification
     */
    private long userId;

    /**
     * When the notification was created
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ", timezone = "UTC")
    private Instant created;

    /**
     * When the notification was read by the user (if it has been read)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ", timezone = "UTC")
    private Instant readAt;

    /**
     * The command UUID associated with this notification
     */
    private UUID commandUuid;
}
