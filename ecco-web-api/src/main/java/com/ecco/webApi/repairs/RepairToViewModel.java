package com.ecco.webApi.repairs;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.repairs.Repair;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoMessageUtils;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.webApi.contacts.AgencyToViewModel;
import com.ecco.webApi.viewModels.ServiceRecipientSourceViewModel;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

public class RepairToViewModel implements Function<Repair, RepairViewModel> {

    private final ListDefinitionRepository listDefinitionRepository;
    private final RepairRateRepository repairRateRepository;
    private final AgencyToViewModel agencyToViewModel = new AgencyToViewModel();

    public RepairToViewModel(ListDefinitionRepository listDefinitionRepository,
                             RepairRateRepository repairRateRepository) {
        this.listDefinitionRepository = listDefinitionRepository;
        this.repairRateRepository = repairRateRepository;
    }

    @Override
    public RepairViewModel apply(Repair input) {

        RepairViewModel result = new RepairViewModel();
        result.repairId = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.buildingId = input.getBuildingId();
        result.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();

        result.receivedDate = input.getReceivedDateJdk();
        result.displayName = input.getServiceRecipient().getDisplayName();
        result.categoryId = input.getCategoryId();
        result.rateId = input.getRateId();
        // `${l.area} - ${l.code} - ${l.description.substring(0, 30)}`}
        if (input.getRateId() != null) {
            this.repairRateRepository.findById(input.getRateId()).ifPresent(r -> {
                // NB see RepairDetailsForm: `${l.area} - ${l.code} - ${l.description.substring(0, 30)}`}
                // TODO - cache all rates, they don't change
                result.rateName = r.getArea() + " - " + r.getCode() + " - " + StringUtils.abbreviate(r.getDescription(), 30);
            });
        }
        result.categoryName = input.getCategoryId() != null ? this.listDefinitionRepository.findById(input.getCategoryId()).get().getName() : null;
        result.priorityId = input.getPriorityId();
        result.priorityName = input.getPriorityId() != null ? this.listDefinitionRepository.findById(input.getPriorityId()).get().getName() : null;

        // source
        result.selfReferral = input.isSelfReferral();
        result.referrerAgencyId = input.getReferrerAgency() != null ? input.getReferrerAgency().getId() : null;
        result.referrerIndividualId = input.getReferrerIndividual() != null ? input.getReferrerIndividual().getId() : null;
        ServiceRecipientSourceViewModel.sourceOfReferral(input.isSelfReferral(), input.getReferrerAgency(), input.getReferrerIndividual(), result, this.agencyToViewModel);

        // accept on service
        AcceptState[] states = ServiceRecipientCaseStatusView.Support.acceptedStates(input.isFinalDecision(), null, false, input.isAcceptedOnService());
        //result.appropriateReferralState = states[0];
        result.acceptOnServiceState = states[1];

        result.statusMessageKey = ServiceRecipientCaseStatusView.Support.getStatusMessageKey(input);
        result.statusMessage = EccoMessageUtils.getUiMessageSource().getMessage(result.statusMessageKey);

        result.decisionMadeOn = JodaToJDKAdapters.localDateToJDk(input.getDecisionMadeOn());
        result.signpostedReasonId = input.getSignpostedReasonId();
        result.signpostedBack = input.isSignpostedBack();
        result.signpostedAgencyId = input.getSignpostedAgencyId();
        result.signpostedExitComment = input.getSignpostedExitComment();

        // start
        if (input.getReceivingServiceDate() != null) {
            result.receivingServiceDate = input.getReceivingServiceDate().toLocalDate();
        }
        result.supportWorkerId = input.getSupportWorkerId();
        //result.supportWorkerDisplayName = supportWorker.getDisplayName();

        // exit
        result.exitedDate = input.getExited();
        result.exitReasonId = input.getExitReasonId();
        result.reviewDate = input.getReviewDate();

        return result;
    }

}