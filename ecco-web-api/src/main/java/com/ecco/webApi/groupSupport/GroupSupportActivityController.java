package com.ecco.webApi.groupSupport;

import com.ecco.dao.GroupSupportActivitySummary;
import com.ecco.dao.GroupSupportActivitySummaryStats;
import com.ecco.dao.EvidenceSupportCommentRepository;
import com.ecco.dom.ReferralStatusName;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.dom.Service;
import com.ecco.dom.groupsupport.GroupActivity_Referral;
import com.ecco.dom.groupsupport.GroupSupportActivity;
import com.ecco.dom.groupsupport.GroupSupportAttendance;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.groupsupport.repositories.GroupSupportActivityRepository;
import com.ecco.groupsupport.repositories.GroupSupportAttendanceRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.annotations.DeleteJson;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.viewModels.ResourceList;
import com.ecco.webApi.viewModels.Result;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static java.util.stream.Collectors.toList;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@PreAuthorize("hasRole('ROLE_USER')")
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public class GroupSupportActivityController extends BaseWebApiController {

    private final GroupSupportActivityRepository repository;
    private final GroupSupportActivityInvolvementRepository involvementRepository;
    private final ServiceRepository serviceRepository;
    private final GroupSupportAttendanceRepository groupSupportAttendanceRepository;

    private final EvidenceSupportCommentRepository supportCommentRepository;
    private final GroupSupportActivityToViewModel toShallowViewModel = new GroupSupportActivityToViewModel();

    private final GroupActivityCommandHandler groupActivityCommandHandler;
    private final GroupActivityAttendanceCommandHandler groupActivityAttendanceCommandHandler;
    private final GroupActivityInvitationCommandHandler groupActivityInvitationCommandHandler;

    private final ReportUnsecuredDelegator reportUnsecuredDelegator;

    @Autowired
    public GroupSupportActivityController(GroupSupportActivityRepository repository,
                                          GroupSupportActivityInvolvementRepository involvementRepository,
                                          ServiceRepository serviceRepository,
                                          GroupSupportAttendanceRepository groupSupportAttendanceRepository,
                                          EvidenceSupportCommentRepository supportCommentRepository,
                                          GroupActivityCommandHandler groupActivityCommandHandler,
                                          GroupActivityAttendanceCommandHandler groupActivityAttendanceCommandHandler,
                                          GroupActivityInvitationCommandHandler groupActivityInvitationCommandHandler,
                                          ReportUnsecuredDelegator reportUnsecuredDelegator) {
        this.repository = repository;
        this.involvementRepository = involvementRepository;
        this.serviceRepository = serviceRepository;
        this.supportCommentRepository = supportCommentRepository;
        this.groupSupportAttendanceRepository = groupSupportAttendanceRepository;
        this.groupActivityCommandHandler = groupActivityCommandHandler;
        this.groupActivityAttendanceCommandHandler = groupActivityAttendanceCommandHandler;
        this.groupActivityInvitationCommandHandler = groupActivityInvitationCommandHandler;
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
    }

    /** Get the next scheduled activity for each activity type on this service.
     *  NOTE: ActivityTypes with no future items scheduled are not returned in this list
     */
    @GetJson("/service/{serviceId}/activities/next")
    public List<GroupSupportActivitySummary> findNextActivityByServiceId(@PathVariable Long serviceId) {
        throwIfInvalidService(serviceId);
        Date startOfToday = DateTime.now().withTimeAtStartOfDay().toDate();
        List<GroupSupportActivity> activities = repository.findNextByService_Id(serviceId, startOfToday);
        return activities.stream().map(toShallowViewModel::apply).collect(toList());
    }

    private void throwIfInvalidService(Long serviceId) {
        Service service = serviceRepository.findById(serviceId).orElse(null);
        if (service == null) {
            throw new IllegalArgumentException("Could not find service with id = " + serviceId);
        }
    }

    /**
     * @param attendancesEndDate - if specified, includes attendances between from and to
     */
    @GetJson("/referrals/{referralId}/activities/")
    public List<ClientAttendanceViewModel> findGroupActivityInvolvementByReferralId(@PathVariable Long referralId,
                                                                                    @RequestParam("attendancesStartDate") @DateTimeFormat(iso=ISO.DATE) LocalDate attendancesStartDate,
                                                                                    @RequestParam("attendancesEndDate") @DateTimeFormat(iso=ISO.DATE) LocalDate attendancesEndDate) {
        List<GroupActivity_Referral> involvement = involvementRepository.findByMultiIdReferralId(referralId);

        ClientAttendanceToViewModel toViewModel = new ClientAttendanceToViewModel(supportCommentRepository)
                .withParentActivity().withAttendances(attendancesStartDate, attendancesEndDate);

        return involvement.stream().map(toViewModel).collect(toList());
    }

    @GetJson("/activities/")
    public ResourceList<GroupSupportActivitySummaryRowResource> findActivities(
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false, defaultValue = "sessions") String groupPageType,
            @RequestParam(required = false) Long serviceId,
            @RequestParam(required = false) Integer svcCatId,
            @RequestParam(required = false) Integer activityTypeId,
            @RequestParam(required = false) Integer venueId,
            @RequestParam(defaultValue="0") Integer page,
            @RequestParam(defaultValue="15") Integer pageSize) {
        ReportCriteriaDto criteria = new ReportCriteriaDto();
        criteria.setParentId(parentId);
        criteria.setServiceId(serviceId);
        criteria.setServiceCategorisationId(svcCatId);
        criteria.setActivityTypeId(activityTypeId);
        criteria.setGroupPageType(groupPageType);
        criteria.setVenueId(venueId);
        var pageReq = PageRequest.of(page, pageSize);
        return reportUnsecuredDelegator.reportGroupSupportActivitySummaries(criteria, pageReq);
    }

//    @GetJson("/activities/{activityId}/") - Can't do it this way and use methodOn until we can use Spring Hateoas 0.23 and Spring 4.3.x
    @RequestMapping(value = "/activities/{activityId}/", method = GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public GroupSupportActivitySummary findOneActivity(@PathVariable Long activityId) {
        GroupSupportActivityToViewModel toViewModel = new GroupSupportActivityToViewModel();
        GroupSupportActivitySummary summary = toViewModel.apply(repository.findById(activityId).orElseThrow());
        Map<Long, GroupSupportActivitySummaryStats> wrapper = new HashMap<>();
        wrapper.put(activityId, summary);
        reportUnsecuredDelegator.applyTotalsToGroupSupportSummaries(wrapper);
        return summary;
    }

    @GetJson(value = "/activities/byUuid/{activityUuid}/")
    public GroupSupportActivitySummary findOneActivityByUuid(@PathVariable UUID activityUuid) {
        GroupSupportActivityToViewModel toViewModel = new GroupSupportActivityToViewModel();
        GroupSupportActivitySummary summary = toViewModel.apply(repository.findOneByUuid(activityUuid));
        Map<Long, GroupSupportActivitySummaryStats> wrapper = new HashMap<>();
        wrapper.put(summary.getId(), summary);
        reportUnsecuredDelegator.applyTotalsToGroupSupportSummaries(wrapper);
        return summary;
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @DeleteJson("/activities/{activityId}/")
    public Result delete(@PathVariable Long activityId) {
        repository.deleteById(activityId);
        return new Result("deleted");
    }

    /**
     * Find those eligible and saved on a group support activity.
     */
    @GetJson("/activities/{activityId}/clients/")
    public Stream<ClientAttendanceViewModel> findInvitedClientsByActivityId(@PathVariable Long activityId) {

        GroupSupportActivity activity = repository.findById(activityId).orElse(null);
        Assert.state(activity != null, "No activity found with id=" + activityId);
        ReferralToClientAttendanceViewModel toViewModel = new ReferralToClientAttendanceViewModel(supportCommentRepository, activity.getActivityReferrals());
        Stream<ClientAttendanceViewModel> list = toViewModel.getRemainingAttendanceViewModels();

        // find eligible (if the activity has a service)
        Stream<? extends ReferralSummaryViewModel> referrals = eligibleReferrals(activity);
        List<ClientAttendanceViewModel> list1 = StreamSupport.stream(referrals.spliterator(), false).map(toViewModel).collect(toList());

        return Stream.concat(list1.stream(), list);
    }

    private Stream<? extends ReferralSummaryViewModel> eligibleReferrals(GroupSupportActivity activity) {
        // skip if no service provided... otherwise we return all live
        if (activity.getServiceId() == null) {
            return Stream.empty();
        }
        boolean hasEndDate = activity.getToDate() != null;
        ReportCriteriaDto dto = ReportCriteriaDto.builder()
                .withReferralStatus(hasEndDate ? ReferralStatusName.Live : ReferralStatusName.LiveAtEnd)
                .withServiceId(activity.getServiceId())
                .withProjectId(activity.getProjectId())
                .withJodaFrom(activity.getFromDate())
                .withJodaTo(hasEndDate ? activity.getToDate() : activity.getFromDate())
                .build();
        Stream<? extends ReferralSummaryViewModel> referrals = reportUnsecuredDelegator.reportReferralSummaryViewModel(dto, null, false);
        return referrals;
    }

    /**
     * Find those eligible and saved on a group support activity IF IT HAS AN END DATE
     */
    @GetJson("/activities/{activityId}/attendance/")
    public Stream<ClientAttendanceViewModel> findAttendanceByActivityIdWithEndDate(@PathVariable Long activityId,
                                                                                     @RequestParam("from") @DateTimeFormat(iso=ISO.DATE) DateTime from,
                                                                                     @RequestParam("to") @DateTimeFormat(iso=ISO.DATE) DateTime to) {

        GroupSupportActivity activity = repository.findById(activityId).orElse(null);
        Assert.state(activity != null, "No activity found with id=" + activityId);

        List<GroupSupportAttendance> attendances = groupSupportAttendanceRepository.findAllByActivity(activityId, from, to);

        // load the referrals on the service
        ReportCriteriaDto dto = new ReportCriteriaDto();
        boolean hasEndDate = activity.getToDate() != null;
        dto.setReferralStatus(hasEndDate ? ReferralStatusName.Live.getName() : ReferralStatusName.LiveAtEnd.getName());
        dto.setServiceId(activity.getServiceId());
        dto.setFrom(activity.getFromDate().toLocalDate().toString(ISODateTimeFormat.date()));
        dto.setTo(hasEndDate
                ? activity.getToDate().toLocalDate().toString(ISODateTimeFormat.date())
                : dto.getFrom());
        Stream<ReferralSummaryViewModel> referralsOnService = reportUnsecuredDelegator.reportReferralSummaryViewModel(dto, null, false);

        ReferralClientAttendanceWithEndDateViewModel toViewModel = new ReferralClientAttendanceWithEndDateViewModel(from, to, attendances);
        return referralsOnService.map(toViewModel);
    }

    @PostJson("/activities/commands/invitation/")
    public Result updateActivityReferrals(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return groupActivityInvitationCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @PostJson("/activities/commands/")
    public Result updateActivity(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return groupActivityCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @PostJson("/activities/commands/attendance/")
    public Result updateAttendance(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return groupActivityAttendanceCommandHandler.handleCommand(authentication, null, requestBody);
    }
}
