package com.ecco.webApi.listsConfig;

import com.ecco.dom.agreements.AppointmentTypeRepository;
import com.ecco.dom.agreements.AppointmentType;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;

import static java.util.stream.Collectors.toList;

@RestController
public class AppointmentTypeController extends BaseWebApiController {

    private final AppointmentTypeRepository appointmentTypeRepository;

    private final AppointmentTypeToViewModel appointmentTypeToViewModel = new AppointmentTypeToViewModel();

    private final AppointmentTypeFromViewModel appointmentTypeFromViewModel;


    @Autowired
    public AppointmentTypeController(AppointmentTypeRepository appointmentTypeRepository, ServiceRepository serviceRepository) {
        this.appointmentTypeRepository = appointmentTypeRepository;
        this.appointmentTypeFromViewModel = new AppointmentTypeFromViewModel(serviceRepository);
    }


    @GetJson("/appointmentTypes/{id}/")
    public AppointmentTypeViewModel findOne(@PathVariable long id) {
        return appointmentTypeToViewModel.apply(appointmentTypeRepository.findById(id).orElse(null));
    }


    @GetJson("/appointmentTypes/")
    public Iterable<AppointmentTypeViewModel> findAll(@RequestParam(required=false) String serviceName) {

        return (serviceName != null
                    ? appointmentTypeRepository.findByServiceName(serviceName)
                    : appointmentTypeRepository.findAllWithService())
                .map(appointmentTypeToViewModel).collect(toList());
    }


    @PostJson("/appointmentTypes/")
    public Map<String, Long> create(@RequestBody AppointmentTypeViewModel appointmentTypeViewModel) {

        final AppointmentType appointmentType = appointmentTypeFromViewModel.apply(appointmentTypeViewModel);

        appointmentTypeRepository.save(appointmentType);
        return Collections.singletonMap("id", appointmentType.getId());
    }
}
