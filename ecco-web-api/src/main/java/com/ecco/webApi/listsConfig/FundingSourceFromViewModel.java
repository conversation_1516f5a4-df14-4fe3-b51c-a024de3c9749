package com.ecco.webApi.listsConfig;

import com.ecco.dom.FundingSource;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

final class FundingSourceFromViewModel implements Function<FundingSourceViewModel, FundingSource> {

    @Nullable
    @Override
    public FundingSource apply(@Nullable FundingSourceViewModel input) {
        if (input == null) {
            throw new NullPointerException("input FundingSource must not be null");
        }

        FundingSource e = new FundingSource();
        // ignore id
        e.setName(input.name);

        return e;
    }

}
