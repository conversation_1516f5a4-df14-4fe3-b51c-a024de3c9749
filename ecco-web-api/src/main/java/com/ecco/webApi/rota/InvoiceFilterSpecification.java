package com.ecco.webApi.rota;

import com.ecco.dom.agreements.QClientSalesInvoice;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;

/** Filter to include startDate -> endDate *inclusive* */
@AllArgsConstructor
@Getter
class InvoiceFilterSpecification {

    private final LocalDate startDate;
    private final LocalDate endDate;


    BooleanExpression toPredicate() {
        return QClientSalesInvoice.clientSalesInvoice.invoiceDate.between(startDate, endDate);
    }
}
