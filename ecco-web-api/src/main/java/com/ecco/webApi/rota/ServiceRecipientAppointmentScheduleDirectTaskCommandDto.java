package com.ecco.webApi.rota;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

/**
 * Command to update an appointment, which could also be used to create an appointment (ad-hoc or not).
 */
@Slf4j
@NoArgsConstructor
@RequiredArgsConstructor
public class ServiceRecipientAppointmentScheduleDirectTaskCommandDto {

    @NonNull
    public String operation;

    /**
     * The definition id of the task.
     * This maps to actionDefId - see useDirectTasks.
     */
    @Nullable
    public Integer taskDefId;

    /**
     * Unique reference to a task instance representing the taskText and taskDescription.
     * This maps to actionInstanceUuid - see careSingleVisitDataLoader.tsx directTask, and useDirectTasks
     */
    @Nullable
    public String taskInstanceId;

    @Nullable
    public ChangeViewModel<String> taskText;

    @Nullable
    public ChangeViewModel<String> taskDescription;

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (taskDefId == null) {
                log.error("Required field: taskDefId");
                return false;
            }
        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_UPDATE)) {
            if (taskInstanceId == null) {
                log.error("Required field: taskHandle");
                return false;
            }
        }

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_REMOVE)) {
            if (taskInstanceId == null) {
                log.error("Required field: taskHandle");
                return false;
            }
        }

        return valid;
    }

}
