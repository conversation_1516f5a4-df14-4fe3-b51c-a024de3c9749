package com.ecco.webApi.rota

import com.ecco.calendar.core.CalendarService
import com.ecco.calendar.core.Recurrence
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle
import com.ecco.calendar.cosmo.CosmoCalendarService
import com.ecco.calendar.event.RotaDemandPostChangeEvent
import com.ecco.config.service.SettingsService
import com.ecco.dao.DemandScheduleRepository
import com.ecco.dao.commands.ServiceRecipientCommandRepository
import com.ecco.dom.agreements.DaysOfWeek
import com.ecco.dom.commands.AppointmentRecurringActionCommand
import com.ecco.infrastructure.bus.MessageBus
import com.ecco.infrastructure.hibernate.EntityUriMapper
import com.ecco.infrastructure.time.JodaToJDKAdapters.UTC
import com.ecco.infrastructure.time.toJDK
import com.ecco.infrastructure.util.EccoTimeUtils.LONDON
import com.ecco.rota.service.RotaDelegator
import com.ecco.rota.service.RotaHandler
import com.ecco.rota.service.RotaService
import com.ecco.webApi.CommandResult
import com.ecco.webApi.evidence.AfterCommandHandlerTxEvent
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler
import com.ecco.webApi.rota.AppointmentRecurringActionCommandDto.OPERATION_ALLOCATE
import com.ecco.webApi.rota.AppointmentRecurringActionCommandDto.OPERATION_DEALLOCATE
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.collect.Range
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEvent
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import org.springframework.util.Assert
import java.io.Serializable
import java.time.temporal.ChronoUnit
import java.time.temporal.Temporal
import javax.persistence.EntityManager
import javax.persistence.PersistenceContext
import java.time.LocalDate as LocalDateJDK

/**
 * Perform an action on a repeating appointment schedule - ALLOCATE / DEALLOCATE
 */
@Component
class AppointmentRecurringActionCommandHandler
@Autowired
constructor(
    objectMapper: ObjectMapper,
    serviceRecipientCommandRepository: ServiceRecipientCommandRepository,
    private val calendarService: CalendarService,
    private val rotaService: RotaService,
    private val rotaDelegator: RotaDelegator,
    private val settingsService: SettingsService,
    private val demandScheduleRepository: DemandScheduleRepository,
    private val entityUriMapper: EntityUriMapper,
    private val messageBus: MessageBus<ApplicationEvent>,
) : ServiceRecipientCommandHandler<AppointmentRecurringActionCommandDto, AppointmentRecurringActionCommand, AppointmentActionParams>(
    objectMapper,
    serviceRecipientCommandRepository,
    AppointmentRecurringActionCommandDto::class.java,
) {
    @PersistenceContext
    var entityManager: EntityManager? = null

    override fun handleInternal(
        auth: Authentication,
        params: AppointmentActionParams,
        dto: AppointmentRecurringActionCommandDto,
    ): CommandResult {
        Assert.state(
            params.getResourceFilter() == dto.resourceFilter,
            "demandedResourceFilter not matching",
        )
        val result: CommandResult =
            when (dto.operation.lowercase()) {
                OPERATION_ALLOCATE -> allocateAppointments(dto)
                OPERATION_DEALLOCATE -> deallocateAppointments(dto)
                else -> throw IllegalArgumentException("cannot handle operation: " + dto.operation)
            }

        // there isn't a link for viewing/editing a single appointment really, so just return null
        // return CommandResult.ofLink(linkToApi(methodOn(RotaController.class).getAppointment(dto.activityRef)).withRel(REL_EDIT));
        return result // FIXME HERE .. sort out how we combine result and link where relevant
    }

    override fun persistCommand(command: AppointmentRecurringActionCommand): AppointmentRecurringActionCommand {
        val cmdSaved: AppointmentRecurringActionCommand = super.persistCommand(command)
        entityManager!!.flush()
        return cmdSaved
    }

    private fun getRecurringEntryHandle(dto: AppointmentRecurringActionCommandDto): RecurringEntryHandle {
        var uid = dto.activityRef
        if (CosmoCalendarService.isRecurrenceUid(dto.activityRef)) {
            uid = calendarService.getEntryHandleFromRecurrenceHandle(Recurrence.RecurrenceHandle.fromString(uid)).toString()
        }
        return RecurringEntryHandle.fromString(uid)
    }

    private fun deallocateAppointments(dto: AppointmentRecurringActionCommandDto): CommandResult {
        // we need a schedule to operate on, not an individual recurrence
        val recurringEntryHandle = getRecurringEntryHandle(dto)
        val demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(recurringEntryHandle)
        val demandSchedule = demandScheduleRepository.findOneByEntryHandleAsString(demandScheduleHandle.toString()).orElseThrow()

        val handler: RotaHandler = rotaDelegator.selectHandler(dto.resourceFilter, dto.demandFilter)

        // NB incoming dates are LONDON based - so midnight to 1am in DST needs to be translated back to UTC
        val start =
            dto.partSchedule.applicableFromDate!!
                .atStartOfDay(LONDON)
                .toInstant()
        val minEnd =
            when {
                dto.partSchedule.endDate?.to != null -> dto.partSchedule.endDate?.to!!
                else -> demandSchedule.end?.toJDK()
            }
        val range =
            if (minEnd ==
                null
            ) {
                Range.atLeast(start)
            } else {
                Range.closedOpen(start, minEnd.plusDays(1).atStartOfDay(LONDON).toInstant())
            }
        // NB incoming dates are LONDON based

        // matches rota-domain.ts deallocateActivity
        val matchPartScheduleDays = DaysOfWeek.fromCalendarDays(dto.partSchedule.days?.added)

        val uri = entityUriMapper.uriForEntity(AppointmentRecurringActionCommandDto::class.simpleName, dto.uuid)

        val count =
            rotaService.deallocateActivitiesFrom(
                handler,
                recurringEntryHandle,
                range,
                matchPartScheduleDays,
                uri,
                dto.deallocateResourceId,
            )

        val useSeriesCalendar = true; // calendarService.allocateAsyncAfterDays() == null
        val msg = if (useSeriesCalendar) "appointments deallocated" else "$count appointments deallocated"
        return CommandResult()
            .withTargetId(demandSchedule.id)
            .withMessage(msg)
    }

    private fun allocateAppointments(dto: AppointmentRecurringActionCommandDto): CommandResult {
        // we need a schedule to operate on, not an individual recurrence
        val demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(getRecurringEntryHandle(dto))
        val demandSchedule =
            demandScheduleRepository
                .findOneByEntryHandleAsString(demandScheduleHandle.toString())
                .orElseThrow { IllegalStateException("Could not find demandScheduleHandle: $demandScheduleHandle") }

        // determine the range to confirm...
        //      - start: from applicableFromDate
        //      - end: a year from now, or the agreement end date (bit of a fudge, assuming they don't apply from a year ago - which could be fixed in ui as min today)
        val start =
            dto.partSchedule.applicableFromDate!!
                .atStartOfDay(LONDON)
                .toInstant()

        val maxMonths = settingsService.settingFor("com.ecco.rota", "maxRecurringAllocateMonths").asNumber.toLong()
        val aLongTimeFromToday = LocalDateJDK.now(UTC).plusMonths(maxMonths)
        val useSeriesCalendar = true; // calendarService.allocateAsyncAfterDays() == null

        val minEnd =
            when {
                dto.partSchedule.endDate?.to != null -> dto.partSchedule.endDate?.to!!
                !useSeriesCalendar && aLongTimeFromToday.isBefore(demandSchedule.end.toJDK()) -> aLongTimeFromToday
                else -> demandSchedule.end?.toJDK()
            }
        // NB ALSO SEE RotaServiceImpl#addOneAppointmentSchedule
        val range =
            if (minEnd ==
                null
            ) {
                Range.atLeast(start)
            } else {
                Range.closedOpen(start, minEnd.plusDays(1).atStartOfDay(LONDON).toInstant())
            }
        val matchPartScheduleDays = DaysOfWeek.fromCalendarDays(dto.partSchedule.days?.added)

        val uri = entityUriMapper.uriForEntity(AppointmentRecurringActionCommandDto::class.simpleName, dto.uuid)

        val handler: RotaHandler = rotaDelegator.selectHandler(dto.resourceFilter, dto.demandFilter)
        val recurrenceBounds =
            if (useSeriesCalendar) {
                Range.atLeast(
                    demandSchedule.start,
                )
            } else {
                Range.closedOpen(demandSchedule.start, demandSchedule.end)
            }

        if (useSeriesCalendar) {
            // don't indicate a number of apts affected here, as its likely to always be one master entry
            // NB call with the series handle, but the method now copes if called with any in the series
            calendarService.confirmRecurrencesInRange(
                demandScheduleHandle,
                range,
                matchPartScheduleDays,
                uri,
                dto.allocateRescheduledMins,
                recurrenceBounds,
                handler.getResourceCalendarId(dto.allocateResourceId!!),
            )
            return CommandResult()
                .withTargetId(demandSchedule.id)
                .withMessage("appointments allocated ${minEnd.descEndRange()}")
        } else {
            val splitPoint = start.plus(calendarService.allocateAsyncAfterDays().toLong(), ChronoUnit.DAYS)
            if (range.upperEndpoint().isAfter(splitPoint)) {
                val count =
                    calendarService.confirmRecurrencesInRange(
                        demandScheduleHandle,
                        Range.closedOpen(start, splitPoint),
                        matchPartScheduleDays,
                        uri,
                        dto.allocateRescheduledMins,
                        recurrenceBounds,
                        handler.getResourceCalendarId(dto.allocateResourceId!!),
                    )

                val r =
                    Runnable {
                        log.info("Allocating from {} asynchronously", splitPoint)
                        try {
                            val asyncCount =
                                calendarService.confirmRecurrencesInRange(
                                    demandScheduleHandle,
                                    Range.closedOpen(splitPoint, range.upperEndpoint()),
                                    matchPartScheduleDays,
                                    uri,
                                    dto.allocateRescheduledMins,
                                    recurrenceBounds,
                                    handler.getResourceCalendarId(dto.allocateResourceId!!),
                                )
                            log.info("Num allocated async: {}", asyncCount)
                        } catch (e: Exception) {
                            log.error("Failed in async", e)
                        }
                    }
                messageBus.publishAfterTxEnd(AfterCommandHandlerTxEvent(r))

                return CommandResult()
                    .withTargetId(demandSchedule.id)
                    .withMessage("$count appointments allocated so far ${splitPoint.descEndRange()} ...")
            } else {
                val count =
                    calendarService.confirmRecurrencesInRange(
                        demandScheduleHandle,
                        range,
                        matchPartScheduleDays,
                        uri,
                        dto.allocateRescheduledMins,
                        recurrenceBounds,
                        handler.getResourceCalendarId(dto.allocateResourceId!!),
                    )
                return CommandResult()
                    .withTargetId(demandSchedule.id)
                    .withMessage("$count appointments allocated ${minEnd.descEndRange()}")
            }
        }
    }

    override fun createCommand(
        targetId: Serializable?,
        params: AppointmentActionParams,
        requestBody: String,
        viewModel: AppointmentRecurringActionCommandDto,
        userId: Long,
    ): AppointmentRecurringActionCommand {
        Assert.state(
            params.serviceRecipientId == viewModel.serviceRecipientId,
            "serviceRecipientId in body must match URI",
        )

        messageBus.publishBeforeTxEnd(
            RotaDemandPostChangeEvent(
                viewModel,
                viewModel.serviceRecipientId,
                viewModel.cacheResetDate,
                getRecurringEntryHandle(viewModel),
                false,
            ),
        )

        return AppointmentRecurringActionCommand(
            viewModel.uuid,
            viewModel.timestamp,
            userId,
            requestBody,
            params.serviceRecipientId,
            targetId.toString().toLong(),
        )
    }
}

private fun Temporal?.descEndRange(): String = when (this) {
    null -> ""
    else -> "up to $this"
}