package com.ecco.webApi.incidents;

import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingLinkDescriptionObject;
import com.ecco.security.SecurityUtil;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import com.ecco.webApi.viewModels.ResourceList;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

import static com.ecco.dom.incidents.QIncident.incident;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;


@RestController
@RequestMapping("/incidents")
@Secured("ROLE_INCIDENTS")
public class IncidentListController extends SchemaProvidingController<IncidentListController> {
    private static final String REL_INSTANCES = "instances";

    @Value("${ecco.api.basePath:}")
    private String apiBasePath;

    private final ReportUnsecuredDelegator reportUnsecuredDelegator;
    private final ServiceRecipientRepository serviceRecipientRepository;

    public IncidentListController(ReportUnsecuredDelegator reportUnsecuredDelegator,
                                  ServiceRecipientRepository serviceRecipientRepository) {
        super();
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    public String getEntityTypeName() {
        return "incidents";
    }

    @GetJson("/")
    public ResourceList<IncidentViewModel> list(
//            @RequestParam(name = "from")
//            @DateTimeFormat(iso= DateTimeFormat.ISO.DATE)
//            @JsonSchemaProperty(format = JsonValueFormat.DATE)
//            @JsonSchemaMetadata(order = 5)
//            LocalDate from,

//            @RequestParam(name = "search", required = false)
//            @JsonSchemaMetadata(order = 10)
//            String search,

//            @RequestParam(name = "postCode", required = false)
//            @JsonSchemaMetadata(order = 30)
//            String postCode,

            @RequestParam(name = "contactSrId", required = false)
            @JsonSchemaMetadata(order = 30)
            Integer contactSrId,

            @RequestParam(name = "page", defaultValue="0")
            Integer page,

            @RequestParam(name = "pageSize", defaultValue="50") // could do getPageSize("pageSize.users"));
            @JsonSchemaProperty(readOnly = true)
            Integer pageSize) {

        PageRequest pr = incidentPageAndSorting(page, pageSize);

        Long contactId = null;
        if (contactSrId != null) {
            contactId = serviceRecipientRepository.getById(contactSrId).getContact().getId();
        }

        var allIncidents = SecurityUtil.hasAuthority("ROLE_ADMININCIDENT");
        var teamIncidents = !allIncidents && SecurityUtil.hasAuthority("ROLE_TEAMINCIDENTS");
        var onlyMyIncidents = SecurityUtil.getAuthenticatedUserContactId();
        var resourceList = reportUnsecuredDelegator.reportIncidentSummaries(contactSrId, contactId, allIncidents, teamIncidents, onlyMyIncidents, pr);
        addDescribedByLink(resourceList);
        return resourceList;
    }

    @Override
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        Object invocation = self().list(null, 0, 0);

        JsonSchema listRequestParamSchema = getSchemaCreator().createForRequestParams(invocation);

        LinkDescriptionObject instancesLink = new SchemaProvidingLinkDescriptionObject()
                .setRel(REL_INSTANCES)
                .setMethod(RequestMethod.GET.toString())
                .setHref(linkTo(invocation).toUriComponentsBuilder().replaceQuery(null).build(false).toString())
                .setSchema(listRequestParamSchema);

        JsonSchema schema = getSchemaCreator().create(IncidentViewModel.class,
                self().describe(request),
                Optional.of(instancesLink),
                Optional.empty());

        cacheForXSecs(request, 180);
        return ResponseEntity.ok(schema);
    }

    private PageRequest incidentPageAndSorting(int page, int pageSize) {
        QSort sort = new QSort(incident.receivedDate.desc());
        return PageRequest.of(page, pageSize, sort);
    }

}
