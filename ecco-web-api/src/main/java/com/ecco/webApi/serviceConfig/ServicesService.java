package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Service;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToProjectViewModel;
import com.ecco.webApi.viewModels.ServicesViewModel;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Component
@RequiredArgsConstructor
public class ServicesService {

    private final ServiceRepository serviceRepository;

    private ServiceToViewModel serviceToViewModel;

    @PostConstruct
    public void init() {
        serviceToViewModel = new ServiceToViewModel(new ServiceCategorisationToProjectViewModel());
    }

    @NonNull
    public Stream<ServiceViewModel> findAll() {
        return serviceRepository.findAll().stream().map(serviceToViewModel);
    }

    @NonNull
    public ServicesViewModel findAllServicesProjects() {
        ServicesViewModel result = new ServicesViewModel();
        result.services = serviceRepository.findAll().stream()
                .map(Service::toViewModel)
                .collect(toList());
        return result;
    }

    @SuppressWarnings("unused") // Used from SpEL in InboundReferralResource
    public Map<String, String> findAllInboundServicesAsMap() {
        // TODO: add {"allowExternalReferrals":true} to accommodation and another service
        return findAllServicesProjects().services.stream()
                .filter(com.ecco.dto.ServiceViewModel::isAllowInboundReferral)
                .collect(toMap(s -> s.id.toString(), s -> s.name));
    }

    public ServiceViewModel findOne(long id) {
        return serviceToViewModel.apply(serviceRepository.findById(id).orElse(null));
    }
}