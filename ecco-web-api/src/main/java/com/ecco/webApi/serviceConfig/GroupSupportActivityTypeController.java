package com.ecco.webApi.serviceConfig;

import static java.util.stream.Collectors.toList;

import java.io.IOException;
import java.util.List;
import java.util.stream.StreamSupport;

import com.ecco.serviceConfig.viewModel.GroupSupportActivityTypeToViewModel;
import com.ecco.webApi.groupSupport.EditTypeCommandHandler;
import com.ecco.webApi.viewModels.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import com.ecco.serviceConfig.dom.GroupSupportActivityType;
import com.ecco.serviceConfig.viewModel.IdNameWithServicesViewModel;
import com.ecco.serviceConfig.repositories.GroupSupportActivityTypeRepository;
import com.ecco.webApi.controllers.BaseWebApiController;

import org.jspecify.annotations.NonNull;

@PreAuthorize("hasRole('ROLE_USER')")
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
public class GroupSupportActivityTypeController extends BaseWebApiController {

    private final GroupSupportActivityTypeRepository repository;

    private final GroupSupportActivityTypeToViewModel toViewModel = new GroupSupportActivityTypeToViewModel();

    private final ObjectArrayToGSATWithCount toGSATWithCount = new ObjectArrayToGSATWithCount();

    private final EditTypeCommandHandler editTypeCommandHandler;

    @Autowired
    public GroupSupportActivityTypeController(GroupSupportActivityTypeRepository repository, EditTypeCommandHandler editTypeCommandHandler) {
        this.repository = repository;
        this.editTypeCommandHandler = editTypeCommandHandler;
    }

    @RequestMapping(value = "/actionDefs/{actionId}/activityTypes/", method = RequestMethod.GET)
    public List<IdNameWithServicesViewModel> findActivityTypesByActionId(@PathVariable Long actionId) {
        List<GroupSupportActivityType> activityTypes = repository.findActivityTypesByActionId(actionId);
        return activityTypes.stream().map(toViewModel).collect(toList());
    }

    @RequestMapping(value = "/activityTypes/", method = RequestMethod.GET)
    public List<IdNameWithServicesViewModel> findAllActivityTypes() {
        Iterable<GroupSupportActivityType> activityTypes = repository.findAll();
        return StreamSupport.stream(activityTypes.spliterator(), false).map(toViewModel).collect(toList());
    }

    @RequestMapping(value = "/service/{serviceId}/activityTypes/", method = RequestMethod.GET)
    public List<ActivityTypeViewModel> findActivityTypesByServiceIdWithCount(@PathVariable Long serviceId) {
        List<Object[]> activityTypes = repository.findByService_IdWithCountsOfInterest(serviceId);
        return activityTypes.stream().map(toGSATWithCount).collect(toList());
    }

    @PostJson("/activities/commands/type/")
    public Result updateActivity(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return editTypeCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
