package com.ecco.webApi.serviceConfig.hact;

import com.ecco.serviceConfig.hact.repositories.HactOutcomeEvidenceActivityRepository;
import com.ecco.serviceConfig.hact.repositories.HactOutcomeEvidenceSurveyRepository;
import com.ecco.serviceConfig.hact.repositories.HactOutcomeMappingRepository;
import com.ecco.serviceConfig.hact.repositories.HactSocialValueBankRepository;
import com.ecco.serviceConfig.hact.viewModel.*;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.StreamSupport;

import static java.util.stream.Collectors.toList;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping(produces = APPLICATION_JSON_VALUE)
public class HactController extends BaseWebApiController {

    @NonNull
    private final HactOutcomeMappingRepository hactOutcomeMappingRepository;

    @NonNull
    private final HactOutcomeEvidenceSurveyRepository hactOutcomeEvidenceSurveyRepository;

    @NonNull
    private final HactOutcomeEvidenceActivityRepository hactOutcomeEvidenceActivityRepository;

    @NonNull
    private final HactSocialValueBankRepository hactSocialValueBankRepository;

    @NonNull
    private final HactOutcomeMappingToViewModel hactOutcomeMappingToViewModel = new HactOutcomeMappingToViewModel();

    @NonNull
    private final HactOutcomeEvidenceSurveyToViewModel hactOutcomeEvidenceSurveyToViewModel = new HactOutcomeEvidenceSurveyToViewModel();

    @NonNull
    private final HactOutcomeEvidenceActivityToViewModel hactOutcomeEvidenceActivityToViewModel = new HactOutcomeEvidenceActivityToViewModel();

    @NonNull
    private final HactSocialValueBankToViewModel hactSocialValueBankToViewModel = new HactSocialValueBankToViewModel();

    // TODO CacheManager cacheManager;


    @Autowired
    public HactController(@NonNull HactOutcomeMappingRepository hactOutcomeMappingRepository,
            @NonNull HactOutcomeEvidenceSurveyRepository hactOutcomeEvidenceSurveyRepository,
            @NonNull HactOutcomeEvidenceActivityRepository hactOutcomeEvidenceActivityRepository,
            @NonNull HactSocialValueBankRepository hactSocialValueBankRepository) {
        this.hactOutcomeEvidenceSurveyRepository = hactOutcomeEvidenceSurveyRepository;
        this.hactOutcomeEvidenceActivityRepository = hactOutcomeEvidenceActivityRepository;
        this.hactOutcomeMappingRepository = hactOutcomeMappingRepository;
        this.hactSocialValueBankRepository = hactSocialValueBankRepository;
    }

    @GetMapping("/hact/outcomeMappings/")
    public List<HactOutcomeMappingViewModel> findAllOutcomeMappings() {
        return StreamSupport.stream(hactOutcomeMappingRepository.findAll().spliterator(), false)
                .map(hactOutcomeMappingToViewModel)
                .collect(toList());
    }

    @GetMapping("/hact/outcomeEvidenceSurvey/")
    public List<HactOutcomeEvidenceSurveyViewModel> findAllOutcomeEvidenceSurvey() {
        return hactOutcomeEvidenceSurveyRepository.findAll()
                .stream().map(hactOutcomeEvidenceSurveyToViewModel)
                .collect(toList());
    }

    @GetMapping("/hact/outcomeEvidenceActivity/")
    public List<HactOutcomeEvidenceActivityViewModel> findAllOutcomeEvidenceActivities() {
        return StreamSupport.stream(hactOutcomeEvidenceActivityRepository.findAll().spliterator(), false)
                .map(hactOutcomeEvidenceActivityToViewModel)
                .collect(toList());
    }

    @GetMapping("/hact/socialValueBank/")
    public List<HactSocialValueBankViewModel> findAllSocialValueBank() {
        return StreamSupport.stream(hactSocialValueBankRepository.findAll().spliterator(), false)
                .map(hactSocialValueBankToViewModel)
                .collect(toList());
    }

}
