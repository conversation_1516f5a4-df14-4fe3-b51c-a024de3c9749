package com.ecco.webApi.serviceConfig;

import static java.util.stream.Collectors.toList;

import java.util.function.Function;

import com.ecco.serviceConfig.dom.Question;
import com.ecco.serviceConfig.dom.QuestionGroupQuestion;
import com.ecco.serviceConfig.dom.QuestionGroupSupport;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;

import org.jspecify.annotations.Nullable;

final class QuestionGroupFromViewModel implements Function<QuestionGroupViewModel, QuestionGroupSupport> {

    private static final Function<QuestionViewModel, Question> questionFromViewModel = new QuestionFromViewModel();

    @Nullable
    @Override
    public QuestionGroupSupport apply(@Nullable QuestionGroupViewModel input) {
        if (input == null) {
            throw new NullPointerException("input QGVM must not be null");
        }

        QuestionGroupSupport questionGroup = new QuestionGroupSupport();
        questionGroup.setId(input.id);
        questionGroup.setName(input.name);
        questionGroup.setHeaderText(input.headerText);

        questionGroup.setQuestions(input.questions.stream().map(questionFromViewModel).map(q -> new QuestionGroupQuestion(questionGroup, q, 0)).collect(toList()));

        return questionGroup;
    }
}