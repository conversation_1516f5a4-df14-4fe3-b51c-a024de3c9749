package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Project;
import com.ecco.dom.Service;
import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.security.dom.LdapGroupMapping;
import com.ecco.security.repositories.LdapRepository;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.dom.ServiceCommand;
import com.ecco.serviceConfig.dom.ServiceTypeMinimalView;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class ServiceCommandHandler extends BaseCommandHandler<ServiceCommandViewModel, Long, ConfigCommand, @Nullable Void> {


    @NonNull
    private final ServiceRepository serviceRepository;

    @NonNull
    private final LdapRepository ldapRepository;

    @NonNull
    private final EntityRestrictionService entityRestrictionService;

    @NonNull
    private final ProjectRepository projectRepository;

    @NonNull
    private final ServiceCategorisationRepository serviceCategorisationRepository;

    @Autowired
    public ServiceCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                 ServiceRepository serviceRepository,
                                 LdapRepository ldapRepository,
                                 EntityRestrictionService entityRestrictionService,
                                 ProjectRepository projectRepository,
                                 ServiceCategorisationRepository serviceCategorisationRepository) {
        super(objectMapper, configCommandRepository, ServiceCommandViewModel.class);
        this.serviceRepository = serviceRepository;
        this.ldapRepository = ldapRepository;
        this.entityRestrictionService = entityRestrictionService;
        this.projectRepository = projectRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull ServiceCommandViewModel viewModel) {

        Service service = (viewModel.serviceId == null)
            ? createNewService(viewModel)
            : serviceRepository.findById(viewModel.serviceId).orElseThrow(NullPointerException::new);

        if (viewModel.name != null) {
            service.setName(viewModel.name.to);
        }

        service = serviceRepository.save(service);

        if (viewModel.serviceId == null) {
            entityRestrictionService.ensureAcls();
        }

        if (viewModel.projectsToAdd != null) {
            for (long projectId : viewModel.projectsToAdd) {
                addProject(service, projectId);
                addProjectLdapGroups(service.getId(), projectId);
            }
        }
        return null;
    }

    private Service createNewService(ServiceCommandViewModel viewModel) {
        Service service = new Service();
        ServiceTypeMinimalView serviceType = new ServiceTypeMinimalView();
        serviceType.setId(viewModel.serviceTypeId);
        service.setServiceType(serviceType);
        return service;
    }

    @NonNull
    @Override
    protected ConfigCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody,
                                          @NonNull ServiceCommandViewModel viewModel, long userId) {
        return new ServiceCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

    private void addProject(Service service, long projectId) {
        Project project = projectRepository.getOne(projectId);
        ServiceCategorisation serviceCategorisation = new ServiceCategorisation(service, project);
        this.serviceCategorisationRepository.save(serviceCategorisation);
    }

    private void addProjectLdapGroups(long serviceId, long projectId) {
        Iterable<LdapGroupMapping> ldapGroups = this.ldapRepository.findAllByLocalClassAndLocalId(Project.class.getName(), projectId);
        for (LdapGroupMapping ldapGroup : ldapGroups) {
            LdapGroupMapping ldap = new LdapGroupMapping(
                    ldapGroup.getLdapGroup(),
                    Service.class.getName(),
                    serviceId);
            this.ldapRepository.save(ldap);
        }
    }

}
