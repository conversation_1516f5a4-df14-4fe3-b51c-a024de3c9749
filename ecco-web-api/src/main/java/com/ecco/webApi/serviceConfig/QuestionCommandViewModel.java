package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;

/**
 * Configuration of a question
 */
@Slf4j
public class QuestionCommandViewModel extends QuestionnaireBaseCommandViewModel {

    @Nullable
    public ChangeViewModel<String> nameChange;

    @Nullable
    public ChangeViewModel<String> typeChange; // change the type of question - only obeyed with 'add' only for now and probably forever

    @Nullable
    public ChangeViewModel<String> listNameChange;

    @Nullable
    public ChangeViewModel<Integer> orderByChange;

    @Nullable
    public ChangeViewModel<Boolean> disableChange;

    @Nullable
    public Integer questionGroupId;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected QuestionCommandViewModel() {
        super();
    }

    public boolean hasChanges() {
        return nameChange != null || typeChange != null || listNameChange != null || disableChange != null;
    }

    public QuestionCommandViewModel(@NonNull String operation, Integer questionGroupId, Integer id) {
        super(UriComponentsBuilder
                .fromUriString("config/questionnaire/question/")
                .toUriString(), operation, id);
        this.questionGroupId = questionGroupId;
    }

    // TODO toString inc all inherited fields

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.questionGroupId == null) {
                log.error("Required field: questionGroupId");
                return false;
            }
        }

        return valid;
    }

}
