package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.UpdateOutcomeCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import static com.ecco.security.SecurityUtil.getUser;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * @deprecated in favour of OutcomeCommandViewModel - see ConfigController
 */
@Deprecated
@RestController
@RequestMapping(produces = APPLICATION_JSON_VALUE)
public class OutcomeCommandController extends BaseWebApiController {

    @NonNull
    private final OutcomeRepository outcomeRepository;

    @NonNull
    private final ConfigCommandRepository configCommandRepository;

    @NonNull
    private final ObjectMapper objectMapper;

    @NonNull
    private final CacheManager cacheManager;

    @Autowired
    public OutcomeCommandController(@NonNull OutcomeRepository outcomeRepository,
            @NonNull ConfigCommandRepository configCommandRepository,
            @NonNull ObjectMapper objectMapper,
            @NonNull CacheManager cacheManager) {
        this.outcomeRepository = outcomeRepository;
        this.configCommandRepository = configCommandRepository;
        this.objectMapper = objectMapper;
        this.cacheManager = cacheManager;
    }

    /** Allow commands to be posted */
    @RequestMapping(value = "/service-config/outcomes/{outcomeId}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Result updateAction(
            @NonNull Authentication authentication,
            @PathVariable long outcomeId,
            @NonNull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        StringChangeCommandViewModel viewModel = objectMapper.readValue(requestBody, StringChangeCommandViewModel.class);

        ConfigCommand duplicate = configCommandRepository.findOneByUuid(viewModel.uuid);

        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }

        long userId = getUser(authentication).getId();
        UpdateOutcomeCommand savedCommand = configCommandRepository.save(
                new UpdateOutcomeCommand(
                        viewModel.uuid,
                        viewModel.timestamp,
                        userId,
                        requestBody));

        if (viewModel.nameChange != null) {
            outcomeRepository.changeName(viewModel.nameChange.to, outcomeId);
        }

        clearServiceTypeCache();

        return new Result(Result.COMMAND_APPLIED, savedCommand.getId());
    }

    private void clearServiceTypeCache() {
        // This could be done as a more specific caching change, but this will be fine as this is rarely used
        cacheManager.getCache("serviceTypeViewModel").clear();
    }
}
