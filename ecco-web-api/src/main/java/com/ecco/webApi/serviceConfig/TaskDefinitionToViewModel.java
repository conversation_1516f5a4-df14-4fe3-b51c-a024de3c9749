package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.dom.TaskDefinition;
import java.util.function.Function;

public class TaskDefinitionToViewModel implements Function<TaskDefinition, TaskDefinitionViewModel> {

    @Override
    public TaskDefinitionViewModel apply(TaskDefinition input) {
        TaskDefinitionViewModel result = new TaskDefinitionViewModel();
        result.id = input.getId();
        result.name = input.getName();
        result.type = input.getType().name();
        result.description = input.getDescription();
        result.display = input.isDisplay();
        result.displayOverview = input.isDisplayOverview();
        result.metadata = input.getMetadata();

        return result;
    }

}
