package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.UpdateActionDefCommand;
import com.ecco.serviceConfig.dom.UpdateActionDefLinkedActivitiesCommand;
import com.ecco.serviceConfig.repositories.ActionDefRepository;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.viewModel.ActionToViewModel;
import com.ecco.serviceConfig.viewModel.ActionViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import org.jspecify.annotations.NonNull;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.stream.StreamSupport;

import static com.ecco.security.SecurityUtil.getUser;
import static java.util.stream.Collectors.toList;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@RestController
@RequestMapping(produces = APPLICATION_JSON_VALUE)
public class ActionDefController extends BaseWebApiController {

    @NonNull
    private final ActionDefRepository actionDefRepository;

    @NonNull
    private final ConfigCommandRepository configCommandRepository;

    @NonNull
    private final ObjectMapper objectMapper;

    @NonNull
    private final CacheManager cacheManager;

    @NonNull
    private final ActionToViewModel actionToViewModel = new ActionToViewModel();

    @Autowired
    public ActionDefController(@NonNull ActionDefRepository actionDefRepository,
            @NonNull ConfigCommandRepository configCommandRepository,
            @NonNull ObjectMapper objectMapper,
            @NonNull CacheManager cacheManager) {
        this.actionDefRepository = actionDefRepository;
        this.configCommandRepository = configCommandRepository;
        this.objectMapper = objectMapper;
        this.cacheManager = cacheManager;
    }


    @RequestMapping(value = "/actionDefs/byId/{id}", method = GET)
    public ActionViewModel findOne(@PathVariable long id) {
        return actionToViewModel.apply(actionDefRepository.findById(id).orElse(null));
    }

    @GetJson("/actionDefs/{uuid}")
    public ActionViewModel findOne(@PathVariable UUID uuid) {
        return actionToViewModel.apply(actionDefRepository.findOneByUuid(uuid));
    }

    @RequestMapping(value = "/actionDefs/", method = GET)
    public List<ActionViewModel> findAll() {
        return StreamSupport.stream(actionDefRepository.findAll().spliterator(), false).map(actionToViewModel).collect(toList());
    }


    /** Allow commands to be posted */
    @RequestMapping(value = "/service-config/actionDefs/{actionDefId}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Result updateAction(
            @NonNull Authentication authentication,
            @PathVariable long actionDefId,
            @NonNull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        StringChangeCommandViewModel viewModel = objectMapper.readValue(requestBody, StringChangeCommandViewModel.class);

        ConfigCommand duplicate = configCommandRepository.findOneByUuid(viewModel.uuid);

        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }

        long userId = getUser(authentication).getId();
        UpdateActionDefCommand savedCommand = configCommandRepository.save(
                new UpdateActionDefCommand(
                        viewModel.uuid,
                        viewModel.timestamp,
                        userId,
                        requestBody));

        if (viewModel.nameChange != null) {
            actionDefRepository.changeName(viewModel.nameChange.to, actionDefId);
        }

        clearServiceTypeCache();

        return new Result(Result.COMMAND_APPLIED, savedCommand.getId());
    }

    /** Allow commands to be posted */
    @RequestMapping(value = "/service-config/{serviceId}/actionDefs/{actionDefId}/linkedActivities/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Result updateLinkedActivities(
            @NonNull Authentication authentication,
            @PathVariable long serviceId,
            @PathVariable long actionDefId,
            @NonNull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        ActionDefActivityAssociationChangeViewModel viewModel = objectMapper.readValue(requestBody, ActionDefActivityAssociationChangeViewModel.class);

        ConfigCommand duplicate = configCommandRepository.findOneByUuid(viewModel.uuid);

        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }

        long userId = getUser(authentication).getId();
        UpdateActionDefLinkedActivitiesCommand savedCommand = configCommandRepository.save(
                new UpdateActionDefLinkedActivitiesCommand(
                        viewModel.uuid,
                        viewModel.timestamp,
                        userId,
                        requestBody));

        updateLinkedActivity(actionDefId, viewModel);

        clearServiceTypeCache();
        return new Result(Result.COMMAND_APPLIED, savedCommand.getId());
    }

    private void updateLinkedActivity(long actionDefId, ActionDefActivityAssociationChangeViewModel viewModel) {
        switch (viewModel.operation) {
            case ActionDefActivityAssociationChangeViewModel.OPERATION_ADD:
                linkActivity(actionDefId, viewModel.activityTypeId);
                break;
            case ActionDefActivityAssociationChangeViewModel.OPERATION_REMOVE:
                unlinkActivity(actionDefId, viewModel.activityTypeId);
                break;
            default:
                throw new UnsupportedOperationException("operation value not supported:" + viewModel.operation);
        }
    }


    private void linkActivity(long actionDefId, long activityTypeId) {
        // delete first to avoid atomic upsert issue (i.e. someone else might have created
        actionDefRepository.unlinkActivity(actionDefId, activityTypeId);
        actionDefRepository.linkActivity(actionDefId, activityTypeId);
    }

    private void unlinkActivity(long actionDefId, long activityTypeId) {
        actionDefRepository.unlinkActivity(actionDefId, activityTypeId);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseStatus(HttpStatus.OK)
    public Result delete(@PathVariable long id) {
        actionDefRepository.deleteById(id);
        return new Result("deleted", id);
    }

    private void clearServiceTypeCache() {
        // This could be done as a more specific caching change, but this will be fine as this is rarely used
        cacheManager.getCache("serviceTypeViewModel").clear();
    }
}
