package com.ecco.webApi.serviceConfig;

import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.viewModel.IdNameWithServicesViewModel;

/**
 * Data-transfer object representing an Activity Type.
 * <p/>
 * An Activity Type is a class of activity for which events may be organised.
 * <p/>
 * For example, "Yoga".
 */
public class ActivityTypeViewModel extends IdNameWithServicesViewModel {

    @Nullable
    public Integer numReferrals;
}
