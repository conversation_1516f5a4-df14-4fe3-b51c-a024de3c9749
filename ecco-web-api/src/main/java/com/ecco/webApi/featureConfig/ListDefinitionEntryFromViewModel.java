package com.ecco.webApi.featureConfig;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import java.util.function.Function;

import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.List;

final class ListDefinitionEntryFromViewModel implements Function<ListDefinitionEntryViewModel, ListDefinitionEntry> {

    private final ListDefinitionRepository listDefinitionRepository;
    ListDefinitionEntryFromViewModel(ListDefinitionRepository listDefinitionRepository) {
        this.listDefinitionRepository = listDefinitionRepository;
    }

    @Nullable
    @Override
    public ListDefinitionEntry apply(@Nullable ListDefinitionEntryViewModel input) {
        if (input == null) {
            throw new NullPointerException("input ListDefinitionEntry must not be null");
        }

        ListDefinitionEntry ld = new ListDefinitionEntry();
        ld.setId(input.id);
        ld.setBusinessKey(input.getBusinessKey());
        ld.setListName(input.listName);
        ld.setName(input.name);
        ld.setDefault(input.defaulted);
        ld.setOrder(input.order);
        ld.setDisabled(input.disabled);

        ld.setParentId(getParentId(input));

        return ld;
    }

    private Integer getParentId(ListDefinitionEntryViewModel input) {

        if (StringUtils.hasText(input.parentListName) && StringUtils.hasText(input.parentItemName)) {
            List<ListDefinitionEntry> nonUniqueParents = listDefinitionRepository.findByListNameAndName(input.parentListName, input.parentItemName);
            Assert.notEmpty(nonUniqueParents,
                    "parent listName and item name not found: " + input.parentListName + ", " + input.parentItemName);
            Assert.isTrue(nonUniqueParents.size() == 1,
                    "parent listName and item name found twice: " + input.parentListName + ", " + input.parentItemName);
            return nonUniqueParents.get(0).getId();
        }
        return null;
    }

}
