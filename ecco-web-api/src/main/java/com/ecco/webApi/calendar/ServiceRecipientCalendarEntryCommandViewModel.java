package com.ecco.webApi.calendar;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.UUID;

/**
 * View model, to capture a new/update/delete request to a calendar entry.
 */
@Getter
@Setter
@Slf4j
@NoArgsConstructor // only for Cglib/Hibernate etc
public class ServiceRecipientCalendarEntryCommandViewModel extends BaseServiceRecipientCommandViewModel implements CalendarEntryCommandViewModelParent {

    @NonNull
    public CalendarEntryCommandViewModel calendarEntryViewModel;

    /* constructor for tests only - <PERSON> creates the full object from JSON - this omits things that a
     * proper command handler will require, such as initialising the UUID etc via super(uuid,...) */
    public ServiceRecipientCalendarEntryCommandViewModel(int ownerServiceRecipientId,
                                                         @NonNull CalendarEntryCommandViewModel calendarEntryCommandViewModel) {
        super(UriComponentsBuilder
                .fromUriString("service-recipient/{serviceRecipientId}/calendar-entry/command/")
                .buildAndExpand(ownerServiceRecipientId)
                .toUriString(),
                ownerServiceRecipientId);
        this.calendarEntryViewModel = calendarEntryCommandViewModel;
    }

    public boolean hasChanges() {
        return calendarEntryViewModel.hasChanges();
    }

    public boolean valid() {
        return super.valid() && calendarEntryViewModel.valid();
    }

    @NonNull
    @Override
    public UUID getCommandUuid() {
        return uuid;
    }

    @NonNull
    @Override
    public CalendarEntryCommandViewModel getCalendarEntryViewModel() {
        return this.calendarEntryViewModel;
    }

    @JsonIgnore
    @NonNull
    @Override
    public BaseCommandViewModel getCalendarEntryViewModelParent() {
        return this;
    }
}
