package com.ecco.webApi.buildings;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public class AddressLocationChangeCommandViewModel extends BaseCommandViewModel {

    @Nullable
    public Integer addressLocationId;

    @Nonnull
    public String operation;

    @Nullable
    public ChangeViewModel<Boolean> disabled;


    AddressLocationChangeCommandViewModel() {
        super(UriComponentsBuilder
                .fromUriString("buildings/address-location/commands/")
                .toUriString());
    }

    // Mirror buildings/commands.ts
    public AddressLocationChangeCommandViewModel(String operation, Integer addressLocationId) {
        this();
        this.operation = operation;
        this.addressLocationId = addressLocationId;
    }

}
