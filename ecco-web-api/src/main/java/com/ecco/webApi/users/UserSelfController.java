package com.ecco.webApi.users;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.security.SecurityUtil;
import com.ecco.security.config.AclConfig;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.webApi.viewModels.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@Secured("ROLE_USER")
@RestController
public class UserSelfController extends UserBaseController {

    @Autowired
    public UserSelfController(UserManagementService userManagementService,
                              WorkerRepository workerRepository,
                              IndividualRepository individualRepository,
                              AddressRepository addressRepository, AclConfig aclConfig) {
        super(userManagementService, workerRepository, individualRepository, addressRepository, aclConfig);
    }

    @PostJsonReturningJson("/self/users/{username}/updateMyPassword/")
    public Result updateMyUserPassword(
            @PathVariable String username,
            @RequestBody ChangePasswordDto dto) {

        String usernameLoggedIn = SecurityUtil.getAuthenticatedUsername();
        if (!usernameLoggedIn.equals(username)) {
            throw new IllegalArgumentException("mismatched usernames");
        }

        return updatePassword(username, dto, true);
    }

}
