package com.ecco.webApi.users;

import org.jspecify.annotations.NonNull;

import com.ecco.dom.Individual;
import com.ecco.security.dom.User;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.webApi.contacts.IndividualFromViewModel;
import com.ecco.webApi.viewModels.UserViewModel;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.Nullable;

@RequiredArgsConstructor
public class UserFromViewModel implements Function<UserViewModel, User> {

    private final IndividualFromViewModel individualFromViewModel;

    @Override
    @NonNull
    public User apply(@Nullable UserViewModel input) {
        if (input == null) {
            throw new NullPointerException("input UserViewModel must not be null");
        }

        // sort the individual details first
        Individual i = new Individual();
        if (input.individual != null)
            i = individualFromViewModel.apply(input.individual);

        // create the user
        User user = UserManagementServiceImpl.createDefaultUser();
        user.setUsername(input.username);
        user.setNewPassword(input.newPassword);
        user.setEnabled(input.isEnabled());
        user.setContact(i);
        user.setId(input.userId);

        // when saving, the userManagementServiceImpl looks up each group and expects unique result
        // so its not essential to ensure the group is valid
        user.setNewGroups(input.groups);

        return user;
    }

}
