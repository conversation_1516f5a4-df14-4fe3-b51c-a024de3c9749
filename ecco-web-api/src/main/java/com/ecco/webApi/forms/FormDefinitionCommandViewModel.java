package com.ecco.webApi.forms;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.UUID;

import org.jspecify.annotations.Nullable;

@Slf4j
public class FormDefinitionCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public String operation;

    @NonNull
    public UUID formDefUuid;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<Number> orderby;

    @Nullable
    public ChangeViewModel<Boolean> disabled;

    @Nullable
    public ChangeViewModel<String> definition;

    @Deprecated
    public FormDefinitionCommandViewModel() {
        super();
    }

    // for testing
    public FormDefinitionCommandViewModel(@NonNull String operation, @NonNull UUID formDefUuid) {
        super(UriComponentsBuilder
                .fromUriString("formDef/")
                .toUriString());
        this.operation = operation;
        this.formDefUuid = formDefUuid;
    }

    public boolean valid() {
        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.name == null) {
                log.error("Required field: name");
                return false;
            }
            if (this.orderby == null) {
                log.error("Required field: orderby");
                return false;
            }
            if (this.definition == null) {
                log.error("Required field: definition");
                return false;
            }
        }

        return valid;
    }

}
