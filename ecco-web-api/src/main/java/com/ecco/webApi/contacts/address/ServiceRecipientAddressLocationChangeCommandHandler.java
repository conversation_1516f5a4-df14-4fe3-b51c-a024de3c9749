package com.ecco.webApi.contacts.address;

import com.ecco.buildings.dom.BuildingServiceRecipient;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.contacts.dao.AddressHistoryRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.AddressedLocation;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.Individual;
import com.ecco.dom.commands.ServiceRecipientAddressLocationChangeCommand;
import com.ecco.dom.contacts.AddressHistory;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.service.ContactService;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.singleValue.HistoryItemCommandSupport;
import com.ecco.webApi.singleValue.HistoryItemCommandSupportHandler;
import com.ecco.webApi.singleValue.HistoryItemCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class ServiceRecipientAddressLocationChangeCommandHandler
        extends ServiceRecipientCommandHandler<ServiceRecipientAddressLocationChangeCommandViewModel, ServiceRecipientAddressLocationChangeCommand, @NonNull Integer>
        implements HistoryItemCommandSupport<AddressHistory, ServiceRecipientAddressLocationChangeCommandViewModel> {

    @NonNull
    private final ContactService contactService;

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final AddressRepository addressRepository;

    @NonNull
    private final AddressHistoryRepository addressHistoryRepository;

    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;

    @NonNull
    private final FixedContainerRepository buildingRepository;

    @NonNull
    private final ServiceRecipientSummaryService serviceRecipientSummaryService;

    @NonNull
    private final HistoryItemCommandSupportHandler<AddressHistory, ServiceRecipientAddressLocationChangeCommandViewModel> support;

    @PersistenceContext
    protected EntityManager entityManager;

    public ServiceRecipientAddressLocationChangeCommandHandler(@NonNull ObjectMapper objectMapper,
                                                               @NonNull ServiceRecipientCommandRepository commandRepository,
                                                               @NonNull ContactService contactService,
                                                               @NonNull AddressRepository addressRepository,
                                                               @NonNull AddressHistoryRepository addressHistoryRepository,
                                                               @NonNull ClientRepository clientRepository,
                                                               @NonNull ServiceRecipientRepository serviceRecipientRepository,
                                                               @NonNull FixedContainerRepository buildingRepository,
                                                               @NonNull ServiceRecipientSummaryService serviceRecipientSummaryService) {
        super(objectMapper, commandRepository, ServiceRecipientAddressLocationChangeCommandViewModel.class);
        this.contactService = contactService;
        this.addressRepository = addressRepository;
        this.addressHistoryRepository = addressHistoryRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.buildingRepository = buildingRepository;
        this.clientRepository = clientRepository;
        this.serviceRecipientSummaryService = serviceRecipientSummaryService;
        this.support = new HistoryItemCommandSupportHandler<>(addressHistoryRepository, this);
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull Integer params, @NonNull ServiceRecipientAddressLocationChangeCommandViewModel viewModel) {

        // for each operation there we need to verify valid dates so we load once here
        // we make use of the unique constraint on the table
        // so we can use the dates to locate correct values
        assert viewModel.serviceRecipientId != null;

        var addressHistories = getAddressHistories(viewModel);

        support.handleHistoryCommand(viewModel, addressHistories);

        ensureLatestHistoryIsActive(viewModel);

        return null;
    }

    private List<AddressHistory> getAddressHistories(@NonNull ServiceRecipientAddressLocationChangeCommandViewModel viewModel) {
        return viewModel.contactId != null
                ? addressHistoryRepository.findByContactIdOrderByValidFromDesc(viewModel.contactId)
                : addressHistoryRepository.findByServiceRecipientIdOrderByValidFromDesc(viewModel.serviceRecipientId);
    }

    @Override
    public void applyChanges(AddressHistory entry, ServiceRecipientAddressLocationChangeCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.buildingLocation != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.buildingLocation, entry.getBuildingLocationId(), "buildingLocationId");
                entry.setBuildingLocationId(cmdVM.buildingLocation.to);
            }
            if (cmdVM.addressLocation != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.addressLocation, entry.getAddressLocationId(), "addressLocationId");
                entry.setAddressLocationId(cmdVM.addressLocation.to);
            }
        }
    }

    @Override
    public AddressHistory createItem(ServiceRecipientAddressLocationChangeCommandViewModel cmdVM) {
        assert cmdVM.serviceRecipientId != null;
        assert cmdVM.validFrom != null;
        var cId = cmdVM.contactId != null ? cmdVM.contactId.longValue() : null;
        return new AddressHistory(cmdVM.serviceRecipientId, cmdVM.validFrom.to, cId);
    }

    @Override
    public void warnIfPrevValueDoesntMatchDelegate(ServiceRecipientAddressLocationChangeCommandViewModel cmdVM, ChangeViewModel<LocalDateTime> change, LocalDateTime from, String fieldName) {
        warnIfPrevValueDoesntMatch(cmdVM, change, from, fieldName);
    }

    private void ensureLatestHistoryIsActive(ServiceRecipientAddressLocationChangeCommandViewModel viewModel) {
        assert viewModel.serviceRecipientId != null;
        var addressHistories = getAddressHistories(viewModel);

        var recipient = serviceRecipientRepository.findById(viewModel.serviceRecipientId)
                .orElseThrow(() -> new NotFoundException(viewModel.serviceRecipientId));
        var hasContact = recipient.getContact() != null;

        // if we are deleting, we can end up with no addressHistories - so allow null
        var adrHistory = addressHistories.stream().findFirst().orElse(null);
        // directly on the service recipient, this path is only for buildings currently
        if (!hasContact) {
            FixedContainer building = ((BuildingServiceRecipient) recipient).getBuilding();
            buildingRepository.updateBuildingLocation(building.getId(), adrHistory != null ? adrHistory.getAddressLocationId() : null);
        }
        else {

            @Nullable ClientDetail client = clientRepository.findOneByServiceRecipientId(recipient.getId());

            // SPECIAL CASE since we've added history retrospectively to clients, so the first entry is likely missing in the history
            // check for size 1 because we've already inserted the new history item if we are an add operation
            if (HistoryItemCommandViewModel.OPERATION_ADD.equals(viewModel.operation) && addressHistories.size() == 1) {
                makeupFirstEntry(viewModel, adrHistory, recipient.getContact());
            }

            // update the contact record with the appropriate address
            updateContactAddress(recipient.getContact(), adrHistory);
            if (client != null) {
                // update the residingAt (residenceId) on the client
                // if the incoming address was actually from a building '@'
                updateClientResidingAt(client, adrHistory);
            }
        }

        serviceRecipientSummaryService.invalidateCacheFor(recipient.getId());
    }

    /**
     * Create a first entry, since we've added history retrospectively to clients, so the first entry will be missing in the history
     */
    private void makeupFirstEntry(ServiceRecipientAddressLocationChangeCommandViewModel viewModel, AddressHistory adrHistory, Individual contact) {

        // check there is an address to insert
        if (contact.getAddressedLocation() == null && contact.getAddress() == null) {
            return;
        }

        var firstEntry = new AddressHistory(viewModel.serviceRecipientId, LocalDate.EPOCH.atStartOfDay(), contact.getId());
        firstEntry.setValidTo(adrHistory.getValidFrom());

        if (contact.getAddressedLocation() != null) {
            var addressedLocation = addressRepository.findById(contact.getAddressedLocation().getId()).orElseThrow(NullPointerException::new);
            firstEntry.setAddressLocationId(addressedLocation.getId());
        } else {
            // create address, if doesn't exist
            var adr = contact.getAddress();

            // NB If the old address is too large for a field we get an error - we could auto trim it but that may cause clashes
            AddressedLocation addressConv = new AddressedLocationFromViewModel().apply(AddressViewModel.fromAddress(adr));
            AddressedLocation found = addressRepository.findOneByPostCodeAndLine1(addressConv.getPostCode(), addressConv.getLine1());
            if (found != null) {
                firstEntry.setAddressLocationId(found.getId());
                if (found.isDisabled()) {
                    found.setDisabled(false);
                    addressRepository.save(found);
                }
            } else {
                addressConv = addressRepository.save(addressConv);
                firstEntry.setAddressLocationId(addressConv.getId());
            }
        }

        addressHistoryRepository.save(firstEntry);
    }

    private void updateContactAddress(Individual contact, @Nullable AddressHistory adr) {
        AddressedLocation addressedLocation = getAddressedLocationFromBldgOrAdr(adr);
        // NB this may have been to do with LocationSyncEvent
        entityManager.flush();
        entityManager.clear();
        contactService.updateAddress(contact.getId(), addressedLocation);
        entityManager.flush();
    }

    private AddressedLocation getAddressedLocationFromBldgOrAdr(@Nullable AddressHistory adr) {
        if (adr == null) {
            return null;
        }
        AddressedLocation addressedLocation = null;
        if (adr.getBuildingLocationId() != null) {
            FixedContainer bldg = buildingRepository.findById(adr.getBuildingLocationId()).orElseThrow(NullPointerException::new);
            if (bldg.getLocation() != null) {
                addressedLocation = addressRepository.findById(bldg.getLocationId()).orElseThrow(NullPointerException::new);
            }
        } else {
            if (adr.getAddressLocationId() != null) {
                addressedLocation = addressRepository.findById(adr.getAddressLocationId()).orElseThrow(NullPointerException::new);
            }
        }
        return addressedLocation;
    }

    private void updateClientResidingAt(ClientDetail client, @Nullable AddressHistory adr) {
        Integer bldgId = adr != null && adr.getBuildingLocationId() != null
                ? buildingRepository.findById(adr.getBuildingLocationId()).orElseThrow().getId()
                : null;

        // set the building
        clientRepository.updateResidingAt(client.getId(), bldgId);
    }

    @NonNull
    @Override
    protected ServiceRecipientAddressLocationChangeCommand createCommand(Serializable targetId, @NonNull Integer params, @NonNull String requestBody,
                                                                         @NonNull ServiceRecipientAddressLocationChangeCommandViewModel viewModel,
                                                                         long userId) {
        return new ServiceRecipientAddressLocationChangeCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params);
    }

}
