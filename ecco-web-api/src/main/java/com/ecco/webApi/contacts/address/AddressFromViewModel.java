package com.ecco.webApi.contacts.address;

import java.util.function.Function;

import org.jspecify.annotations.Nullable;

import com.ecco.dom.contacts.Address;

public final class AddressFromViewModel implements Function<AddressViewModel, Address> {

    @Nullable
    @Override
    public Address apply(@Nullable AddressViewModel input) {
        if (input == null) return null;

        Address address = new Address();
        if (input.address != null && input.address.length >= 1) {
            address.setLine1(input.address[0]);
            if (input.address.length >= 2) {
                address.setLine2(input.address[1]);
            }
            if (input.address.length >= 3) {
                address.setLine3(input.address[2]);
            }
        }
        address.setTown(input.town);
        address.setCounty(input.county);
        address.setPostCode(input.postcode);

        return address;
    }
}
