package com.ecco.webApi.contacts;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dto.ClientDefinition;
import org.jspecify.annotations.Nullable;

import java.util.function.Function;

/**
 * Takes a ClientDefinition (involved in communicating with external systems) and converts to a ClientViewModel for displaying in ECCO
 */
public class StaffDefinitionToViewModel extends PersonDefinitionToViewModel implements Function<ClientDefinition, WorkerViewModel> {

    public StaffDefinitionToViewModel(ListDefinitionRepository listDefinitionRepository) {
        super(listDefinitionRepository);
    }

    @Nullable
    @Override
    public WorkerViewModel apply(@Nullable ClientDefinition input) { // NOTE: We're using ClientDefinition as a superset of both
        if (input == null) {
            return null;
        }
        final WorkerViewModel viewModel = new WorkerViewModel();
        viewModel.setWorkerId(input.getLocalClientId());
        viewModel.setCode(input.getCode());
        applyPersonFields(input, viewModel);
        viewModel.setPrimaryLocationId(input.getAssignedLocationId());
        viewModel.setPrimaryLocationName(input.getAssignedLocationName());

        return viewModel;
    }

}
