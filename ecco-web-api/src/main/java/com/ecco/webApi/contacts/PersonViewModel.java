package com.ecco.webApi.contacts;

import java.util.HashMap;

import com.ecco.dom.ClientDetailAbstract;
import com.ecco.webApi.contacts.address.AddressViewModel;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.LocalDate;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

/**
 * Data-transfer object representing a person.
 * <p/>
 * This is a base class for specific classes of person, such as
 * {@link ClientViewModel} and {@link WorkerViewModel}.
 */
// Property accessors are unfortunately required for com.ecco.data.client.dataimport.csv.CSVBeanReader.
// See ECCO-703
@Getter
@Setter
public abstract class PersonViewModel {

    /**
     * Return firstname lastname formatted name with null handling (returns empty string if both are null)
     */
    @NonNull
    static public String formatDisplayName(String firstName, String lastName) {
        if (firstName == null) {
            return lastName == null ? "" : lastName; // i.e. lastname or null
        }
        if (lastName == null) {
            return firstName;
        }
        return firstName + " " + lastName;
    }

    /**
     * The client's ID in an external system (e.g. Northgate).
     * <p/>
     * The external system that this ID belongs to is identified by
     * {@link #externalSystemSource}.
     */
    public String externalSystemRef;

    /**
     * The external system that owns the ID specified by
     * {@link #externalSystemRef}.
     * <p/>
     * This should match an {@link com.ecco.config.dom.ExternalSystem} entity.
     */
    public String externalSystemSource;


    public Long avatarId;

    /**
     * The calendarId of the person - if any
     */
    public String calendarId;

    /**
     * The title of the client - Mr Mrs etc
     */
    public String title;

    /**
     * The first name of the client as displayed in the user interface.
     */
    public String firstName;

    /**
     * The last name of the client as displayed in the user interface.
     */
    public String lastName;

    /** Preferred name to be used. Null if not specified */
    @Nullable
    public String knownAs;

    /** Id of the ListDefinitionEntry of the pronouns to be used. Null if not specified */
    @Nullable
    public Integer pronounsId;

    /**
     * The client's date of birth, or null if not known.
     */
    public LocalDate birthDate;

    /** The person's date of death in ISO 8601 extended format (YYYY-MM-DD) */
    public LocalDate dateOfDeath;

    /**
     * The client's age (as of now or at death - {@link ClientDetailAbstract#getAge()}) in years, or null if the client's age is not known.
     */
    public Integer age;

    /**
     * The client's gender, or null if the client's gender is not known.
     * NB ClientViewModel extends PersonViewModel - and both are used as 2-way communication, and with external systems.
     * The only 2-way accurate approach is to use the id of the list-def entry.
     * If a gender needs creating (eg data-import) or matching, then this should be done in advance of this step.
     */
    public Integer genderId;

    public Integer genderAtBirthId;

    /**
     * The client's disability, or null.
     * NB ClientViewModel extends PersonViewModel - and both are used as 2-way communication, and with external systems.
     * The only 2-way accurate approach is to use the id of the list-def entry.
     * If a disability needs creating (eg data-import) or matching, then this should be done in advance of this step.
     */
    public Integer disabilityId;

    /**
     * Ethnic origin. Should match an entry in Settings -> Lists -> Ethnic Origins
     */
    public Integer ethnicOriginId;

    /**
     * The client's nationality, or null.
     * NB ClientViewModel extends PersonViewModel - and both are used as 2-way communication, and with external systems.
     * The only 2-way accurate approach is to use the id of the list-def entry.
     * If an entry needs creating (eg data-import) or matching, then this should be done in advance of this step.
     */
    public Integer nationalityId;

    /**
     * The client's marital stats, or null.
     * NB this is sometimes recorded on the referral instead, since changes to this can impact relevant reports for the industry
     * NB ClientViewModel extends PersonViewModel - and both are used as 2-way communication, and with external systems.
     * The only 2-way accurate approach is to use the id of the list-def entry.
     * If an entry needs creating (eg data-import) or matching, then this should be done in advance of this step.
     */
    public Integer maritalStatusId;

    /**
     * Religion. Should match an entry in Settings -> Lists -> Religions
     */
    public Integer religionId;

    /**
     * First langauge. Should match an entry in Settings -> Lists -> Languages
     */
    public Integer firstLanguageId;

    /**
     * The client's sexual orientation, or null.
     * NB ClientViewModel extends PersonViewModel - and both are used as 2-way communication, and with external systems.
     * The only 2-way accurate approach is to use the id of the list-def entry.
     * If a disability needs creating (eg data-import) or matching, then this should be done in advance of this step.
     */
    public Integer sexualOrientationId;

    /**
     * The client's postal address, or null if the client's postal address is
     * not known.
     */
    @Nullable
    public AddressViewModel address;

    public Integer addressedLocationId;

    /**
     * ermm, phone number
     */
    public String phoneNumber;

    public String mobileNumber;

    public String email;

    public String preferredContactMethod; //  as per IndividualViewModel

    /**
     * Map of string values we want as key:value pairs available for viewing and editing
     */
    public HashMap<String, String> textMap = new HashMap<String, String>();

    /**
     * Map of dd/MM/yyyy dates we want as key:value pairs available for viewing and editing
     */
    public HashMap<String, String> dateMap = new HashMap<String, String>();

}
