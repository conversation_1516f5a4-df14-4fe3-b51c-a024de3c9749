package com.ecco.webApi.contacts.address;

import com.ecco.dom.contacts.AddressHistory;

import javax.annotation.Nullable;
import java.util.function.Function;
import java.util.function.Supplier;

public abstract class BaseHistoryToViewModel<T extends AddressHistoryViewModel> implements Function<AddressHistory, T> {

    private final Supplier<T> viewModelSupplier;

    protected BaseHistoryToViewModel(Supplier<T> viewModelSupplier) {
        this.viewModelSupplier = viewModelSupplier;
    }

    @Override
    public T apply(@Nullable AddressHistory input) {
        if (input == null) {
            throw new NullPointerException("input AddressHistory must not be null");
        }

        T result = viewModelSupplier.get();
        result.id = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.validFrom = input.getValidFrom();
        result.validTo = input.getValidTo();

        result.contactId = input.getContactId() != null ? input.getContactId().intValue() : null;
        result.buildingId = input.getBuildingLocationId();
        result.addressId = input.getAddressLocationId();

        customise(input, result);

        return result;
    }

    protected abstract void customise(AddressHistory input, T result);
}
