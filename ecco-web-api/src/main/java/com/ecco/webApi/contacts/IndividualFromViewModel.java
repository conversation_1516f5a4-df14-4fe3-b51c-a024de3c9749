package com.ecco.webApi.contacts;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.dom.*;
import com.ecco.webApi.contacts.address.AddressFromViewModel;
import java.util.function.Function;
import com.ecco.dom.contacts.Address;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.Nullable;

@RequiredArgsConstructor
public class IndividualFromViewModel implements Function<IndividualViewModel, Individual> {

    private final AddressRepository addressRepository;

    @Override
    @Nullable
    public Individual apply(@Nullable IndividualViewModel input) {
        if (input == null) {
            throw new NullPointerException("input IndividualViewModel must not be null");
        }

        Individual i = new Individual();
        IndividualFromViewModel.apply(this.addressRepository, i, input);
        return i;
    }

    public static void apply(AddressRepository addressRepository, Individual result, IndividualViewModel input) {

        result.setCode(input.code);
        result.setTitle(input.title);
        result.setFirstName(input.firstName);
        result.setLastName(input.lastName);
        if (input.address != null) {
            AddressFromViewModel addressFromViewModel = new AddressFromViewModel();
            result.setAddress(addressFromViewModel.apply(input.address));
        }

        if (input.addressedLocationId != null) {
            // for consistency, be sure that the display address is overridden - see ServiceRecipientAddressLocationChangeCommandHandler
            AddressedLocation addressedLocation = addressRepository.findById(input.addressedLocationId).orElseThrow(NullPointerException::new);
            result.setAddressedLocation(addressedLocation);
            result.setAddress(Address.from(addressedLocation)); // see ContactImpl.AddressLike
        }

        result.setJobTitle(input.jobTitle);

        result.setPhoneNumber(input.phoneNumber);
        result.setMobileNumber(input.mobileNumber);
        result.setEmail(StringUtils.trimToNull(input.getEmail()));
        result.setPreferredContactMethod(input.getPreferredContactMethod());
        result.setArchived(input.getArchived());

        if (input.organisationId != null) {
            Agency agency = new Agency();
            agency.setId(input.organisationId);
            result.setCompany(agency);
        }

    }
}
