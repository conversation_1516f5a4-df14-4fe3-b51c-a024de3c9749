package com.ecco.webApi.managedvoids;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoMessageUtils;
import com.ecco.servicerecipient.AcceptState;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.webApi.contacts.AgencyToViewModel;
import com.ecco.webApi.viewModels.ServiceRecipientSourceViewModel;

import java.util.function.Function;

public class ManagedVoidToViewModel implements Function<ManagedVoid, ManagedVoidViewModel> {

    private final ListDefinitionRepository listDefinitionRepository;
    private final AgencyToViewModel agencyToViewModel = new AgencyToViewModel();

    public ManagedVoidToViewModel(ListDefinitionRepository listDefinitionRepository) {
        this.listDefinitionRepository = listDefinitionRepository;
    }

    @Override
    public ManagedVoidViewModel apply(ManagedVoid input) {

        ManagedVoidViewModel result = new ManagedVoidViewModel();
        result.managedVoidId = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        //result.buildingId = input.getBuildingId();
        result.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();

        result.receivedDate = input.getReceivedDateJdk();
        result.displayName = input.getServiceRecipient().getDisplayName();
        // `${l.area} - ${l.code} - ${l.description.substring(0, 30)}`}

        // source
        result.selfReferral = input.isSelfReferral();
        result.referrerAgencyId = input.getReferrerAgency() != null ? input.getReferrerAgency().getId() : null;
        result.referrerIndividualId = input.getReferrerIndividual() != null ? input.getReferrerIndividual().getId() : null;
        ServiceRecipientSourceViewModel.sourceOfReferral(input.isSelfReferral(), input.getReferrerAgency(), input.getReferrerIndividual(), result, this.agencyToViewModel);

        // accept on service
        AcceptState[] states = ServiceRecipientCaseStatusView.Support.acceptedStates(input.isFinalDecision(), null, false, input.isAcceptedOnService());
        //result.appropriateReferralState = states[0];
        result.acceptOnServiceState = states[1];

        result.statusMessageKey = ServiceRecipientCaseStatusView.Support.getStatusMessageKey(input);
        result.statusMessage = EccoMessageUtils.getUiMessageSource().getMessage(result.statusMessageKey);

        result.decisionMadeOn = JodaToJDKAdapters.localDateToJDk(input.getDecisionMadeOn());
        result.signpostedReasonId = input.getSignpostedReasonId();
        result.signpostedBack = input.isSignpostedBack();
        result.signpostedAgencyId = input.getSignpostedAgencyId();
        result.signpostedExitComment = input.getSignpostedExitComment();

        // start
        if (input.getReceivingServiceDate() != null) {
            result.receivingServiceDate = input.getReceivingServiceDate().toLocalDate();
        }
        result.supportWorkerId = input.getSupportWorkerId();
        //result.supportWorkerDisplayName = supportWorker.getDisplayName();

        // exit
        result.exitedDate = input.getExited();
        result.exitReasonId = input.getExitReasonId();

        return result;
    }

}