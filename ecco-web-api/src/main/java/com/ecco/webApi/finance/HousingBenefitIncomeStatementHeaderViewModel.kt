package com.ecco.webApi.finance

import com.ecco.webApi.serialization.LocalDateSerializer
import kotlinx.serialization.Serializable
import java.time.LocalDate

@Serializable
data class HousingBenefitIncomeStatementHeaderViewModel(
    val reference: String?,
    @Serializable(with = LocalDateSerializer::class)
    val issueDate: LocalDate?,
    @Serializable(with = LocalDateSerializer::class)
    val periodStart: LocalDate?,
    @Serializable(with = LocalDateSerializer::class)
    val periodEnd: LocalDate?,
)