package com.ecco.hr.dao;

import com.ecco.dom.hr.WorkerJob;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.jspecify.annotations.NonNull;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

public interface WorkerJobRepository extends QueryDslPredicateAndProjectionExecutor<WorkerJob, Integer> {

    @Query("SELECT w.serviceRecipient.id from WorkerJob w where w.id = ?1")
    int getServiceRecipientId(long workerJobId);

    Optional<WorkerJob> findByServiceRecipient_Id(int serviceRecipientId);

    List<WorkerJob> findAllByServiceRecipient_IdIn(Iterable<Integer> serviceRecipientIds);

    String WORKERJOBS_EMPLOYED_AT = "SELECT workerJob FROM WorkerJob workerJob WHERE"
            + " (workerJob.startDate IS NULL OR workerJob.startDate <= :date)"
            + " AND (workerJob.endDate IS NULL OR workerJob.endDate >= :date)";
    @Query(WORKERJOBS_EMPLOYED_AT)
    Iterable<WorkerJob> findAllWorkerJobsEmployedAt(@Param("date") ZonedDateTime date);

    String WORKERJOBS_EMPLOYED_IN_BLDG = WORKERJOBS_EMPLOYED_AT
            + " AND workerJob.worker.primaryLocation.id = :bldgId";
    @Query(WORKERJOBS_EMPLOYED_IN_BLDG)
    Iterable<WorkerJob> findAllStaffWithPrimaryLocationBuildingIdEmployedAt(@Param("bldgId") int buildingId, @Param("date") ZonedDateTime date);

    String WORKERJOBS_FROM_CONTACT = WORKERJOBS_EMPLOYED_AT
            + " AND workerJob.worker.contact.id in (:individualIds)";
    @Query(WORKERJOBS_FROM_CONTACT)
    Iterable<WorkerJob> findAllStaffWithIndividualIdEmployedAt(@Param("individualIds") @NonNull List<Long> individualIds, @Param("date") ZonedDateTime date);

}
