-- PART ANONYMISE (for test systems)
-- Keep a few letters to allow search testing
-- TODO we should also clear attachments (referrals and evidence)
update contacts set lastname=left(lastname,2) where discriminator_orm='individual';
update setting set keyvalue='COPY SITE' where keyname='SITE_TITLE';

-- select ra.id, ra.filename, ra.upload_size, octet_length(b.bytes) len from svcrec_attachments ra, uploadbytes b where ra.bytesId = b.id;
update contacts set avatarId = null;
delete from svcrec_attachments where id > 0;
delete from supportworkattachments where id > 0;
delete from uploadfile where id > 0;
delete from uploadbytes where id > 0;


-- FULL ANONYMISE
update messages set message='anon org' where msg_key='organisationName';

-- TODO see delete-calendar in data-reset.xml

-- clients & contacts
update contacts set firstname=concat('test-',id);
-- update contacts set lastname='person' || id where discriminator_orm='individual';
update contacts set addressline1='',addressline2='',addressline3='',addresspostcode='TST1 123';
update contacts set email=concat(concat('test',id),'@test.com') where email is not null;
update contacts set mobilenumber='07811223344' where mobilenumber is not null;
update contacts set phonenumber='01234 567890' where phonenumber is not null;
update contacts set birthday=2 where birthday is not null;
update contacts set addressId=null;

update clientdetails set textMap=null;
update clientdetails set NI='', nhs=null militaryNumber=null, birthday=2, mothersFirstName=null, keyCode=null;
update clientdetails set emergencyDetails='emergency details' where emergencyDetails is not null;
update clientdetails set doctorDetails='doctor details' where doctorDetails is not null;
update clientdetails set dentistDetails='dentist details' where dentistDetails is not null;
update clientdetails set medicationDetails='mediation details' where medicationdetails is not null;
update clientdetails set descriptionDetails='description details' where descriptiondetails is not null;
update clientdetails set communicationNeeds='communication needs' where communicationNeeds is not null;
update clientdetails set personality='personality' where personality is not null;
update clientdetails set behaviour='behaviour' where behaviour is not null;
update clientdetails set manners='manners' where manners is not null;
update clientdetails set significantEvents='significant events' where significantEvents is not null;

-- TODO see delete-referrals in data-reset.xml
update referrals set textMap=null, customObjectData=null, comments=null, location=null, interviewDnaComments=null, interviewSetupComments=null, interviewersOther=null;
update referralcomments set bc_comment=concat(id, ': this is a comment');

-- TODO see delete-evidence - see data-reset.xml
update supportplancomments set bc_comment=concat(id, ": some support comment");
update supportthreatcomments set bc_comment=concat(id, ": some risk comment");

-- TODO see data-reset.xml for commands etc...
