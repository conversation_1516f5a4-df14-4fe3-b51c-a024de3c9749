<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>

<%@taglib prefix="bs" tagdir="/WEB-INF/tags/bootstrap" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!-- charts/handler.jsp -->
<page:rich-client-page classes="reports" title="charts"
    importRequireJsModules="bootstrap controls/banner-titlebar reports/controller-page controls/NetworkActivityIndicator"
    importHeadFiles="/WEB-INF/views/heads/dataTablesBootstrapCss.jsp" devMode="${devMode}">
    <page:page-banner>
        <bs:navbar noCollapse="true">
            <ul class="nav navbar-nav navbar-right">

                <%-- hidden charts list, which displays through reports/controller-page when on individual chart --%>
                <c:url var="onlineReportsListUrl" value="/nav/charts/"/>
                <li class="backToParent" style="display: none;"><a href="${onlineReportsListUrl}"><i class="fa fa-bars"></i> reports list</a></li>

                <bs:menu quickGuideId="quickGuide"/>
                <bs:user-dropdown adminMenuText="manage reports"/>
            </ul>
        </bs:navbar>
    </page:page-banner>
    <page:page-titlebar>
    </page:page-titlebar>

    <page:modal title="guide" id="quickGuide">
        <span style="font-weight: bold;">reports list page</span>
        <p>
        This page lists the charts that are currently pre-configured in 'reports list'. Click a specific chart to see it.
        </p>
        <br/>

        <span style="font-weight: bold;">report page</span>
        <p>
        When on an individual report there are descriptions to show what the report is showing. Most reports are configured
        to show a further chart or breakdown table when a section of the chart is selected. The chart will also start
        with some defaults - such as the current quarter (where appropriate) and a 'search criteria'). You can change the quarter by
        clicking 'prev period' and 'next period' and you can change the 'search criteria' by clicking on it.
        </p>
        <br/>

        <span style="font-weight: bold;">report criteria: date</span>
        <p>The report dictates what the date range refers to. This may be shown in the text under the title - such as
        'on referrals this quarter' (which is often the 'received date' from 'details of referal') or 'on support work
        this quarter' (which reports against the 'took place on' date on needs/support screens).
        </p>
        <br/>

        <span style="font-weight: bold;">report criteria: service/project/geo area</span>
        <p>The search criteria can restrict the results to a particular service and/or project and a geographical area
        (where configured). The geographical area can include all within it through the 'inc within' which is on by default.
        </p>
        <br/>

        <span style="font-weight: bold;">report criteria: status group</span>
        <p>A further restriction can be to specify a status group of the referral. The status group can be one of
        the options below:</p>

        <%-- see also menus/referralsList.jsp --%>
        <dl>status group options:
            <dt>received (in the period)</dt>
                <dd>clients that have been received during the period
                </dd>
            <dt>accepted (in the period)</dt>
                <dd>clients that have been '<spring:message code="referralDecision.assessmentAccepted"/>' during the period
                </dd>
            <dt>incomplete (as at date)</dt>
                <dd>all clients where no decision has been made at the date (or now, if no date) regarding
                '<spring:message code="referralDecision.assessmentAccepted"/>'
                <br/>
                <span style="font-style: italic;">
                matches '<spring:message code="status.forAssessment"/>' and '<spring:message code="status.incomplete"/>'
                </span>
                </dd>
            <dt>waiting (as at date)</dt>
                <dd>all clients that have been '<spring:message code="referralDecision.assessmentAccepted"/>'
                but are still waiting at the date (or now, if no date)
                <br/>
                <span style="font-style: italic;">
                matches '<spring:message code="status.toStart"/>' or '<spring:message code="status.toStartAccommodation"/>'
                (where configured)
                </span>
                </dd>
            <dt>live (as at date)</dt>
                <dd>all clients that have been '<spring:message code="referralDecision.assessmentAccepted"/>' at the
                date (or now, if no date)
                <br/>
                <span style="font-style: italic;">
                matches '<spring:message code="status.toStart"/>' or '<spring:message code="status.toStartAccommodation"/>',
                and '<spring:message code="status.started"/>' or '<spring:message code="status.startedAccommodation"/>'
                (where configured)
                </span>
                </dd>
            <dt>live (some point during the period)</dt>
            <dd>all clients that have been '<spring:message code="referralDecision.assessmentAccepted"/>' at some point
                during the period (or now, if no period)
                <br/>
                <span style="font-style: italic;">
                matches '<spring:message code="status.toStart"/>' or '<spring:message code="status.toStartAccommodation"/>',
                and '<spring:message code="status.started"/>' or '<spring:message code="status.startedAccommodation"/>'
                (where configured)
                </span>
            </dd>
            <dt><spring:message code="status.signposted"/> (during the period)</dt>
                <dd>clients that have been <spring:message code="status.signposted"/> during the period
                </dd>
            <dt>exited (in the period)</dt>
                <dd>clients that have been exited during the period
                </dd>
        </dl>

        <span style="font-weight: bold;">report display: 'status now'</span>
        <p>The 'status now' shown in reports or breakdown is the status of the referral as seen today and is more
        detailed than the status group.
        </p>
        <%-- See messages.d.ts. Actually, do we need a list - this should be self-explanatory?
             Perhaps what we need
        <span style="font-style: italic;">status now</span>
        <ul style="padding-left: 10px;">
        <li><span style="font-weight: bold;"><spring:message code="status.forAssessment"/></span>: during the period [default])</li>
        </ul>
        --%>
        <br/>

    </page:modal>

    <div class="report-container"></div>
</page:rich-client-page>
