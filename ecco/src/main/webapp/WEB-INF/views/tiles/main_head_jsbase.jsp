<%-- <%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%> --%>

<%@include file="/WEB-INF/views/pathvars.jsp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<%-- ********************** --%>
<%-- ***   GLOBAL VARS  *** --%>

<script type="text/javascript">
    var sessionLenMins = ${empty sessionLength ? 0 : sessionLength};
    var urlServletBase = "${urlServletBase}";
    <%-- // we can use these to construct links without c:url because they are static resources --%>
    var scripts = "${scriptsBase}";
    var images = "${imagesBase}";
    var css = "${cssBase}";

    // define some controller urls
    var urls = new Array();
    <fmt:message key="noopHidden.url" var="link"/>
    <c:url value="${link}" var="url"/>
    urls["noopHidden.url"] = '${url}';
    <fmt:message key="logoutExpiredSuccess.url" var="logoutExpiredSuccessUrl"/>
    <fmt:message key="logout.url" var="link"/>
    <%-- pre spring 3? logoutSuccessUrl --%>
    <c:url value="${link}" var="url"><c:param name="security-redirect" value="${logoutExpiredSuccessUrl}"/></c:url>
    urls["logout.url"] = '${url}';
</script>
