<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>

<%-- tag files - reusuable jsp, forced in this dir --%>
<%@ taglib prefix="med" tagdir="/WEB-INF/tags" %>

<%-- this is used for collections where we don't use binding but want the path --%>
<%@ taglib prefix="medFn" uri="/WEB-INF/taglibs/medFn.tld" %>
<%@ taglib prefix="eccoFn" uri="/WEB-INF/taglibs/eccoFn.tld" %>

<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>

<%-- hdiv --%>
<%@ taglib prefix="form-hdiv" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="spring-hdiv" uri="http://www.springframework.org/tags" %>

<%@ include file="/WEB-INF/views/pathvars.jsp" %>
