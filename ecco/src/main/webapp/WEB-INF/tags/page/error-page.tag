<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>

<%@taglib prefix="bs" tagdir="/WEB-INF/tags/bootstrap"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>


<page:rich-client-page isTransitionalUI="${true}" useNoCache="${true}" title="${title}" importRequireJsModules="bootstrap" devMode="${false}">
    <c:url var="imagesBase" value="/r/noCache/themes/ecco/images"/>
    <page:logo imagesBase="${imagesBase}"/>
    <div class="error-page rounded">
        <p>
            <span class="rightpoint"><c:out value="there was a problem!"/></span>
        </p>
        <jsp:doBody/>
    </div>
</page:rich-client-page>