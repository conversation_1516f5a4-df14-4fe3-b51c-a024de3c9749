<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<!-- page-menus-bs.tag -->

<%@attribute name="helpId" type="java.lang.String" required="false"
    description="If specified, provides relative URI for help page (e.g. help/rota-overview)"%>

<%@attribute name="welcome" description="set true if welcome page (shows login and not menu)" required="false" type="java.lang.Boolean" %>
<%@attribute name="noCollapse" description="set true for small menus" required="false" type="java.lang.Boolean" %>
<%@attribute name="title" description="title text to show in navbar" required="false" type="java.lang.String" %>
<%@attribute name="quickGuideId" description="id of quick guide element for menu action" required="false" type="java.lang.String" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="ecco" tagdir="/WEB-INF/tags" %>
<%@taglib prefix="bs" tagdir="/WEB-INF/tags/bootstrap" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@taglib prefix="security" uri="http://www.springframework.org/security/tags" %>

<security:authorize var="loggedIn" access="hasAnyRole('ROLE_USER')"/>

<c:url var="baseUrl" value="/nav/secure/welcome.html"/>
<fmt:message var="loginPath" key="login.url"/>
<c:url var="loginUrl" value="${loginPath}"/>
<fmt:message var="logoutPath" key="logout.url"/>
<c:url var="logoutUrl" value="${logoutPath}"/>

<div class="navbar-default">
    <bs:navbar title="${title}" noCollapse="${noCollapse}">
        <ul class="nav navbar-nav">
        </ul>
        <ul class="nav navbar-nav navbar-right">
            <jsp:doBody/>
            <c:if test="${!welcome}">
                <li><a href="${baseUrl}"><i class="fa fa-level-up"></i> menu</a></li>
            </c:if>
            <c:if test="${! empty quickGuideId}">
                <li><a href="#" data-toggle="modal" data-target="#${quickGuideId}"><i class="fa fa-question-circle"></i>
                quick guide</a></li>
            </c:if>
            <c:if test="${empty quickGuideId}">
                <li class="quickGuideMenu" style="display:none;">
                    <a href="#" data-toggle="modal" data-target="#quickGuide"><i class="fa fa-question-circle"></i>
                quick guide</a></li>
                <page:modal title="quick guide" id="quickGuide">
                    <div id="guideBody"></div>
                </page:modal>
                <script>require(["jquery"], function($) {
                    var snippets = $(".quick-guide-snippet");
                    if (snippets.length > 0) {
                        snippets.removeClass("hidden").appendTo($("#guideBody"));
                        $(".quickGuideMenu").show();
                    }
                });
                </script>
            </c:if>
            <c:if test="${! empty helpId}">
                <li><a target="_help" title="open help in another tab" href="<c:url value="/dynamic/help/${helpId}"/>">help</a></li>
            </c:if>
            <ecco:module-enabled module="workflow">
                <c:set var="tasksUrl" value="/dynamic/secure/tasks/my.html"/>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <fmt:message key="tasks"/><span class='tasks-count'></span> <b class="caret"></b>
                    </a>
                    <ul class="dropdown-menu">
                        <div class="notification-tasks" data-tasks-username="${loggedInUsername}"
                            data-tasks-view-url="<c:url value="${tasksUrl}"/>">
                        </div>
                    </ul>
                </li>
            </ecco:module-enabled>
            <bs:user-dropdown noCollapse="${noCollapse}">
                <c:choose>
                    <c:when test="${ambiguous}">
                    </c:when>
                    <c:when test="${!loggedIn}">
                        <li><a href="${loginUrl}">login</a></li>
                    </c:when>
                    <c:when test="${loggedIn && welcome}">
                        <li><a href="${logoutUrl}"><i class="fa fa-sign-out"></i> logout</a></li>
                    </c:when>
                </c:choose>
            </bs:user-dropdown>
        </ul>
    </bs:navbar>
</div>
<ecco:module-enabled module="workflow">
    <script>require(["online/tasks-content"])</script>
</ecco:module-enabled>
