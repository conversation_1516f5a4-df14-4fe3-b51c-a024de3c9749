package com.ecco.web.admin;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.Referral;
import com.ecco.service.ReferralService;
import com.ecco.security.ReferenceDataSource;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/secure/admin/referralFieldTransfer.html")
public class ReferralFieldTransferController extends BaseAdminPageController {

    private static final String VIEW_NAME = "admin/referralFieldTransfer";

    private static final Logger log = LoggerFactory.getLogger(ReferralFieldTransferController.class);

    @Autowired
    ReferralService referralService;
    @Autowired
    ClientRepository clientRepo;
    @Autowired
    ListDefinitionRepository listDefRepo;

    ReferenceDataSource referenceData;
    @Resource(name="applicationReferenceData")
    public void setReferenceData(ReferenceDataSource referenceData) {
        this.referenceData = referenceData;
    }

    @RequestMapping(method=RequestMethod.GET)
    public String get(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return VIEW_NAME;
    }

    @RequestMapping(method=RequestMethod.POST)
    public String handleRequest(ModelMap model, HttpServletRequest request) {

        //String objectName = ServletRequestUtils.getStringParameter(request, "objectName", null);
        Long referralId = ServletRequestUtils.getLongParameter(request, "referralId", -1);
        boolean allreferrals = ServletRequestUtils.getBooleanParameter(request, "allreferrals", false);

        String fieldSrc = ServletRequestUtils.getStringParameter(request, "field-src", null);
        String keySrc = ServletRequestUtils.getStringParameter(request, "key-src", null);

        String entityDest = ServletRequestUtils.getStringParameter(request, "entity-dest", null);
        String keyDest = ServletRequestUtils.getStringParameter(request, "key-dest", null);

        String listDefSrc = ServletRequestUtils.getStringParameter(request, "listdef-src", null);
        boolean save = ServletRequestUtils.getBooleanParameter(request, "save", false);

        Long serviceId = ServletRequestUtils.getLongParameter(request, "serviceId", -1);
        if (serviceId == -1) {
            serviceId = null;
        }

        int updated = 0;

        boolean valid = true;
        if ((!allreferrals && referralId < 1) || (allreferrals && referralId > 0)) {
            valid = false;
        }
        if (fieldSrc == null || keySrc == null) {
            valid = false;
        }
        if (entityDest == null || keyDest == null) {
            valid = false;
        }
        if ("object".equals(fieldSrc) && "client".equals(entityDest)) {
            valid = false;
        }

        if (valid) {
            // gather ids
            List<Long> referralIds = null;
            if (allreferrals) {
                log.info("loading ids: ALL");
            } else {
                log.info("loading id: " + referralId);
                referralIds = new ArrayList<>();
                referralIds.add(referralId);
            }

            // load referrals
            List<Referral> referrals = referralService.getCustomProperties(referralIds, serviceId);

            // log the keys in the customObjectData and textMap for the first referral
            // the first referral may not represent the whole set as questions might have been added to/made redundant over time
            if (referrals.isEmpty()) {
                log.info("referrals are EMPTY");
            } else {
                logCustomDataKeys(referrals.get(0));
            }

            // process
            List<Object> invalidEntities = new ArrayList<>();
            for (Referral referral : referrals) {

                log.info("processing: " + referral.getId());

                if ("referral".equals(entityDest)) {
                    processDestinationReferral(save, referral, fieldSrc, keySrc, keyDest, listDefSrc);
                }
                if ("client".equals(entityDest)) {
                    processDestinationClient(save, referral, fieldSrc, keySrc, keyDest);
                }

                updated++;
            }
            model.put("invalidEntites", invalidEntities);
            model.put("updated", updated);
            model.put("success", true);
        } else {
            model.put("success", false);
        }
        return VIEW_NAME;
    }

    private boolean processDestinationReferral(boolean save, Referral referral, String fieldSrc, String keySrc,
            String keyDest, String listDefSrc) {

        boolean changed = false;
        if ("object".equals(fieldSrc)) {
            Object srcValueObj = getReferralObjectData(referral, keySrc);
            changed = updateReferralObjectData(srcValueObj, referral, keyDest, listDefSrc);

        } else if ("string".equals(fieldSrc)) {
            String srcValueStr = getReferralStringData(referral, keySrc);
            changed = updateReferralStringData(srcValueStr, referral, keyDest);
        }

        // save the info
        if (changed && save) {
            referralService.setCustomProperties(referral);
        }

        return changed;
    }

    private boolean processDestinationClient(boolean save, Referral referral, String fieldSrc, String keySrc, String keyDest) {

        boolean changed = false;

        ClientDetail cd = clientRepo.findOneByReferralId(referral.getId());
        if (cd == null) {
            return false;
        }

        if ("date".equals(fieldSrc) && cd != null) {
            LocalDate srcValueObj = getReferralDateData(referral, keySrc);
            changed = updateClientDateData(srcValueObj, cd, keyDest);
        } else if ("string".equals(fieldSrc)) {
            String srcValueStr = getReferralStringData(referral, keySrc);
            changed = updateClientStringData(srcValueStr, cd, keyDest);
        }

        // save the info
        if (changed && save) {
            clientRepo.save(cd);
        }

        return changed;

    }
    private Object getReferralObjectData(Referral referral, String keySrc) {

        // get the object data...
        Object value = null;
        try {
            value = getReferralCustomObjectData(referral, keySrc);
        } catch (IllegalAccessException e) {
            log.info("\tvalue for '" + keySrc + "' does NOT exist in object data on this referral");
        }

        log.info("\tvalue raw: " + (value == null ? "null" : value.toString()));

        return value;
    }
    private LocalDate getReferralDateData(Referral referral, String keySrc) {

        // get the object data...
        LocalDate value = null;
        try {
            value = getReferralCustomDateData(referral, keySrc);
        } catch (IllegalAccessException e) {
            log.info("\tvalue for '" + keySrc + "' does NOT exist in date data on this referral");
        }

        log.info("\tvalue raw: " + (value == null ? "null" : value.toString()));

        return value;
    }


    private String getReferralStringData(Referral referral, String keySrc) {

        // get the object data...
        String value = null;
        try {
            value = getReferralTextMap(referral, keySrc);
        } catch (IllegalAccessException e) {
            log.info("\tvalue for '" + keySrc + "' does NOT exist in string data on this referral");
        }

        log.info("\tvalue raw: " + (value == null ? "null" : value.toString()));

        return value;
    }

    private boolean updateReferralStringData(String value, Referral referral, String keyDest) {

        // check the destination doesn't exist
        try {
            String destValue = getReferralTextMap(referral, keyDest);
            if (StringUtils.isNotBlank(destValue)) {
                log.info("\tvalue for '" + keyDest + "' EXISTS in string data on this referral, leaving untouched");
                return false;
            }
        } catch (IllegalAccessException e) {
            // its good if it doesn't exist - no conflicts
        }

        // translating
        // we only get here when there is a value, so set it
        log.info("\tcopying " + value + " onto " + keyDest);
        referral.getTextMap().put(keyDest, value);
        return true;
    }

    private boolean updateReferralObjectData(Object value, Referral referral, String keyDest, String listDefSrc) {

        // check the destination doesn't exist
        try {
            Object destValue = getReferralCustomObjectData(referral, keyDest);
            if (destValue != null) {
                log.info("\tvalue for '" + keyDest + "' EXISTS in object data on this referral, leaving untouched");
                return false;
            }
        } catch (IllegalAccessException e) {
            // its good if it doesn't exist - no conflicts
        }

        /*
        // translating Boolean -> YesNoDontKnow
        // we only get here when there is a value, so true or false
        if ("checkbox".equals(keyTypeSrc) && ("yesnodontknow".equals(keyTypeDest))) {
            Boolean valueBoolean;
            valueBoolean = value == null ? false : Boolean.valueOf(""+value);
            YesNoDontKnow yesno = valueBoolean ? YesNoDontKnow.Yes : YesNoDontKnow.No;
            log.info("\ttranslating " + valueBoolean.toString() + " into " + yesno);
            referral.getCustomObjectData().put(keyDest, yesno);
            return true;
        }
        */

        // translating
        // we only get here when there is a value, so set it
        if (listDefSrc == null) {
            log.info("\tcopying " + value + " onto " + keyDest);
            referral.getCustomObjectData().put(keyDest, value);
        } else {
            Integer id = null;
            if (value != null) {
                // get the id - which could be a mess on some referrals if it used to be a multi-select for instance
                try {
                    id = Integer.parseInt(value.toString());
                } catch (NumberFormatException e) {
                    log.info("\tnot a number: " + value);
                }
            }

            ListDefinitionEntry listEntry = null;
            if (id != null) {
                Map<Integer, String> mapToConvert = getMapOf_RT_ICD10();
                String name = getNameFromValue(mapToConvert, id);
                listEntry = getListEntryFromName(listDefSrc, name);
            }

            if (listEntry != null) {
                log.info("\tcopying " + value + " onto choicesMap " + keyDest + " with " + listEntry.getId());
                // the object instanatiates with an empty hashmap, but hibernate does other things
                if (referral.getChoicesMap() == null) {
                    referral.setChoicesMap(new HashMap<>());
                }
                referral.getChoicesMap().put(keyDest, listEntry.getId());
            } else {
                log.info("\tnot found " + value);
            }
        }
        return true;
    }

    private Map<Integer, String> getMapOf_RT_ICD10() {
        /*
        <form:option value="1" label="Cardiac"/>
        <form:option value="2" label="Circulatory"/>
        <form:option value="3" label="Gastrointestinal"/>
        <form:option value="4" label="Genetic"/>
        <form:option value="5" label="Genito-urinary"/>
        <form:option value="6" label="Haematology"/>
        <form:option value="7" label="Metabolic"/>
        <form:option value="8" label="Neurological"/>
        <form:option value="9" label="Oncology"/>
        <form:option value="10" label="Perinatal"/>
        <form:option value="11" label="Respiratory"/>
        <form:option value="12" label="Undiagnosed"/>
        <form:option value="13" label="Other"/>
        */
        Map<Integer, String> icd10 = new HashMap<>();
        icd10.put(1, "Cardiac");
        icd10.put(2, "Circulatory");
        icd10.put(3, "Gastrointestinal");
        icd10.put(4, "Genetic");
        icd10.put(5, "Genito-urinary");
        icd10.put(6, "Haematology");
        icd10.put(7, "Metabolic");
        icd10.put(8, "Neurological");
        icd10.put(9, "Oncology");
        icd10.put(10, "Perinatal");
        icd10.put(11, "Respiratory");
        icd10.put(12, "Undiagnosed");
        icd10.put(13, "Other");
        return icd10;
    }

    private ListDefinitionEntry getListEntryFromName(String listDefSrc, String name) {
        ListDefinitionEntry listEntry = null;
        if (name != null) {
            List<ListDefinitionEntry> listDef = listDefRepo.findByListNameAndName(listDefSrc, name);
            if (listDef != null) {
                listEntry = listDef.get(0);
            }
        }
        return listEntry;
    }

    private String getNameFromValue(Map<Integer, String> icd10, Integer id) {
        String name = null;
        List<Integer> keys = new ArrayList<>(icd10.keySet());
        for (int i = 0; i < keys.size(); i++) {
            int key = keys.get(i);
            if (id.equals(key)) {
                name = icd10.get(key);
            }
        }
        return name;
    }

    private boolean updateClientDateData(LocalDate value, ClientDetail cd, String keyDest) {

        // check the destination doesn't exist
        try {
            LocalDate destValue = getClientCustomDateData(cd, keyDest);
            if (destValue != null) {
                log.info("\tvalue for '" + keyDest + "' EXISTS in object data on this client, leaving untouched");
                return false;
            }
        } catch (IllegalAccessException e) {
            // its good if it doesn't exist - no conflicts
        }

        // translating
        // we only get here when there is a value, so set it
        log.info("\tcopying " + value + " onto " + keyDest);
        cd.getDateMap().put(keyDest, value);
        return true;
    }

    private boolean updateClientStringData(String value, ClientDetail cd, String keyDest) {

        // check the destination doesn't exist
        try {
            String destValue = getClientTextMap(cd, keyDest);
            if (StringUtils.isNotBlank(destValue)) {
                log.info("\tvalue for '" + keyDest + "' EXISTS in string data on this referral, leaving untouched");
                return false;
            }
        } catch (IllegalAccessException e) {
            // its good if it doesn't exist - no conflicts
        }

        // translating
        // we only get here when there is a value, so set it
        log.info("\tcopying " + value + " onto " + keyDest);
        cd.getTextMap().put(keyDest, value);
        return true;
    }

    private void logCustomDataKeys(Referral referral) {
        HashMap<String, Object> obj = referral.getCustomObjectData();
        for (String str : obj.keySet()) {
            log.info("referral object data keys: " + str);
        }
        HashMap<String, String> str = referral.getTextMap();
        for (String s : str.keySet()) {
            log.info("referral string data keys: " + s);
        }
    }

    private Object getReferralCustomObjectData(Referral referral, String key) throws IllegalAccessException {
        HashMap<String, Object> obj = referral.getCustomObjectData();
        for (String str : obj.keySet()) {
            if (key.equals(str)) {
                return obj.get(key);
            }
        }
        throw new IllegalAccessException();
    }
    private LocalDate getReferralCustomDateData(Referral referral, String key) throws IllegalAccessException {
        HashMap<String, LocalDate> obj = referral.getDateMap();
        for (String str : obj.keySet()) {
            if (key.equals(str)) {
                return obj.get(key);
            }
        }
        throw new IllegalAccessException();
    }
    private String getReferralTextMap(Referral referral, String key) throws IllegalAccessException {
        HashMap<String, String> str = referral.getTextMap();
        for (String s : str.keySet()) {
            if (key.equals(s)) {
                return str.get(key);
            }
        }
        throw new IllegalAccessException();
    }

    private LocalDate getClientCustomDateData(ClientDetail cd, String key) throws IllegalAccessException {
        HashMap<String, LocalDate> obj = cd.getDateMap();
        for (String str : obj.keySet()) {
            if (key.equals(str)) {
                return obj.get(key);
            }
        }
        throw new IllegalAccessException();
    }
    private String getClientTextMap(ClientDetail cd, String key) throws IllegalAccessException {
        HashMap<String, String> str = cd.getTextMap();
        for (String s : str.keySet()) {
            if (key.equals(s)) {
                return str.get(key);
            }
        }
        throw new IllegalAccessException();
    }

}
