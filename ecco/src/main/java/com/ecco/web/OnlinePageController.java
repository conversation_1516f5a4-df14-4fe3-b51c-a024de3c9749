package com.ecco.web;

import com.ecco.infrastructure.config.ApplicationProperties;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * Online mappings for /nav/ entry points designed to work online and offline.
 * Typically, these jsp files (in online/) simply kick start importRequireJsModules.
 * They can also form part of JspOfflineResourceProvider which puts the /nav/ jsp pages as offline fallbacks
 * for OfflineConfig servlet which matches prefix /offline/. The jsp pages which want both online and offline
 * therefore need to call importRequireJsModules with the prefix 'offline/'.
 * NOTE: See web.xml where 'ecco' servlet maps to p,online,nav,dynamic prefixes.
 */
@Controller
@AllArgsConstructor
public class OnlinePageController extends BaseController {

    private final ApplicationProperties applicationProperties;

    @ModelAttribute
    public ApplicationProperties applicationProperties() {
        return applicationProperties;
    }


     // REACT-ROUTER SECTION START
     /**
     * Generic matcher for react-router, more specific matches below.
      * eg matches /r/refer which goes to online/refer which loads referral/refer
     */
    @GetMapping({"/r/{routeBase}/**", "/w/{routeBase}/**"})
    public String getJsp(@PathVariable String routeBase) {
        return "../online/" + routeBase; // NOTE: JSP only - relative to WEB-INF/content to divert to WEB-INF/online
    }

    // REACT-ROUTER SECTION END

    /**
     * Generic matcher for non-react, more specific matches below.
     * This controller actually maps to {p|online|nav|dynamic}, or offline (see javadoc)
     * Therefore /online/{path} can mean online/online.
     * Using 'page' may be clearer
     */
    @GetMapping({"/online/{path}", "/page/{path}"})
    public String getDev(@PathVariable String path) {
        return "../online/" + path;
    }

    @GetMapping("/online/{path1}/{path2}")
    public String getDev(@PathVariable String path1, @PathVariable String path2) {
        return "../online/" + path1 + "/" + path2;
    }

    /**
     * Specific matchers
     */
    @GetMapping("/help/{path}")
    public String getHelp(@PathVariable String path) {
        return "../help/" + path;
    }

 }
