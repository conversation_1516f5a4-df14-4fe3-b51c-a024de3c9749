package com.ecco.web.validators;

import java.util.Set;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import com.ecco.dom.Company;
import com.ecco.dom.Individual;
import com.ecco.dom.contacts.Contact;

@Component
public class ContactValidator {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name="globalAnnotationValidator")
    private Validator validator;
    @Resource(name="globalAnnotationValidator")
    private javax.validation.Validator javaxValidator;

    // we still need to implement a spring Validator stuff here, since 'Contact' is an interface which webflow skips as not-matched
    // https://jira.springsource.org/browse/SWF-995
    public boolean supports(Class<?> clazz) {
        return Contact.class.isAssignableFrom(clazz);
    }
    // standard non-webflow validation to ensure sensible fields for all Contact types
    public void validate(Object target, Errors errors) {
        Contact contact = (Contact) target;

        // validate using annotated aspects
        Set<ConstraintViolation<Contact>> constraints = javaxValidator.validate(contact);

        if (contact instanceof Individual) {
            Individual i = (Individual) contact;
            if (StringUtils.isBlank(i.getFirstName())) {
                errors.rejectValue("firstName", null, "a first name is required");
            }
            if (StringUtils.isBlank(i.getLastName())) {
                errors.rejectValue("lastName", null, "a last name is required");
            }
        }
        if (contact instanceof Company) {
            Company i = (Company) contact;
            if (StringUtils.isBlank(i.getCompanyName())) {
                errors.rejectValue("companyName", null, "a name is required");
            }
        }

        if (StringUtils.isNotBlank(contact.getEmail())) {
            buildJsrMessage(constraints, errors, "email");
        }
    }

    // WEBFLOW VALIDATION
    // we want webflow to validate Contact details, and specifically, knowing if we are validating a client or a non-client Individual
    // we can't validate using normal webflow way - which looks for a method validate<view name> on a registered validator and the class of the object
    // (be careful not to use Contact (superclass) but Individual, otherwise this method is never called because webflow is specific on the matching)
    // because webflow can't tell if the transitions are for a client's Individual or a 'contacts' Individual
    // so to validate additional client details, we needs to call specific methods from webflow
    // we could do so with a specific transition (then use if (!context.getUserEvent().equals("next"))), but our transition names are buried in jsp
    // we could see if ValidationContext could access flowScope.isClient
    // or we opt for specific validation method calls as below

    // webflow validation to ensure sensible fields - specifically called, for non-client contact's

    // webflow specific validation is removed - in favour or client-side validation coming in next commits...

    private void buildJsrMessage(Set<ConstraintViolation<Contact>> constraints, Errors errors, String property) {
        for (ConstraintViolation<?> constraint : constraints) {
            if (constraint.getPropertyPath().toString().equals(property)) {
                errors.rejectValue(property, null, constraint.getMessage());
                break;
            }
        }
    }

}
