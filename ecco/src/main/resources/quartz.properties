org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.ThreadCount=1
org.quartz.threadPool.threadPriority=4

org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin
org.quartz.plugin.triggHistory.triggerFiredMessage = Trigger {1}.{0} fired job {6}.{5} at: {4, date, HH:mm:ss dd/MMM/yyyy}
org.quartz.plugin.triggHistory.triggerCompleteMessage = Trigger {1}.{0} completed firing job {6}.{5} at {4, date, HH:mm:ss dd/MMM/yyyy} with resulting trigger instruction code: {9}

# use this if there is a data store
#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate


# EXAMPLE FILE
# with ## meaning was enabled

# Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance.
#
# Instances of the specified JobStore, ThreadPool and Logger classes will
# be created by name, and then any additional properties specified for them
# in this file will be set on the instance by calling an equivalent 'set'
# method. (see below for more examples)
#

# ===========================================================================
# Configure Main Scheduler Properties  ======================================
# ===========================================================================
#
# The general pattern for defining the scheduler's main properties is:
#
#     org.quartz.scheduler.instanceName = SCHED_NAME
#     org.quartz.scheduler.instanceId = INSTANCE_ID
#     org.quartz.scheduler.threadName = THREAD_NAME
#     org.quartz.scheduler.rmi.export = false
#     org.quartz.scheduler.rmi.proxy = false
#     org.quartz.scheduler.rmi.registryHost = localhost
#     org.quartz.scheduler.rmi.registryPort = 1099
#     org.quartz.scheduler.rmi.createRegistry = never
#     org.quartz.scheduler.userTransactionURL = USER_TX_LOCATION
#     org.quartz.scheduler.wrapJobExecutionInUserTransaction = JOBS_IN_USER_TX
#     org.quartz.scheduler.idleWaitTime = IDLE_WAIT_TIME
#     org.quartz.scheduler.dbFailureRetryInterval = DB_FAILURE_RETRY_INTERVAL
#     org.quartz.scheduler.classLoadHelper.class = CLASS_LOAD_HELPER_CLASS
#     org.quartz.context.key.SOME_KEY = SOME_VALUE
#
#
# "SCHED_NAME" can be any string, and has no meaning to the scheduler itself -
# but rather serves as a mechanism for client code to distinguish schedulers
# when multiple instances are used within the same program.  If you are using
# the clustering features, you must use the same name for every instance in 
# the cluster that is 'logically' the same Scheduler.
#
# "INSTANCE_ID" can be any string, and but must be unique for all schedulers
# working as if they are the same 'logical' Scheduler within a cluster.
# you may use the value "AUTO" as the instanceId if you wish the Id to be 
# generated for you.
#
# "THREAD_NAME" can be any String that is a valid name for a java thread. If
# this property is not specified, the thread will receive the scheduler's 
# name ("org.quartz.scheduler.instanceName").
#
# "USER_TX_LOCATION" should be set to the JNDI URL at which Quartz can locate
# the Application Server's UserTransaction manager.  The default value (if not
# specified) is "java:comp/UserTransaction" - which works for almost all 
# Application Servers.  Websphere users may need to set this property to 
# "jta/usertransaction".  This is only used if Quartz is configured to use
# JobStoreCMT, and "JOBS_IN_USER_TX" is set to true.
# 
# "JOBS_IN_USER_TX" should be set to "true" if you want Quartz to start a 
# UserTransaction before calling execute on your job.  The Tx will commit after
# the job's execute method completes, and the JobDataMap is updated (if it is
# a StatefulJob).  The default value is "false".
#
# "IDLE_WAIT_TIME" is the amount of time in milliseconds that the scheduler 
# will wait before re-queries for available triggers when the scheduler is otherwise
# idle.  Normally you should not have to 'tune' this parameter, unless you're using
# XA transactions, and are having problems with delayed firings of triggers that
# should fire immediately.
#
# "DB_FAILURE_RETRY_INTERVAL" is the amount of time in milliseconds that the
# scheduler will wait between re-tries when it has detected a loss of 
# connectivity to the database (obviously not meaningful with RamJobStore)
#
# "CLASS_LOAD_HELPER_CLASS" defaults to the most robust approach, which is to 
# use the "org.quartz.simpl.CascadingClassLoadHelper" class - which in turn
# uses every other ClassLoadHelper class until one works.  You should probably
# not find the need to specify any other class for this property, though strange
# things seem to happen within application servers.  All of the current 
# ClassLoadHelper implementation can be found in the "org.quartz.simpl" package.
#
# "SOME_KEY" and "SOME_VALUE" represent a name-value pair that will be placed
# into the "scheduler context" as strings. (see Scheduler.getContext()).  
# So for example, the setting "org.quartz.context.key.MyKey = MyValue" would 
# perform the equivalent of scheduler.getContext().put("MyKey", "MyValue").
#
#
# RMI notes:
#
# If you want the Quartz Scheduler exported via RMI as a server then set
# the 'rmi.export' flag to true.  You must also then specify a  host and
# port for the rmiregistry process - which is typically 'localhost' port 1099.
#
# Set the 'rmi.createRegistry' flag according to how you want Quartz to cause 
# the creation of an RMI Registry.  Use "false" or "never" if you don't want
# Quartz to create a registry.  Use "true" or "as_needed" if you want Quartz
# to first attempt to use an existing registry, and then fall back to creating 
# one.  Use "always" if you want Quartz to attempt creating a Registry, and
# then fall back to using an existing one.
#   If a registry is created, it will be bound to port number in the given 
# the 'rmi.registryPort' property, and 'rmi.registryHost' should be "localhost".
#
# If you want to connect (use) a remotely served scheduler, then set the
# 'rmi.proxy' flag to true.  You must also then specify a  host and port
# for the rmiregistry process - which is typically 'localhost' port 1099.
#
# You cannot specify a 'true' value for both 'export' and 'proxy' - if you
# do, the 'export' option will be ignored.  A value of 'false' for both
# 'export' and 'proxy' properties is of course valid.
#
##org.quartz.scheduler.instanceName = Sched1
##org.quartz.scheduler.instanceId = 1
##org.quartz.scheduler.rmi.export = false
##org.quartz.scheduler.rmi.proxy = false


# ===========================================================================
# Configure ThreadPool  =====================================================
# ===========================================================================
#
# The general pattern for defining a thread pool is the following:
#
#     org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
#     org.quartz.threadPool.threadCount = THREAD_COUNT
#     org.quartz.threadPool.threadPriority = THREAD_PRIO
#
#       optional parameters for SimpleThreadPool are:
#
#         org.quartz.threadPool.makeThreadsDaemons = DAEMON_THREADS
#         org.quartz.threadPool.threadsInheritGroupOfInitializingThread = INHERIT_GRP
#         org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = INHERIT_LDR
#
#  or
#
#     org.quartz.threadPool.class = com.mycompany.goo.FooThreadPool
#     org.quartz.threadPool.somePropOfFooThreadPool = someValue
#
# "THREAD_COUNT" can be any positive integer, although you should realize that
# only numbers between 1 and 100 are very practical.  This is the number of
# threads that are available for concurrent execution of jobs.  If you only
# have a few jobs that fire a few times a day, then 1 thread is plenty! If you
# have tens of thousands of jobs, with many firing every minute, then you
# probably want a thread count more like 50 or 100 (this highly depends on the
# nature of the work that your jobs perform, and your systems resources!)
#
# "THREAD_PRIO" can be any int between Thread.MIN_PRIORITY (1) and
# Thread.MAX_PRIORITY (10).  The default is Thread.NORM_PRIORITY (5).
#
# "DAEMON_THREADS" can be set to "true" to have the threads in the pool created
# as daemon threads.  Default is "false".
#
# "INHERIT_GRP" can be "true" or "false", and defaults to true.
#
# "INHERIT_LDR" can be "true" or "false", and defaults to false.
#
##org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
##org.quartz.threadPool.threadCount = 3
##org.quartz.threadPool.threadPriority = 5

# ===========================================================================
# Configure JobStore  =======================================================
# ===========================================================================
#
# The general pattern for defining a JobStore is one of the following:
#
#     org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore
#     org.quartz.jobStore.misfireThreshold = MISFIRE_THRESHOLD
#
#  or
#
#     org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.<JobStoreClass>
#       Where JobStoreClass is one of:
#         - JobStoreTX is for standalone-Quartz implementations
#         - JobStoreCMT is for appserver-based container-managed
#           transaction Quartz implementations
#
#     org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.<DriverDelegateClass>
#       Where DriverDelegateClass is one of:
#         - StdJDBCDelegate (for many JDBC-compliant drivers)
#         - MSSQLDelegate (for Microsoft SQL Server drivers)
#         - PostgreSQLDelegate (for PostgreSQL drivers)
#         - WebLogicDelegate (for WebLogic drivers)
#         - oracle.OracleDelegate (for Oracle drivers)
#
#     org.quartz.jobStore.useProperties = USE_PROPERTIES
#     org.quartz.jobStore.dataSource = DS_NAME
#     org.quartz.jobStore.tablePrefix = TABLE_PREFIX
#     org.quartz.jobStore.isClustered = IS_CLUSTERED
#     org.quartz.jobStore.selectWithLockSQL = LOCKING_SELECT_STATEMENT
#     org.quartz.jobStore.dontSetAutoCommitFalse = DONT_TURN_OFF_AUTO_COMMIT
#     org.quartz.jobStore.maxMisfiresToHandleAtATime = MAX_MISFIRE_HANDLE
#     org.quartz.jobStore.txIsolationLevelSerializable = SERIALIZABLE_ISOLATION
#
#   If you're using JobStoreCMT then you need this param also:
#
#     org.quartz.jobStore.nonManagedTXDataSource = NON_MANAGED_TX_DS_NAME
#
#   And, if you're using JobStoreCMT, then these params are optional:
#
#     org.quartz.jobStore.dontSetNonManagedTXConnectionAutoCommitFalse = DONT_TURN_OFF_AUTO_COMMIT
#     org.quartz.jobStore.txIsolationLevelReadCommitted = READ_COMMITTED_ISOLATION
#
#
#  or, for a custom JobStore implementation:
#
#     org.quartz.jobStore.class = com.mycompany.goo.FooJobStore
#     org.quartz.jobStore.somePropOfFooJobStore = someValue
#
#
# The value of "MISFIRE_THRESHOLD" should be the number of milliseconds the
# scheduler will 'tolerate' a trigger to pass its next-fire-time by, before
# being considered "misfired".  The default value (if you don't make an entry
# of this property in your configuration) is 60000 (60 seconds).
#
# The value of "MAX_MISFIRE_HANDLE" is the maximum number of misfired triggers 
# that the misfire handlingthread will try to recover at one time (within one 
# transaction).  If unspecified, the default is 20.
#
# The "USE_PROPERTIES" flag (true or false value - defaults to false) instructs
# JDBCJobStore that all values in JobDataMaps will be Strings, and therefore
# can be stored as name-value pairs, rather than storing more complex objects
# in their serialized form in the BLOB column.  This is much safer in the long 
# term, as you avoid the class versioning issues that there are with 
# serializing your non-String classes into a BLOB.
#
# JDBCJobStore's "DS_NAME" must be the name of one the datasources
# defined in this file. JobStoreCMT _requires_ a datasource that contains 
# container-managed-transaction-capable connections.  Typically this means a 
# datasource that is managed by an application server, and used by Quartz by
# specifying the JNDI url of the datasource.
#
# JobStoreCMT also _requires_ a (second) datasource that contains connections
# that will not be part of container-managed transactions. 
# "NON_MANAGED_TX_DS_NAME" must be the name of one the datasources defined in 
# this file. - This datasource must contain non-container-transaction managed
# connections.  
#
# JDBCJobStore's "TABLE_PREFIX" property is a string equal to the prefix
# given to Quartz's tables that were created in your database.
#
# JDBCJobStore's "IS_CLUSTERED" property must be set to either "true" or
# "false".  If unset, the default is "false".  This property must be set
# to "true" if you are having multiple instances of Quartz use the same
# set of database tables... otherwise you will experience havoc.  Also
# note that each instance in the cluster MUST have a unique "instance id"
# (the "org.quartz.scheduler.instanceId" property), but should have the 
# same "scheduler instance name" ("org.quartz.scheduler.instanceName").
#
# * NOTE: Never run clustering on separate machines, unless their clocks are
#         synchronized using some form of time-sync service (daemon) that runs
#         very regularly (the clocks must be within a second of each other).
#         See http://www.boulder.nist.gov/timefreq/service/its.htm if you are
#         unfamiliar with how to do this.
#
#         Also: never fire-up a non-clustered instance against the same set
#         of tables that any other instance is running against.  You will 
#         get serious data corruption, and eratic behavior.
#
#
# JDBCJobStore's "LOCKING_SELECT_STATEMENT" property must be a SQL string
# that selects a row in the "LOCKS" table and places a lock on it. If not
# set, the default is "SELECT * FROM {0}LOCKS WHERE LOCK_NAME = ? FOR UPDATE", 
# which works for most databases.  The "{0}" is replaced during run-time 
# with the TABLE_PREFIX that you configured above.
#
# "DONT_TURN_OFF_AUTO_COMMIT" tells Quartz not to call setAutoCommit(false) 
# on connections obtained from the DataSource(s).  This can be helpful 
# in a few situations, such as if you have a driver that complains if 
# it is called when it is already off.  This property defaults to false.
#
# "SERIALIZABLE_ISOLATION" tells Quartz (when using JobStoreTX or CMT) to call 
# setTransactionIsolation(Connection.TRANSACTION_SERIALIZABLE);  on JDBC 
# connections.  This can be helpful to prevent lock timeouts with some databases 
# under high load, and "longer"-lasting transactions.
#
# "READ_COMMITTED_ISOLATION" tells Quartz (When using JobStoreCMT) to call
# setTransactionIsolation(Connection.TRANSACTION_READ_UNCOMMITTED);  on the
# non-managed JDBC connections.  This can be helpful to prevent lock timeouts 
# with some databases (such as DB2) under high load, and "longer"-lasting 
# transactions.
#
#
#
##org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
##org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
##org.quartz.jobStore.useProperties = false
##org.quartz.jobStore.dataSource = myDS
##org.quartz.jobStore.tablePrefix = QRTZ_
##org.quartz.jobStore.isClustered = false
#org.quartz.jobStore.selectWithLockSQL = 

# ===========================================================================
# Configure Datasources  ====================================================
# ===========================================================================
#
# (only needed when using JDBCJobStore, or a plugin that requires JDBC)
#
# -- If your Scheduler is very busy (i.e. nearly always executing the same
#    number of jobs as the size of the thread pool, then you should probably
#    set the number of connections in the DataSource to be the size of the
#    thread pool + 1
#
# The general pattern for defining a DataSource is one of the following:
#
#     org.quartz.dataSource.NAME.driver = DRIVER_CLASS_NAME
#     org.quartz.dataSource.NAME.URL = DB_URL
#     org.quartz.dataSource.NAME.user = DB_USER
#     org.quartz.dataSource.NAME.password = DB_PASSWORD
#     org.quartz.dataSource.NAME.maxConnections = DB_POOL_SIZE
#     org.quartz.dataSource.NAME.validationQuery= VALIDATION_QUERY
#
#  or
#
#     org.quartz.dataSource.NAME.jndiURL = DB_JNDI_URL
#
#  or
#     org.quartz.dataSource.NAME.jndiURL = DB_JNDI_URL
#     org.quartz.dataSource.NAME.jndiAlwaysLookup = DB_JNDI_ALWAYS_LOOKUP
#     org.quartz.dataSource.NAME.java.naming.factory.initial = JNDI_CTXT_FACTORY
#     org.quartz.dataSource.NAME.java.naming.provider.url = JNDI_PROVIDER_URL
#     org.quartz.dataSource.NAME.java.naming.security.principal = JNDI_PRINCIPAL
#     org.quartz.dataSource.NAME.java.naming.security.credentials = JNDI_CREDENTIALS
#
#
# The DataSource's "NAME" can be anything you want, and has no meaning other
# than being able to 'define' a DataSource here, and assign it by name to the
# JDBCJobStore.
#
# With the two types of DataSource definition shown above, a DataSource can
# either be created with the given database connection information, or can
# be "logically mapped" to use a DataSource that is managed by an application
# server an made available via JNDI.
#
# "DRIVER_CLASS_NAME" must be the java class name of the JDBC driver for your
# database.
#
# "DB_URL" must be the connection URL (host, port, etc.) for connection to your
# database.
#
# "DB_USER" is the user name to use when connecting to your database.
#
# "DB_USER" is the password to use when connecting to your database.
#
# "DB_POOL_SIZE" is the maximum number of connections that the DataSource can
# create in it's pool of connections.
#
# "VALIDATION_QUERY" is an optional SQL query string that the DataSource
# can use to detect and replace failed/corrupt connections.  For example an
# oracle user might choose "select table_name from user_tables" - which is a 
# query that should never fail - unless the connection is actually bad.
#
# "DB_JNDI_URL" is the JNDI URL for a DataSource that is managed by your
# application server.  Additionally, you can provide the class name of the 
# JNDI InitialContextFactory that you wish to use, the provider's URL, and
# a username & password for connecting to the JNDI provider, if it is not
# the default provider of your environment.
#
# "DB_JNDI_ALWAYS_LOOKUP" can be "true" or "false" - if the property is not
# set, the default is "false".  This option tells Quartz whether or not it
# should always lookup the DataSource under the JNDI tree each time it 
# needs to get a connection from it.  If set to (the default) "false", 
# Quartz will "hold on to" the DataSource after looking it up only once.
#
##org.quartz.dataSource.myDS.driver = oracle.jdbc.driver.OracleDriver
##org.quartz.dataSource.myDS.URL = jdbc:oracle:thin:@*********:1521:demodb
##org.quartz.dataSource.myDS.user = mops_proto
##org.quartz.dataSource.myDS.password = mops_proto
##org.quartz.dataSource.myDS.maxConnections = 5

#org.quartz.dataSource.myDS.jndiURL=jdbc/PAWS
#org.quartz.dataSource.myDS.jndiAlwaysLookup=false
#org.quartz.dataSource.myDS.java.naming.factory.initial=com.evermind.server.rmi.RMIInitialContextFactory
#org.quartz.dataSource.myDS.java.naming.provider.url=ormi://localhost
#org.quartz.dataSource.myDS.java.naming.security.principal=admin
#org.quartz.dataSource.myDS.java.naming.security.credentials=123


# ===========================================================================
# Configure SchedulerPlugins  ===============================================
# ===========================================================================
#
# The general pattern for defining a SchedulerPlugin is the following:
#
#     org.quartz.plugin.NAME.class = PLUGIN_CLASS_NAME
#
# If the plugin class has properties you want set via some "setter" methods 
# on the class, name the properties and values as such
#
#     org.quartz.plugin.NAME.propName = propValue
#
# ...where "propName" corrisponds to a "setPropName" method on the plugin 
# class.  Only primitive data type values (including Strings) are supported.
#
#
# Configure Plugins =========================================================

##org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin
##org.quartz.plugin.triggHistory.triggerFiredMessage = Trigger {1}.{0} fired job {6}.{5} at: {4, date, HH:mm:ss MM/dd/yyyy}
##org.quartz.plugin.triggHistory.triggerCompleteMessage = Trigger {1}.{0} completed firing job {6}.{5} at {4, date, HH:mm:ss MM/dd/yyyy} with resulting trigger instruction code: {9}

##org.quartz.plugin.jobInitializer.class = org.quartz.plugins.xml.JobInitializationPlugin
##org.quartz.plugin.jobInitializer.fileName = data/my_job_data.xml
##org.quartz.plugin.jobInitializer.overWriteExistingJobs = false
##org.quartz.plugin.jobInitializer.failOnFileNotFound = true

##org.quartz.plugin.shutdownhook.class = org.quartz.plugins.management.ShutdownHookPlugin
##org.quartz.plugin.shutdownhook.cleanShutdown = true

# ===========================================================================
# Configure Listeners         ===============================================
# ===========================================================================
#
# The general pattern for defining a "Global" TriggerListener is:
#
#     org.quartz.triggerListener.NAME.class = TRIGGER_LISTENER_CLASS_NAME
#
# The general pattern for defining a "Global" JobListener is the following:
#
#     org.quartz.jobListener.NAME.class = JOB_LISTENER_CLASS_NAME
#
# "NAME" becomes the listener's name, and a "setName(String)" method is 
# reflectively found and called on the class that is instantiated.
#
# If the listener class has properties you want set via some "setter" methods 
# on the class, name the properties and values as such
#
#     org.quartz.triggerListener.NAME.propName = propValue
# or
#     org.quartz.jobListener.NAME.propName = propValue
#
# ...where "propName" corrisponds to a "setPropName" method on the listener 
# class.  Only primitive data type values (including Strings) are supported.
#
#
# Configure Plugins =========================================================

##org.quartz.triggerListener.dummy.class = org.quartz.examples.DumbTriggerListener
