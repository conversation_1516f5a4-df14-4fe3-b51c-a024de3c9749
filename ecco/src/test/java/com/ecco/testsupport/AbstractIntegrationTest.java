package com.ecco.testsupport;

import java.io.Serializable;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.ecco.dom.*;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.dom.BaseEntity;

@Transactional
public abstract class AbstractIntegrationTest extends AbstractNonTxIntegrationTest {

    // NB test with the hibenate filter, like
    // List<Referral> entities = new HibFilterTemplate(entityManager).executeUnfiltered(() ->
    //                referralRepository.findByClientId(c1.getId()));

    /**
     * Insert individual and individual.getUser(), and flush to database
     */
    protected void insert(Individual individual) {
        //noinspection deprecation
        entityManager.persist(individual.getUser());
        entityManager.persist(individual);
        // Work around the @PrePersist behaviour in ContactImpl#copyAddressOnFirstSave to ensure we don't populate contacts.address
        if (individual.getAddressedLocation() != null && individual.getAddressedLocation().getId() == null) {
            individual.setAddress(null);
            entityManager.persist(individual.getAddressedLocation());
        }
        flush();
    }

    /**
     * Insert entity and flush to database
     */
    protected <PK extends Serializable> void insert(BaseEntity<PK> entity) {
        entityManager.persist(entity);
        flush();
    }

    protected void flush() {
        entityManager.flush();
    }

    protected void flushAndClear() {
        flush();
        entityManager.clear();
    }

    protected List<Referral> getReferrals() {
        //noinspection unchecked
        return entityManager.createQuery("from Referral e order by id asc").getResultList();
    }

    protected ContactImpl createAuthor() throws Exception {
        Individual i = Individual.builder("my", "staff1").withNewUser("mystaff1").build();
        insert(i);
        return i;
    }

    protected List<Service> getServices() {
        //noinspection unchecked
        return entityManager.createQuery("from " + Service.class.getName())
                .getResultList();
    }

    protected ServiceCategorisation persistServiceAllocation(String serviceName) {
        return persistServiceAllocation(serviceName, null);
    }

    protected ServiceCategorisation persistServiceAllocation(String serviceName, String projectName) {
        Project project = projectName == null ? null : new Project(projectName);
        Service service = new Service(serviceName);
        ServiceCategorisation sc = new ServiceCategorisation(service, project);
        insert(sc);
        flush();
        return sc;
    }

}
