package com.ecco.security.ldap.config;

import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.security.service.EccoUserDetailsContextMapper;
import com.ecco.security.ldap.LdapController;
import com.ecco.security.ldap.LdapUserDetailsContextMapper;
import com.ecco.security.ldap.service.LdapGroupMappingService;
import com.ecco.security.repositories.LdapRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.security.ldap.userdetails.UserDetailsContextMapper;


@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(basePackageClasses = LdapRepository.class,
        repositoryBaseClass= QueryDslJpaEnhancedRepositoryImpl.class)
@Order(40)
public class LdapConfig {

    @Value("${ecco.authn.ad:false}")
    boolean enableAd;

    @Value("${ecco.authn.kerberos:false}")
    boolean enableKerberos;

    @Value("${azure.activedirectory.client-id:#{null}}")
    private String azureClientId;

    /**
     * Enable if ldap groups are in use.
     * Currently allows AD groups to show in EditProjectForm.ts.
     */
    @Bean(name="enableLdap")
    public boolean enableLdap() {
        return enableAd || enableKerberos || (azureClientId != null);
    }

    @Bean
    public UserDetailsContextMapper customLdapUserDetailsContextMapper(EccoUserDetailsContextMapper eccoUserDetailsContextMapper) {
        return new LdapUserDetailsContextMapper(eccoUserDetailsContextMapper);
    }

    @Bean
    public LdapController ldapController(@Qualifier("enableLdap") boolean enableLdap,
                                         LdapGroupMappingService ldapService) {
        return new LdapController(enableLdap, ldapService);
    }

}
