package com.ecco.config.service;

import javax.validation.constraints.NotNull;

public interface FeatureEnablementVoter {

    enum Vote {
        /** This vote will result in vote being NO (feature disabled) with no further voters getting a vote */
        ALWAYS_DISABLED,

        /** This vote will result in vote being YES (feature enabled) with no further voters getting a vote */
        ALWAYS_ENABLED,

        /** This vote will result in a NO answer if no further votes are configured */
        DISABLED_BY_DEFAULT,

        /** This vote will result in a YES answer if no further votes are configured */
        ENABLED_BY_DEFAULT,

        /** A 'null' vote. i.e. this voter has no opinion */
        ABSTAIN
    }

    /**
     * Get the vote for this feature from this voter.
     *
     * @return the vote.
     */
    @NotNull
    Vote getVote(String featureName);

}
