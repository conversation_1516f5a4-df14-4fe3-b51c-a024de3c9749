package com.ecco.config.dom;

import javax.persistence.*;

import com.ecco.infrastructure.spring.data.AbstractTableGeneratedIdPersistable;

@Entity
@Table(name = "cfg_menuitem")
@Access(AccessType.FIELD)
public class MenuItem extends AbstractTableGeneratedIdPersistable {

    private static final long serialVersionUID = 1L;

    @Column(nullable = false)
    private String url;

    @Column(nullable = false, length=60)
    private String linkText;

    @Column(nullable = false)
    private String imageUrl;

    @Column(nullable = true, length=1)
    private String acceleratorKey;

    @Column(nullable = true)
    private String roles;

    @JoinColumn(name="module_name")
    @ManyToOne(optional=false, fetch=FetchType.EAGER)
    private SoftwareModule module;


    public String getUrl() {
        return url;
    }

    public String getLinkText() {
        return linkText;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public String getAcceleratorKey() {
        return acceleratorKey;
    }

    public String getRoles() {
        return roles;
    }

    public boolean isEnabled() {
        return module.isEnabled();
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((acceleratorKey == null) ? 0 : acceleratorKey.hashCode());
        result = prime * result + ((imageUrl == null) ? 0 : imageUrl.hashCode());
        result = prime * result + ((linkText == null) ? 0 : linkText.hashCode());
        result = prime * result + ((module == null) ? 0 : module.hashCode());
        result = prime * result + ((roles == null) ? 0 : roles.hashCode());
        result = prime * result + ((url == null) ? 0 : url.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!super.equals(obj))
            return false;
        if (getClass() != obj.getClass())
            return false;
        MenuItem other = (MenuItem) obj;
        if (acceleratorKey == null) {
            if (other.acceleratorKey != null)
                return false;
        } else if (!acceleratorKey.equals(other.acceleratorKey))
            return false;
        if (imageUrl == null) {
            if (other.imageUrl != null)
                return false;
        } else if (!imageUrl.equals(other.imageUrl))
            return false;
        if (linkText == null) {
            if (other.linkText != null)
                return false;
        } else if (!linkText.equals(other.linkText))
            return false;
        if (module == null) {
            if (other.module != null)
                return false;
        } else if (!module.equals(other.module))
            return false;
        if (roles == null) {
            if (other.roles != null)
                return false;
        } else if (!roles.equals(other.roles))
            return false;
        if (url == null) {
            if (other.url != null)
                return false;
        } else if (!url.equals(other.url))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "MenuItem [url=" + url + ", link=" + linkText + ", image=" + imageUrl + ", acceleratorKey=" + acceleratorKey
                + ", role=" + roles + "]";
    }
}
