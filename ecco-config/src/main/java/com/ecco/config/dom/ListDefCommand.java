package com.ecco.config.dom;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("listDef")
public class ListDefCommand extends ConfigCommand {
    public ListDefCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ListDefCommand() {
    }
}
