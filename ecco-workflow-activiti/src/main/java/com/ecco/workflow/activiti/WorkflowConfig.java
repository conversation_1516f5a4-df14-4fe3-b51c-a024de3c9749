package com.ecco.workflow.activiti;

import com.ecco.config.service.SettingsService;
import com.ecco.security.service.UserManagementService;
import com.google.common.collect.ImmutableList;

import org.activiti.engine.HistoryService;
import org.activiti.engine.ManagementService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.interceptor.SessionFactory;
import org.activiti.engine.impl.persistence.entity.GroupEntityManager;
import org.activiti.spring.ProcessEngineFactoryBean;
import org.activiti.spring.SpringProcessEngineConfiguration;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.PlatformTransactionManager;

import java.io.IOException;

import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import javax.sql.DataSource;

@Configuration
public class WorkflowConfig {
    @Autowired
    private DataSource dataSource;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private UserManagementService userManagementService;

    @PersistenceUnit
    private EntityManagerFactory entityManagerFactory;

    @Qualifier("activitiWorkflow")
    @Bean
    public ActivitiWorkflowServiceImpl activitiWorkflowService(SettingsService settingsService) throws Exception {
        return new ActivitiWorkflowServiceImpl(activitiRepositoryService(), activitiRuntimeService(),
                activitiTaskService(), activitiHistoryService(), activitiFormService(), settingsService);
    }

    @Bean
    public SpringProcessEngineConfiguration processEngineConfiguration() throws IOException {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource);
        config.setTransactionManager(transactionManager);
        config.setDatabaseSchemaUpdate(Boolean.TRUE.toString());
        config.setJpaEntityManagerFactory(entityManagerFactory);
        config.setJpaHandleTransaction(true);
        config.setJpaCloseEntityManager(true);
        config.setJobExecutorActivate(false);
        config.setDeploymentResources(
                new PathMatchingResourcePatternResolver().getResources("classpath:/com/ecco/workflow/autodeploy/*.bpmn20.xml"));
        config.setFormService(activitiFormService());
        config.setCustomSessionFactories(ImmutableList.of(genericSessionFactory(GroupEntityManager.class, eccoGroupManager())));

        config.setMailServerDefaultFrom("<EMAIL>");
        config.setMailServerHost("localhost");
        config.setMailServerPort(7025);
        config.setMailServerUsername("test");
        config.setMailServerPassword("test");
//        config.setMailServerUseSSL(useSSL);
//        config.setMailServerUseTLS(useTLS)
        return config;
    }

    @Bean
    public GroupEntityManager eccoGroupManager() {
        return new EccoGroupManager(userManagementService);
    }

    private <T extends Session> SessionFactory genericSessionFactory(final Class<? super T> type, final T manager) {
        return new SessionFactory() {
            @Override
            public Class<?> getSessionType() {
                return type;
            }

            @Override
            public Session openSession() {
                return manager;
            }
        };
    }

    @Bean
    public ProcessEngineFactoryBean processEngine() throws IOException {
        ProcessEngineFactoryBean processEngineFactoryBean = new ProcessEngineFactoryBean();
        processEngineFactoryBean.setProcessEngineConfiguration(processEngineConfiguration());
        return processEngineFactoryBean;
    }

    @Bean
    public RepositoryService activitiRepositoryService() throws Exception {
        return processEngine().getObject().getRepositoryService();
    }

    @Bean
    public RuntimeService activitiRuntimeService() throws Exception {
        return processEngine().getObject().getRuntimeService();
    }

    @Bean
    public TaskService activitiTaskService() throws Exception {
        return processEngine().getObject().getTaskService();
    }

    @Bean
    public HistoryService activitiHistoryService() throws Exception {
        return processEngine().getObject().getHistoryService();
    }

    @Bean
    public ManagementService activitiManagementService() throws Exception {
        return processEngine().getObject().getManagementService();
    }

    @Bean
    public ExtendedFormService activitiFormService() {
        return new ExtendedFormServiceImpl();
    }



    @Bean
    @Scope(BeanDefinition.SCOPE_PROTOTYPE)
    DateTime now() {
        return new DateTime();
    }

/*
    @Bean
    public ActivitiRule activitiRule() throws Exception {
        return new ActivitiRule(processEngine().getObject());
    }
*/
}
