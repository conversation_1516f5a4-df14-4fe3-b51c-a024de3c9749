<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="demo-ocs-domcare" isExecutable="true">
        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <!-- ** IMPORTANT: Update this expression with the ordered list when you add tasks to the workflow ** -->
        <serviceTask id="setTasksToShowClientView" name="set tasksToShowClientView"
            activiti:resultVariable="tasksToShowClientView"
            activiti:expression="emergencyDetails,needsReduction"/>

        <serviceTask id="setTasksToShowRestricted" name="set tasksToShowRestricted"
            activiti:resultVariable="tasksToShowRestricted"
            activiti:expression=
            "dataProtection,emergencyDetails,from,referralDetails,pendingStatus,referralAccepted,assessmentDate,staff notes,needsAssessment,threatAssessmentReduction,assessmentAccepted,start,agreementOfAppointments,needsReduction,rotaVisit,scheduleReviews,needsAssessmentReductionReview,feedback,close"/>

        <userTask id="dataProtection" name="dataProtection" activiti:candidateGroups="admin" activiti:formKey="flow:/dataProtection">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="emergencyDetails" name="emergencyDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/emergencyDetails"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin"
                activiti:formKey="flow:/pendingStatus"/>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin"
                activiti:formKey="flow:/assessmentDate"/>

        <userTask id="agreementOfAppointments" name="agreementOfAppointments" activiti:candidateGroups="admin"
                activiti:formKey="entitiesByReferralId:agreementOfAppointments"/>

        <userTask id="funding" name="funding" activiti:candidateGroups="admin" activiti:formKey="flow:/funding"/>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/decideFinal"/>

        <userTask id="start" name="start" activiti:candidateGroups="admin"
                activiti:formKey="flow:/start"/>

        <userTask id="staffNotes" name="staff notes" activiti:candidateGroups="admin"
                activiti:formKey="generic:/needsReduction" >
            <extensionElements>
                <activiti:formProperty id="titleRaw" expression="staff notes" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="showActions" expression="none" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="none" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type" writable="false"/>
                <activiti:formProperty id="showOverviewComponents" expression="desc" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <!-- sourcePageGroup is an integer id that evidence is saved against, such that the history of this work
                     can be shown, thus allowing multiple work streams (e.g. multiple needs assessments/threat
                     assessment etc to have their own evidence trail -->
                <activiti:formProperty id="sourcePage" expression="94" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="94" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsAssessment" name="needsAssessment" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessment" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessment" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,status" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="27" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsReduction" name="needsReduction" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsReduction" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="relevant" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="relevant" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="showRiskManagementRequired,type,minutesSpent" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="19" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="rotaVisit" name="rotaVisit" activiti:candidateGroups="admin"
            activiti:formKey="generic:/rotaVisit" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.rotaVisit" writable="false"/>
                <activiti:formProperty id="actAs" expression="reduction" writable="false"/>
                <activiti:formProperty id="tookPlaceOn" expression="dateTime" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="relevant" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="relevant" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="showRiskManagementRequired,mileage,minutesSpent" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="101" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="threatAssessmentReduction" name="threatAssessmentReduction" activiti:candidateGroups="admin"
            activiti:formKey="generic:/threatAssessmentReduction">
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.threatAssessmentReduction" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status,riskMatrix" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomeComponents" expression="triggerControl" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="showRiskManagementDealsWith,type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showSupportActions" expression="n" writable="false"/>
                <activiti:formProperty id="showFlags" expression="y" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="28" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="28" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="scheduleReviews" name="scheduleReviews" activiti:candidateGroups="admin" activiti:formKey="flow:/scheduleReviews">
            <extensionElements>
                <activiti:formProperty id="reviewSchedule" expression="6m" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="needsAssessmentReductionReview" name="needsAssessmentReductionReview" activiti:candidateGroups="admin"
            activiti:formKey="generic:/needsAssessmentReductionReview" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.needsAssessmentReductionReview" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction,review" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
                <activiti:formProperty id="changesByOutcome" expression="y" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="showActions" expression="all" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="byOutcome" writable="false"/>
                <activiti:formProperty id="showWorkComponents" expression="review" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="minutesSpent,type,showRiskManagementRequired" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, spidergraph, overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="54" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="feedback" name="feedback" activiti:candidateGroups="admin"
            activiti:formKey="generic:/generalQuestionnaire" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.feedbackQuestionniare" writable="false"/>
                <activiti:formProperty id="actAs" expression="questions" writable="false"/>
                <activiti:formProperty id="showMenus" expression="overview, spidergraph" writable="false"/>
                <activiti:formProperty id="questions" expression="motivation to change,life skills,finance,social,drug and alcohol,physical health,emotional health,training/work,tenancy/home,offending" writable="false"/>
                <activiti:formProperty id="validateComment" expression="allowNullComment" writable="false"/>
                <activiti:formProperty id="commentLocation" expression="bottom" writable="false"/>
                <activiti:formProperty id="questionnaireAsBlank" expression="y" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="86" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="86" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="admin" activiti:formKey="flow:/close"/>




        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_setTasksToShowClientView" sourceRef="theStart"                 targetRef="setTasksToShowClientView"/>
        <sequenceFlow id="to_setTasksToShowRestricted" sourceRef="setTasksToShowClientView" targetRef="setTasksToShowRestricted"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="setTasksToShowRestricted" targetRef="loadReferral"/>

        <sequenceFlow id="to_dataProtection"     sourceRef="loadReferral"     targetRef="dataProtection"/>
        <sequenceFlow id="to_emergencyDetails"   sourceRef="dataProtection"     targetRef="emergencyDetails"/>
        <sequenceFlow id="from_emergencyDetails" sourceRef="emergencyDetails" targetRef="fork1"/>

        <parallelGateway id="fork1"/>

            <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
            <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

            <sequenceFlow id="to_referralDetails"   sourceRef="fork1" targetRef="referralDetails"/>
            <sequenceFlow id="from_referralDetails" sourceRef="referralDetails" targetRef="join1"/>

            <sequenceFlow id="to_pendingStatus"   sourceRef="fork1" targetRef="pendingStatus"/>
            <sequenceFlow id="from_pendingStatus" sourceRef="pendingStatus" targetRef="join1"/>

            <sequenceFlow id="to_referralAccepted"   sourceRef="fork1" targetRef="referralAccepted"/>
            <sequenceFlow id="from_referralAccepted" sourceRef="referralAccepted" targetRef="join1"/>

            <sequenceFlow id="to_assessmentDate"       sourceRef="fork1" targetRef="assessmentDate"/>
            <sequenceFlow id="from_assessmentDate"     sourceRef="assessmentDate" targetRef="join1"/>

            <sequenceFlow id="to_needsAssessment"    sourceRef="fork1" targetRef="needsAssessment"/>
            <sequenceFlow id="from_needsAssessment"  sourceRef="needsAssessment" targetRef="join1"/>

            <sequenceFlow id="to_threatAssessmentReduction"    sourceRef="fork1" targetRef="threatAssessmentReduction"/>
            <sequenceFlow id="from_threatAssessmentReduction"  sourceRef="threatAssessmentReduction" targetRef="join1"/>

            <sequenceFlow id="to_funding"       sourceRef="fork1" targetRef="funding"/>
            <sequenceFlow id="from_funding"     sourceRef="funding" targetRef="join1"/>

            <sequenceFlow id="to_staffNotes"       sourceRef="fork1" targetRef="staffNotes"/>
            <sequenceFlow id="from_staffNotes"     sourceRef="staffNotes" targetRef="join1"/>

            <sequenceFlow id="to_decideFinal"       sourceRef="fork1" targetRef="decideFinal"/>
            <sequenceFlow id="from_decideFinal"     sourceRef="decideFinal" targetRef="join1"/>

            <sequenceFlow id="to_start"       sourceRef="fork1" targetRef="start"/>
            <sequenceFlow id="from_start"     sourceRef="start" targetRef="fork2"/>

            <parallelGateway id="fork2"/>
                <sequenceFlow id="to_agreementOfAppointments" sourceRef="fork2" targetRef="agreementOfAppointments"/>
                <sequenceFlow id="from_agreementOfAppointments" sourceRef="agreementOfAppointments" targetRef="join2"/>

                <sequenceFlow id="to_needsReduction"   sourceRef="fork2" targetRef="needsReduction"/>
                <sequenceFlow id="from_needsReduction"   sourceRef="needsReduction" targetRef="join2"/>

                <sequenceFlow id="to_rotaVisit"          sourceRef="fork2" targetRef="rotaVisit"/>
                <sequenceFlow id="from_rotaVisit"        sourceRef="rotaVisit" targetRef="join2"/>

                <sequenceFlow id="to_scheduleReviews"    sourceRef="fork2" targetRef="scheduleReviews"/>
                <sequenceFlow id="from_scheduleReviews"  sourceRef="scheduleReviews" targetRef="needsAssessmentReductionReview"/>
                <sequenceFlow id="from_needsAssessmentReductionReview" sourceRef="needsAssessmentReductionReview" targetRef="join2"/>

                <sequenceFlow id="to_closeReferral" sourceRef="fork2" targetRef="closeReferral"/>
                <sequenceFlow id="from_closeReferral" sourceRef="closeReferral" targetRef="join2"/>

                <sequenceFlow id="to_feedback"     sourceRef="fork2" targetRef="feedback"/>
                <sequenceFlow id="from_feedback"   sourceRef="feedback" targetRef="join2"/>

            <parallelGateway id="join2"/>

        <!-- end parallel tasks -->
        <parallelGateway id="join1"/>

        <sequenceFlow id="to_theEnd" sourceRef="closeReferral" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
