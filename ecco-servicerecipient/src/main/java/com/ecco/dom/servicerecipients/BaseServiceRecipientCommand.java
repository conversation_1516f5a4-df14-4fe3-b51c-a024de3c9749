package com.ecco.dom.servicerecipients;

import com.ecco.infrastructure.dom.BaseLongKeyedCommand;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import java.util.UUID;

@MappedSuperclass
public abstract class BaseServiceRecipientCommand extends BaseLongKeyedCommand {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceRecipientId", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE) // because since Hib 5.4 -> 5.6 fetching the command triggers this despite @Lazy
    private BaseServiceRecipient serviceRecipient;

    @Column
    private int serviceRecipientId;

    /** Make this available to pass out as discriminiator at client end */
    @Column(name = "commandname", insertable = false, updatable = false)
    private String commandName;

    protected BaseServiceRecipientCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                          long userId, @NonNull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body);
        this.serviceRecipientId = serviceRecipientId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected BaseServiceRecipientCommand() {
        super();
    }

    public int getServiceRecipientId() {
        return serviceRecipientId;
    }

    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    @Override
    public String getCommandName() {
        return commandName;
    }
}
