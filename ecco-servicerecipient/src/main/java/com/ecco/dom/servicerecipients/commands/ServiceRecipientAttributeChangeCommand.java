package com.ecco.dom.servicerecipients.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("attributeChange")
public class ServiceRecipientAttributeChangeCommand extends ServiceRecipientCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceRecipientAttributeChangeCommand() {
        super();
    }

    public ServiceRecipientAttributeChangeCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                                  long userId, @NonNull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
    }

}
