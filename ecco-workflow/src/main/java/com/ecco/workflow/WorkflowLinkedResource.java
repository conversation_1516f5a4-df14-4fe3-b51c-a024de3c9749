package com.ecco.workflow;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.ProxyDtoBuilderProxy;

import java.io.Serializable;
import java.net.URI;

/**
 * Representation of a linked resource. Modelled after Atom (http://tools.ietf.org/html/rfc4287).
 */
public interface WorkflowLinkedResource extends Serializable, BuildableDto<WorkflowLinkedResource> {
    /** The title of the linked resource for display purposes. */
    String getTitle();
    /** A link relation (preferably from http://www.iana.org/assignments/link-relations/link-relations.xhtml). */
    String getRel();
    /** The URI to the resource. */
    URI getUri();
    /** The optional, advisory media type of the resource, or null if not relevant. */
    String getMediaType();

    interface Builder extends DtoBuilder<WorkflowLinkedResource> {
        Builder title(String title);
        Builder rel(String rel);
        Builder uri(URI uri);
        Builder mediaType(String mediaType);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, WorkflowLinkedResource.class);
        }
    }

}
