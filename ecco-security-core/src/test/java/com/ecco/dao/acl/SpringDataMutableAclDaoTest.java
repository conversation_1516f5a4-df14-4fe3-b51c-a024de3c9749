package com.ecco.dao.acl;

import com.ecco.security.dao.acl.*;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.security.acls.jdbc.LookupStrategy;
import org.springframework.security.acls.model.AclCache;

import static org.junit.Assert.fail;

public class SpringDataMutableAclDaoTest {
    private final LookupStrategy lookupStrategy = Mockito.mock(LookupStrategy.class);
    private final AclObjectIdentityRepository objectIdentityRepository = Mockito.mock(AclObjectIdentityRepository.class);
    private final AclCache aclCache = Mockito.mock(AclCache.class);
    private final AclSecurityIdentityRepository securityIdentityRepository = Mockito.mock(AclSecurityIdentityRepository.class);
    private final AclClassRepository classRepository = Mockito.mock(AclClassRepository.class);
    private final AclEntryRepository entryRepository = Mockito.mock(AclEntryRepository.class);

    @Test
    public void constructorRejectsNullParameters() throws Exception {
        try {
            new SpringDataMutableAclDao(null, classRepository, objectIdentityRepository, entryRepository, lookupStrategy, aclCache);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

        try {
            new SpringDataMutableAclDao(securityIdentityRepository, null, objectIdentityRepository, entryRepository, lookupStrategy, aclCache);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

        try {
            new SpringDataMutableAclDao(securityIdentityRepository, classRepository, null, entryRepository, lookupStrategy, aclCache);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

        try {
            new SpringDataMutableAclDao(securityIdentityRepository, classRepository, objectIdentityRepository, null, lookupStrategy, aclCache);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

        try {
            new SpringDataMutableAclDao(securityIdentityRepository, classRepository, objectIdentityRepository, entryRepository, null, aclCache);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

        try {
            new SpringDataMutableAclDao(securityIdentityRepository, classRepository, objectIdentityRepository, entryRepository, lookupStrategy, null);
            fail("It should have thrown IllegalArgumentException");
        }
        catch (IllegalArgumentException expected) {
        }

    }

}
