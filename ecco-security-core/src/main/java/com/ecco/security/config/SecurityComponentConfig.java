package com.ecco.security.config;

import com.azure.spring.autoconfigure.aad.AADAuthenticationFilterAutoConfiguration;
import com.azure.spring.autoconfigure.aad.AADAuthenticationProperties;
import com.ecco.infrastructure.config.YamlPropertyLoaderFactory;
import com.ecco.security.authentication.MagicTokenAuthenticationProvider;
import com.ecco.security.dao.CustomSpringJdbcUserDetailsManager;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.LdapGroupMappingRepository;
import com.ecco.security.repositories.MagicTokenRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.security.service.*;
import com.ecco.security.acl.AclHandler;
import com.ecco.security.web.filter.MagicTokenAuthenticationFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.*;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.LoggerListener;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.web.authentication.*;
import org.springframework.security.web.authentication.session.ChangeSessionIdAuthenticationStrategy;
import org.springframework.security.web.authentication.switchuser.SwitchUserFilter;
import org.springframework.security.web.header.HeaderWriter;
import org.springframework.security.web.header.writers.CacheControlHeadersWriter;
import org.springframework.security.web.header.writers.DelegatingRequestMatcherHeaderWriter;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SimpleSavedRequest;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.NegatedRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.util.List;

import static java.util.Collections.singletonList;

/**
 * Global security configuration of filters, listeners and providers - migrating from applicationContext-security.
 *
 * Used in ecco-web-api WebAppSecurityConfigurer, ecco-war applicationContext-security.xml and common med-security WebSecurityConfigurer.
 */
@Configuration
@Import({
        SecurityConfig.class,
        AclConfig.class,
        AADAuthenticationFilterAutoConfiguration.class,
        AADComponentConfig.class
})
@PropertySource(value = "classpath:/azure-scopes.yml", factory = YamlPropertyLoaderFactory.class)
// yaml needs a factory: https://www.baeldung.com/spring-yaml-propertysource
@PropertySources({
        @PropertySource(value = "classpath:/azure-scopes.yml", factory = YamlPropertyLoaderFactory.class),
        // see https://docs.spring.io/spring-framework/docs/current/javadoc-api/org/springframework/context/annotation/PropertySource.html
        // and https://stackoverflow.com/a/30785083
        // eg -Doverride.location=file:/..linux.., or file:/C//..<windows>.., possibly file:///
        // or override each setting with -D
        @PropertySource(value="${azure.scopes.location}", ignoreResourceNotFound = true, factory = YamlPropertyLoaderFactory.class)
})
@Slf4j
public class SecurityComponentConfig {

    private final AADAuthenticationProperties properties;

    @Value("${azure.activedirectory.user-group.prefix:#{null}}")
    private String azureGroupPrefix;

    public SecurityComponentConfig(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection") AADAuthenticationProperties properties) {
        this.properties = properties;
    }

    /** logs authentication-related application events */
    @Bean
    public LoggerListener loggerListener() {
        return new LoggerListener();
    }

    @Bean
    public org.springframework.security.access.event.LoggerListener accessLoggerListener() {
        return new org.springframework.security.access.event.LoggerListener();
    }

    /**
     * A RequestCache that resolves the referer if we're caching requests that would hit the API
     */
    @Bean
    public RequestCache refererRequestCache() {
        return new HttpSessionRequestCache() {
            @Override
            public void saveRequest(HttpServletRequest request, HttpServletResponse response) {
                log.info("Saving to RequestCache: uri: {}, referrer: {}", request.getRequestURI(), request.getHeader("referer"));
                if (request.getRequestURI().contains(request.getContextPath() + "/api/")) {
                    // User referer header if we hit the API and got redirected for auth
                    String referrer = request.getHeader("referer");
                    if (referrer != null) {
                        request.getSession().setAttribute("SPRING_SECURITY_SAVED_REQUEST", new SimpleSavedRequest(referrer));
                    }
                }
                else {
                    super.saveRequest(request, response);
                }
            }
        };
    }

    @Bean
    public EccoUserDetailsContextMapper eccoUserDetailsContextMapper(LdapGroupMappingRepository groupMappingRepository,
                                                                     UserManagementService userManagementService,
                                                                     Boolean enableAcls,
                                                                     AclHandler aclHandler) {
        return new EccoUserDetailsContextMapper(groupMappingRepository, userManagementService, enableAcls, aclHandler);
    }

    @Bean
    OAuth2UserService<OidcUserRequest, OidcUser> eccoEnhancingOidcUserService(EccoUserDetailsContextMapper userDetailsContextMapper,
                                                                              UserRepository userRepository,
                                                                              UserManagementService userManagementService) {
        return new EccoEnhancingOidcUserService(properties, userDetailsContextMapper, userRepository, userManagementService, azureGroupPrefix);
    }

    /**
     *
     * @param additionalMessageSource
     *  if we wanted, we could change the original message source, which would help if we wanted to use the original scriptlet login page
     */
    @Bean
    public CustomDaoAuthenticationProvider eccoAuthenticationProvider(
            PasswordEncoder passwordEncoder,
            @Qualifier("messageSource") MessageSource additionalMessageSource,
            UserManagementService userManagementService) {
        AuthenticationCheck<User, UsernamePasswordAuthenticationToken> authenticationCheck = AuthenticationCheck.all(
                new IpNotBlocklistedAuthenticationCheck(additionalMessageSource, userManagementService),
                new AccountNotLockedAuthenticationCheck(additionalMessageSource),
                new UsernamePasswordAuthenticationCheck(additionalMessageSource, userManagementService)
        );
        return new CustomDaoAuthenticationProvider(passwordEncoder, userManagementService, authenticationCheck);
    }

    @Bean
    public CustomSpringJdbcUserDetailsManager customSpringJdbcUserDetailsManager(DataSource dataSource) {
        var authManager = new CustomSpringJdbcUserDetailsManager();
        authManager.setEnableAuthorities(false);
        authManager.setEnableGroups(true);
        authManager.setDataSource(dataSource);
        return authManager;
    }

    @Bean
    public MagicTokenAuthenticationProvider magicTokenAuthenticationProvider(
            @Qualifier("messageSource") MessageSource messageSource,
            UserManagementService userManagementService,
            MagicTokenRepository magicTokenRepository
    ) {
        // Note: We deliberately don't check if the User account is locked
        // when logging in via a Magic Token.
        // We create a special-purpose User account for each Magic Token.
        // We probably should lock those accounts so that they can't be
        // logged into with a password, but we still want the Magic Token
        // to work as long as it has not expired or been revoked.

        var authenticationCheck = new IpNotBlocklistedAuthenticationCheck(messageSource, userManagementService);
        return new MagicTokenAuthenticationProvider(magicTokenRepository, authenticationCheck);
    }

    /*
     * NOTE: This is not how OFFLINE login is done, so that won't redirect - only works if offline not installed
     * if none then check the useReferer, and if not, use the default page
     * we did have a custom method thinking spring-security-redirect in the url was a xss risk - but its not needed: http://forum.springsource.org/showthread.php?p=337451#post337451
     * since hdiv, entry to random parts in the system have no state - so we set alwaysUseDefault...
     */
    // NB For website-based access only.
    @Bean
    @Qualifier("webSuccessHandler")
    public WebAuthenticationSuccessHandler webSuccessHandler() {
        WebAuthenticationSuccessHandler handler = new WebAuthenticationSuccessHandler();
        handler.setDefaultTargetUrl("/nav/r/welcome/");
        handler.setTargetUrlParameter("security-redirect"); // for logout success - used in main_head_jsbase.jsp (i.e. logout and then goto the url specified)
        // alwaysUseDefaultTargetUrl is disabled to allow targetUrlParameter to be specified
        return handler;
    }

    /**
     * Authentication failure needs to redirect since there could be many pages between (eg oauth).
     * However, the redirect page depends on whether we are the app or browser-based access.
     * This can be determined by knowing whether the saved request contains the servlet context or not.
     */
    @Bean
    public WebAuthenticationFailureHandler webFailureHandler() {
        return new WebAuthenticationFailureHandler();
    }

    /* Adam's notes from XML config:
     * exposed bean to use as a reference for loginEntryPoint
     * have to set this when using a custom authentication processing filter
     * also block out form-login, session-fixation-protection="migrateSession" from http
     * also add entry-point-ref="authenticationProcessingFilterEntryPoint" to http
     * also enable the authenticationManager
     * <security:form-login always-use-default-target="false" login-processing-url="/nav/secure/j_acegi_security_check" login-page="/nav/secure/login.html"
        authentication-failure-url="/nav/secure/login.html?login_error=1" default-target-url="/nav/r/welcome/" />
     */

    /**
     * Form-based login url.
     * "Used by the ExceptionTranslationFilter to commence a form login authentication via the UsernamePasswordAuthenticationFilter"
     */
    @Bean
    public LoginUrlAuthenticationEntryPoint authenticationProcessingFilterEntryPoint() {
        return new LoginUrlAuthenticationEntryPoint("/nav/secure/login.html"); // Can remove??
    }
    @Bean
    public UsernamePasswordAuthenticationFilter authenticationProcessingFilter(AuthenticationManager authenticationManager) {
        UsernamePasswordAuthenticationFilter filter = new UsernamePasswordAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManager);
        filter.setUsernameParameter("j_username");
        filter.setPasswordParameter("j_password");
        filter.setSessionAuthenticationStrategy(new ChangeSessionIdAuthenticationStrategy());
        // form based authentication requires MFA
        //MfaPassthruAuthenticationHandler handler = new MfaPassthruAuthenticationHandler("/nav/mfa/validate");
        //filter.setAuthenticationSuccessHandler(handler);
        //filter.setAuthenticationFailureHandler(handler);
//        filter.setAuthenticationSuccessHandler(authenticationSuccessHandler());
//        filter.setAuthenticationFailureHandler(authenticationFailureHandler());
        filter.setAllowSessionCreation(true);
        //filter.setAuthenticationDetailsSource();
        filter.setRequiresAuthenticationRequestMatcher(
                new AntPathRequestMatcher("/nav/secure/j_acegi_security_check"));
        return filter;
    }

    @Bean
    public SwitchUserFilter switchUserProcessingFilter(UserDetailsService userManagementService) {
        SwitchUserFilter filter = new SwitchUserFilter();
        filter.setUserDetailsService(userManagementService);
        filter.setSwitchUserUrl("/nav/secure/admin/switchUser");
        filter.setExitUserUrl("/nav/secure/admin/switchUserExit");
        filter.setTargetUrl("/nav/r/welcome/");
        return filter;
    }

    @Bean
    public MagicTokenAuthenticationFilter magicTokenAuthenticationFilter(AuthenticationManager authenticationManager) {
        return new MagicTokenAuthenticationFilter(authenticationManager);
    }

    @Bean
    public HeaderWriter selectiveCacheControlHeaderWriter() {

        RequestMatcher paths = new OrRequestMatcher(
                new AntPathRequestMatcher("/r/**"),
                new AntPathRequestMatcher("/api/**")
        );

        return new DelegatingRequestMatcherHeaderWriter(
                new NegatedRequestMatcher(paths),
                new CacheControlHeadersWriter());
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(singletonList("*"));
        configuration.setAllowedMethods(List.of("GET", "POST", "OPTIONS", "DELETE", "PATCH"));
        configuration.setAllowedHeaders(List.of("X-Requested-With", "Origin", "Content-Type", "Accept", "Authorization", "Content-Length", "Cookie"));
        configuration.setAllowCredentials(true);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
