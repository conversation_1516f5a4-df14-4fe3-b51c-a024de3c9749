package com.ecco.security.web;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.web.util.matcher.RequestMatcher;


/**
 * A matcher which delegates to the normal form-based authentication provider if <PERSON>rber<PERSON> is disabled.
 * The matchers are processed whether Kerber<PERSON> is enabled or not so it helps clarity to provide a matcher
 * which avoids kerberos altogether when disabled.
 */
public class KerberosDisabledRequestMatcher implements RequestMatcher {

    private final Logger log = LoggerFactory.getLogger(getClass());

    private final String name;

    public KerberosDisabledRequestMatcher(String name) {
        this.name = name;
    }

    @PostConstruct
    public void configure() throws Exception {
        log.info("Kerberos ENABLED: this 'matcher' will ALLOW further matchers to apply spnego negotiate header");
    }

    @Override
    public boolean matches(HttpServletRequest request) {
        // if spnego is disabled, go to the form based authentication
        //if (!enabled) {
        //    return true;
        //}

        // if spnego is enabled, don't go to the form based authentication
        log.debug("{} entered - skipping to allow spnego 'Negotiate ' to be applied through later rules", name);
        return false;
    }

}
