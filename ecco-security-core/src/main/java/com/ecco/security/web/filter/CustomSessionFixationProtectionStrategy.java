package com.ecco.security.web.filter;

import java.util.Map;

import javax.servlet.http.HttpSession;

import org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy;

/**
 * Removes attributes such as "clickstream" from extractAttributes for cases where propagating those causes problems.
 */
public class CustomSessionFixationProtectionStrategy extends SessionFixationProtectionStrategy {

    private String[] omitAttributes;


    public CustomSessionFixationProtectionStrategy omittingAttributes(String... attributeNames) {
        this.omitAttributes = attributeNames;
        return this;
    }

    @Override
    protected Map<String, Object> extractAttributes(HttpSession session) {
        Map<String, Object> attributes = super.extractAttributes(session);
        if (attributes != null && omitAttributes != null) {
            for (String attribute : omitAttributes) {
                attributes.remove(attribute);
            }
        }
        return attributes;
    }
}
