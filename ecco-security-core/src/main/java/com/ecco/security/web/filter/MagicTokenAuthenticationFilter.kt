package com.ecco.security.web.filter

import com.ecco.security.authentication.MagicTokenAuthenticationToken
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.core.Authentication
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

class MagicTokenAuthenticationFilter
@JvmOverloads
constructor(
    authenticationManager: AuthenticationManager,
    private val requestMatcher: RequestMatcher = defaultRequestMatcher,
) : AbstractAuthenticationProcessingFilter(requestMatcher, authenticationManager) {
    companion object {
        private val defaultRequestMatcher = AntPathRequestMatcher("/magic/{credentials}", "GET")
    }

    init {
        val leadingSlash = "^/*".toRegex()
        setAuthenticationSuccessHandler { request, response, authentication ->
            if (authentication is MagicTokenAuthenticationToken && response != null) {
                response.sendRedirect(
                    "${request.contextPath}/${
                        authentication.targetPath?.replace(
                            leadingSlash,
                            "",
                        ) ?: ""
                    }",
                )
            } else {
                // This should never happen
                throw IllegalStateException()
            }
        }
    }

    override fun attemptAuthentication(request: HttpServletRequest?, response: HttpServletResponse?): Authentication {
        val credentials = requestMatcher.matcher(request).variables["credentials"]
        if (credentials == null) {
            throw BadCredentialsException("Magic token is missing")
        } else {
            return authenticationManager.authenticate(MagicTokenAuthenticationToken.unauthenticated(credentials))
        }
    }
}