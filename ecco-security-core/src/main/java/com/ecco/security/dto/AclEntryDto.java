package com.ecco.security.dto;

import org.springframework.security.acls.model.Permission;

/**
 * A class to provide a more sane representation of acl permissions.
 * @see org.springframework.security.acls.model.AccessControlEntry
 *
 */
public class AclEntryDto {

    public final String username;
    //public String userDisplayName;
    public final long secureObjectId;
    public final Class clazz;
    public final Permission permission;
    // isGranting - only really useful to specify false when using inheritance

    public AclEntryDto(String username, long secureObjectId, Class clazz, Permission permission) {
        this.username = username;
        this.secureObjectId = secureObjectId;
        this.clazz = clazz;
        this.permission = permission;
    }

    public long getSecureObjectId() {
        return secureObjectId;
    }

    public Class getClazz() {
        return clazz;
    }

    public Permission getPermission() {
        return permission;
    }

    public String getUsername() {
        return username;
    }

    @Override
    public String toString() {
        return "[" + username + ": " + clazz + ": " + secureObjectId + " " + permission+ "] ";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((clazz == null) ? 0 : clazz.hashCode());
        result = prime * result + ((permission == null) ? 0 : permission.hashCode());
        result = prime * result + (int) (secureObjectId ^ (secureObjectId >>> 32));
        result = prime * result + ((username == null) ? 0 : username.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        AclEntryDto other = (AclEntryDto) obj;
        if (clazz == null) {
            if (other.clazz != null)
                return false;
        } else if (!clazz.equals(other.clazz))
            return false;
        if (permission == null) {
            if (other.permission != null)
                return false;
        } else if (!permission.equals(other.permission))
            return false;
        if (secureObjectId != other.secureObjectId)
            return false;
        if (username == null) {
            if (other.username != null)
                return false;
        } else if (!username.equals(other.username))
            return false;
        return true;
    }

}
