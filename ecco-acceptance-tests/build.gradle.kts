import org.siouan.frontendgradleplugin.infrastructure.gradle.InstallNodeTask
import org.siouan.frontendgradleplugin.infrastructure.gradle.InstallPackageManagerTask
import org.siouan.frontendgradleplugin.infrastructure.gradle.ResolvePackageManagerTask

plugins {
    id("org.siouan.frontend-jdk17") version "8.0.0"
}

dependencies {
    implementation(project(":ecco-data-client"))
    implementation(project(":ecco-web-api"))
    implementation("args4j:args4j:2.0.16")
    implementation("org.apache.commons:commons-lang3")
    implementation("commons-io:commons-io")
    implementation("org.seleniumhq.selenium:selenium-leg-rc:3.141.59")
    implementation("org.springframework:spring-core")
    implementation("org.springframework:spring-web")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jdk8")
    implementation("com.google.code.gson:gson:2.12.1")
    implementation("com.jayway.jsonpath:json-path")
    implementation("joda-time:joda-time:2.10.8")
    testImplementation(project(":test-support"))
    testImplementation("org.seleniumhq.selenium:selenium-java")
    testImplementation("org.jetbrains.kotlin:kotlin-test")
    testImplementation("com.google.guava:guava")

    // JUnit 5 dependencies
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
}

tasks.withType<Test> {
    systemProperty("httpPort", "8899")
    systemProperty("target.host.url", "http://localhost:8899/")
    filter {
        excludeTestsMatching("*.acceptancetests.ui.*.*Test*")
        if (!project.hasProperty("test.api") || project.hasProperty("skipITs")) {
            excludeTestsMatching("*.acceptancetests.api.*.*Test*")
        }
    }
    enabled = !project.hasProperty("skipITs")
    useJUnitPlatform() // Use JUnit Platform to support both JUnit 4 (via vintage) and JUnit 5
}

val runFrontend = project.hasProperty("test.ui") &&
    !project.hasProperty("skipITs") &&
    !project.hasProperty("skipNode")

frontend {
    nodeVersion = "18.20.8"
    packageJsonDirectory = rootProject.layout.projectDirectory
    nodeInstallDirectory = rootProject.layout.projectDirectory.dir(".node")
    assembleScript = null
    checkScript = if (runFrontend) "e2e" else null
    installScript = if (runFrontend) "install" else null
    verboseModeEnabled = true
}

tasks.withType<InstallNodeTask> {
    enabled = runFrontend
}
tasks.withType<ResolvePackageManagerTask> {
    enabled = runFrontend
}
tasks.withType<InstallPackageManagerTask> {
    enabled = runFrontend
}

description = "ecco-acceptance-tests"