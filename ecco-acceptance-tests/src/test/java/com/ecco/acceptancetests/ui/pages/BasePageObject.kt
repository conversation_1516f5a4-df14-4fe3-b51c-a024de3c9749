package com.ecco.acceptancetests.ui.pages

import asl.fm.last.commons.RetryTemplate
import com.ecco.data.client.WebApiSettings
import org.apache.commons.lang3.ArrayUtils
import org.eeichinger.testing.web.Browser
import org.eeichinger.testing.web.Settings
import org.hamcrest.Matcher
import org.hamcrest.Matchers.startsWith
import org.joda.time.DateTime
import org.joda.time.LocalDate
import org.joda.time.LocalTime
import org.joda.time.format.DateTimeFormat
import org.junit.Assert.fail
import org.openqa.selenium.By
import org.openqa.selenium.InvalidElementStateException
import org.openqa.selenium.JavascriptExecutor
import org.openqa.selenium.Keys
import org.openqa.selenium.NoSuchElementException
import org.openqa.selenium.WebDriver
import org.openqa.selenium.WebDriverException
import org.openqa.selenium.WebElement
import org.openqa.selenium.support.ui.ExpectedCondition
import org.openqa.selenium.support.ui.ExpectedConditions
import org.openqa.selenium.support.ui.FluentWait
import org.openqa.selenium.support.ui.Select
import org.openqa.selenium.support.ui.WebDriverWait
import org.slf4j.LoggerFactory
import org.springframework.util.StringUtils.hasText
import java.util.*

abstract class BasePageObject protected constructor(pageURL: String?, protected val webDriver: WebDriver) {
    private val log = LoggerFactory.getLogger(javaClass)
    private val pageUrl = WebApiSettings.APPLICATION_URL + pageURL
    protected val pageHeader: PageHeader = PageHeader(webDriver)
    protected val pageFooter: PageFooter = PageFooter(webDriver)

    /**
     * Navigate to the supplied url for this page. i.e. this page is an entry point, not the result
     * of navigating via a link/action.
     */
    fun navigateAsEntryPoint() {
        webDriver.navigate().to(pageUrl)
    }

    /**
     * @return One of Boolean, Long, String, List or WebElement. Or null.
     */
    private fun executeJavascript(script: String, args: Any? = null): Any? = (webDriver as JavascriptExecutor).executeScript(script, args)

    private fun scrollIntoView(element: WebElement) {
        executeJavascript("arguments[0].scrollIntoView(true);", element)
    }

    protected fun waitForPageLoaded(message: String) {
        try {
            waitForPageLoaded()
        } catch (e: Exception) {
            throw AssertionError("Failed waiting for page load. $message")
        }
    }

    protected fun waitForPageLoaded() {
        val start = DateTime()
        val expectation =
            ExpectedCondition {
                val now = DateTime()
                now.isAfter(start.plus(Settings.WAIT_FOR_PAGE_LOADED_DELAY.toLong())) &&
                    executeJavascript("return document.readyState") == "complete"
            }

        val wait: FluentWait<WebDriver> = WebDriverWait(webDriver, DEFAULT_WAIT_TIMEOUT_SECS.toLong())
        try {
            log.debug("waitForPageLoaded on thread: " + Thread.currentThread().toString())
            wait.until(expectation::apply)
        } catch (error: Throwable) {
            log.debug("waitForPageLoaded timeout on thread: " + Thread.currentThread().toString())
            // carry on regardless as an experiment for build server
            //            fail("Timeout waiting for Page Load Request to complete.");
        }
    }

    /** When all else fails, just (regretfully) put a delay in  */
    protected fun waitFor(millis: Long) {
        try {
            Thread.sleep(millis)
        } catch (error: InterruptedException) {
            // ignore
        }
    }

    /** Use javascript to hide a datepicker that pops up on focus  */
    protected fun hideDateAndTimePicker() {
        try {
            executeJavascript("document.getElementsByClassName('ui-datepicker')[0].style.display='none'")
        } catch (e: WebDriverException) {
            // won't exist for native date controls
        }
    }

    // **** HELPFUL SETTERS ****

    // see http://stackoverflow.com/questions/11337353/correct-way-to-focus-an-element-in-webdriver-using-java
    protected fun setFocus(fieldName: String) {
        findElementSoon(By.name(fieldName)).sendKeys(Keys.SHIFT) // See https://stackoverflow.com/a/50726989/1998186
    }

    protected fun setFocusById(fieldId: String) {
        findElementSoon(By.id(fieldId)).sendKeys(Keys.SHIFT) // See https://stackoverflow.com/a/50726989/1998186
    }

    protected fun setDatePickerField(fieldName: String, date: LocalDate) {
        setDateByElementId(fieldName, date.toDate())
        hideDateAndTimePicker()
    }

    protected fun setTimePickerField(fieldName: String, time: LocalTime) {
        setField(fieldName, time.toString("hh:mm"))
        hideDateAndTimePicker()
    }

    /** Note: for date or time fields where we need to hide the picker afterwards, use
     * [.setDatePickerField]
     */
    protected fun setField(fieldName: String, text: String?) {
        if (hasText(text)) {
            val element = findElementSoon(By.name(fieldName))
            setField(element, text!!)
        }
    }

    protected fun setFieldSoonByXpathSelector(selector: String, text: String) {
        setFieldSoonBy(By.xpath(selector), text)
    }

    private fun setFieldSoonByCssSelector(selector: String, text: String) {
        setFieldSoonBy(By.cssSelector(selector), text)
    }

    protected fun setFieldSoonByName(name: String, text: String) {
        setFieldSoonBy(By.name(name), text)
    }

    private fun setFieldSoonBy(by: By, text: String) {
        if (hasText(text)) {
            val element = findElementSoon(by)
            setField(element, text)
        }
    }

    private fun setField(element: WebElement, text: String) {
        val tagName = element.tagName
        if (tagName.equals("input", ignoreCase = true) || tagName.equals("textarea", ignoreCase = true)) {
            element.clear()
        }
        element.sendKeys(text)
    }

    protected fun setDateXpath(xpath: String, date: Date) {
        setDate(findElementSoon(By.xpath(xpath)), date)
    }

    /** Use this one for JQ datepicker enhanced fields as the hidden field has the name, but the input element has
     * the id
     */
    protected fun setDateByElementId(fieldId: String, date: Date) {
        setDate(findElementSoon(By.id(fieldId)), date)
    }

    protected fun setDateByElementIdIso(fieldId: String, date: Date) {
        setDateIso(findElementSoon(By.id(fieldId)), date)
    }

    protected fun setDate(fieldName: String, date: Date) {
        setDate(findElementSoon(By.name(fieldName)), date)
    }

    private fun setDateIso(element: WebElement, date: Date?) {
        if (date != null) {
            try {
                element.clear()
            } catch (e: InvalidElementStateException) {
                // Ignore clear failure which will happen on Chromium type="date" field
            }

            // User typing keys may work for this, but not for FF: element.sendKeys(DATE_FORMAT.print(date.time))
            // see https://github.com/mozilla/geckodriver/issues/1070
            element.sendKeys(ISO_DATE_FORMAT.print(date.time))
// seems to clear it            element.sendKeys(Keys.TAB) // Should hide it for us so shouldn't need hideDateAndTimePicker();
        }
    }

    private fun setDate(element: WebElement, date: Date?) {
        if (date != null) {
            try {
                element.clear()
            } catch (e: InvalidElementStateException) {
                // Ignore clear failure which will happen on Chromium type="date" field
            }

            element.sendKeys(DATE_FORMAT.print(date.time))
            element.sendKeys(Keys.TAB) // Should hide it for us so shouldn't need hideDateAndTimePicker();
        }
    }

    /** Set date time where we aim to use datetime-local type, but for some browsers we cannot */
    protected fun setDateTime(fieldName: String, dateTime: DateTime) {
        if (Settings.BROWSER == Browser.FIREFOX) {
            // These are simple text fields in Firefox 45 ESR, but will be date and time on modern Firefox
            setField("${fieldName}_date", ISO_DATE_FORMAT.print(dateTime.toLocalDate()))
            setField("${fieldName}_time", TIME_FORMAT.print(dateTime.toLocalTime()))
        } else {
            setField(fieldName, DD_MM_YYYY_HH_MM.print(dateTime))
        }
    }

    /**
     * Set options in a &lt;select ...&gt;
     */
    protected fun setSelection(fieldName: String, optionsToSelect: String?) {
        // TODO: Make this work with react-select

        if (optionsToSelect == null) {
            return
        }

        val toSelect = arrayOf(optionsToSelect)

        var listElement = findElementSoon(By.name(fieldName))

        // For single option, we can use shorter version
        if (toSelect.size == 1) {
            // If it's not visible, check if there's a 'select2' next to it
            if (!listElement.isDisplayed) {
                // Use JQuery to trigger the mouse click with which=1 as it must be a left click for Select2 to trigger
                executeJavascript("$('select[name=$fieldName] + .select2 .select2-selection').trigger($.Event('mousedown', { which: 1 }))")
                // Type into the search field
                setFieldSoonByCssSelector(".select2-search input", toSelect[0])
                // Click the (only) matching result
                clickElementByCssSelector(".select2-results li")
            } else {
                if (!listElement.tagName.equals("select", ignoreCase = true)) {
                    listElement = findElementSoon(By.cssSelector("select[name='$fieldName']"))
                }
                Select(listElement).selectByVisibleText(toSelect[0])
            }
            return
        }

        val elements = listElement.findElements(By.tagName("option"))
        for (element in elements) {
            if (ArrayUtils.contains(toSelect, element.text)) {
                element.click()
            }
        }
    }

    protected fun setRadio(fieldName: String, option: String?) {
        if (hasText(option)) {
            val options = findElementsSoon(By.xpath("//input[@name='$fieldName']"))
            for (element in options) {
                if (element.getAttribute("value") == option) {
                    element.click()
                }
            }
        }
    }

    protected fun setCheckboxes(fieldName: String, options: List<String>?) {
        if (options == null || options.isEmpty()) {
            return
        }

        val elements = findElementsSoon(By.xpath("//input[@name='$fieldName']"))
        for (element in elements) {
            if (options.contains(element.getAttribute("value"))) {
                element.click()
            }
        }
    }

    protected fun setCheckbox(fieldName: String, checked: Boolean) {
        val checkbox = findElementSoon(By.name(fieldName))
        if (checkbox.isSelected != checked) {
            checkbox.click()
        }
    }

    protected fun setCheckboxLabel(text: String, checked: Boolean) {
        val label = findElementSoon(By.xpath("//label[text() = '$text']"))
        val id = label.getAttribute("for")
        if (hasText(id)) {
            setCheckboxId(id, checked)
        } else {
            val checkbox = label.findElement(By.cssSelector("input[type='checkbox']"))
            if (checkbox.isSelected != checked) {
                checkbox.click()
            }
        }
    }

    private fun setCheckboxId(id: String, checked: Boolean) {
        val checkbox = findElementSoon(By.id(id))
        if (checkbox.isSelected != checked) {
            checkbox.click()
        }
    }

    protected fun uploadFile(uploadFieldName: String, filename: String) {
        if (hasText(filename)) {
            // TODO needs to be tested, probably need to get a File() object and do file.getAbsolutePath() ?
            val element = findElementSoon(By.name(uploadFieldName))
            element.clear()
            element.sendKeys(filename)
        }
    }

    /**
     * Finds an element using linkText, and clicks it
     */
    protected fun clickLink(linkText: String) {
        if (hasText(linkText)) {
            try {
                clickElementWithRetry(findElementSoon(By.linkText(linkText)))
            } catch (e: Exception) {
                throw AssertionError("Couldn't click link: '$linkText'")
            }

            waitForPageLoaded("having clicked link: '$linkText'")
        }
    }

    /**
     * Finds an element using partialLinkText as part match of text, and clicks it
     * Useful when link may have other info in it such as a span with an icon
     */
    protected fun clickLinkPartial(partialLinkText: String) {
        if (hasText(partialLinkText)) {
            clickElementWithRetry(findElementSoon(By.partialLinkText(partialLinkText)))
            waitForPageLoaded()
        }
    }

    /**
     * Finds an element using id attribute
     */
    protected fun clickById(id: String) {
        if (hasText(id)) {
            clickElementWithRetry(findElementSoon(By.id(id)))
            waitForPageLoaded()
        }
    }

    protected fun clickByXpath(xpath: String) {
        if (hasText(xpath)) {
            clickElementWithRetry(
                waitForVisibleElement(
                    "Failed to click on element with xpath: $xpath",
                    By.xpath(xpath),
                ),
            )
            waitForPageLoaded()
        }
    }

    /**
     * Finds a link with the specified href, clicks it, and waits for the next page to load
     */
    protected fun clickLinkHref(href: String) {
        if (hasText(href)) {
            clickElementWithRetry(
                waitForVisibleElement(
                    "Failed to click on link with href: $href",
                    By.xpath("//a[@href='$href']"),
                ),
            )
            waitForPageLoaded()
        }
    }

    /**
     * Finds the first element by name (such as a button or submit) then clicks it
     */
    protected fun clickButton(buttonName: String) {
        clickElementWithRetry(
            waitForVisibleElement(
                "Failed to click button named: $buttonName",
                By.name(buttonName),
            ),
        )
        waitForPageLoaded()
    }

    protected fun clickButtonById(buttonId: String) {
        clickElementWithRetry(
            waitForVisibleElement(
                "Failed to click on button with id: $buttonId",
                By.id(buttonId),
            ),
        )
        waitForPageLoaded()
    }

    protected fun clickButtonByText(buttonText: String) {
        clickElementWithRetry(
            waitForVisibleElement(
                "Failed to click button: $buttonText (didn't appear or not visible)",
                By.xpath("//button[.='$buttonText']"),
            ),
        )
        waitForPageLoaded()
    }

    protected fun clickActionButton(text: String) {
        clickButtonById("action-$text")
    }

    protected fun clickMaterialUIButton(buttonText: String) {
        clickByXpath("//span[contains(@class, 'MuiButton-label')][text()='$buttonText']")
    }

    protected fun clickMaterialUIMenuItem(menuText: String) {
        clickByXpath("//div[contains(@class, 'MuiListItemText-root')]/span[text()='$menuText']")
    }

    /**
     * Finds an element (such as a button or submit) using css selector, then clicks it
     */
    protected fun clickElementByCssSelector(selector: String) {
        clickElementWithRetry(
            waitForVisibleElement(
                "Failed to click cssSelector: $selector",
                By.cssSelector(selector),
            ),
        )
        waitForPageLoaded()
    }

    /**
     * Finds an element (such as a button or submit) using css selector, then clicks it
     */
    protected fun clickElementByCssSelector(selector: String, timeoutSecs: Int) {
        clickElementWithRetry(findElementSoon(By.cssSelector(selector), timeoutSecs))
        waitForPageLoaded()
    }

    /**
     * Finds an element (such as a button or submit) using xpath, then clicks it
     */
    protected fun clickElementXpath(xPathExpression: String) {
        clickElementWithRetry(
            waitForVisibleElement(
                "Failed to click xpath: $xPathExpression",
                By.xpath(xPathExpression),
            ),
        )
        waitForPageLoaded()
    }

    /** Click an element with retry */
    fun clickElementWithRetry(element: WebElement) {
        val millisPerLoop = 100
        val limit = 4000 // Snackbar auto-hide is 3000ms
        RetryTemplate(limit / millisPerLoop, millisPerLoop).execute {
            element.click()
        }
    }

    protected fun getTextByCssSelector(selector: String): String =
        findElementSoon(By.cssSelector(selector), "Failed to get text at cssSelector: $selector")!!.text

    protected fun getTextByCssSelector(selector: String, timeoutSecs: Int): String =
        findElementSoon(By.cssSelector(selector), timeoutSecs).text

    protected fun getElementsByCssSelector(selector: String): List<String> {
        val elements = findElementsSoon(By.cssSelector(selector))
        return elements.map({ it.text })
    }

    /** Verify URL is as expected including retrying for a good few seconds to allow for page transitions to complete  */
    open fun verifyIsCurrentPage() {
        verifyCurrentPage(startsWith(pageUrl))
    }

    protected fun verifyCurrentPage(matcher: Matcher<String>) {
        // Wait up to two seconds for page to match - up to 20 secs if we started on apple.com due to
        // WTF when running tests on iOS on SauceLabs... simply cannot launch emulator with our URL.. no .. not on Apple!
        val limit = if (webDriver.currentUrl.startsWith("http://www.apple.com")) 20000 else 4000

        val millisPerLoop = 100
        RetryTemplate(limit / millisPerLoop, millisPerLoop).execute {
            if (matcher.matches(webDriver.currentUrl)) {
                return@execute
            }
            if (webDriver.currentUrl.contains("/referrals/")) {
                return@execute // SKIPPING url test when it's a beta page
            }
            fail("Unexpected page encountered: " + webDriver.currentUrl + ", but was expecting " + pageUrl)
        }
    }

    protected fun waitForVisibleElement(failureMessage: String, by: By): WebElement =
        waitForVisibleElement(failureMessage, by, DEFAULT_WAIT_TIMEOUT_SECS)

    protected fun waitForVisibleElement(failureMessage: String, by: By, waitTimeoutSecs: Int): WebElement {
        waitFor(failureMessage, { webDriver: WebDriver? ->
            try {
                val element = webDriver!!.findElement(by)
//                scrollIntoView(element)
                element
                    .isDisplayed
            } catch (e: NoSuchElementException) {
                false
            }
        }, waitTimeoutSecs)
        val element = findElement(by)
        if (!element.isDisplayed) {
            log.warn("Element $by was found but not displayed. (is there perhaps more than one?)")
            // could scrollIntoView(element), but likely it's the wrong element or some other fault
        }
        return element
    }

    protected fun waitWhileElementDisplayed(failureMessage: String, by: By) {
        waitFor(failureMessage, ExpectedConditions.invisibilityOfElementLocated(by))
    }

    protected fun waitForVisibleElementById(elementId: String) {
        val locator = By.id(elementId)
        waitForVisibleElement("Timed out waiting for element id=$elementId to be visible.", locator)
    }

    protected fun waitForVisibleElementByName(elementName: String) {
        val locator = By.name(elementName)
        waitForVisibleElement("Timed out waiting for element name=$elementName to be visible.", locator)
    }

    protected fun waitForIdContainingText(elementId: String, text: String) {
        val locator = By.xpath("//*[@id='$elementId' and contains(text(),'$text')]")
        waitForVisibleElement("Timed out waiting for text '$text' to be visible in element id=$elementId", locator)
    }

    protected fun <T> waitFor(failureMessage: String, expectation: ExpectedCondition<T>): T? =
        waitFor(failureMessage, expectation, DEFAULT_WAIT_TIMEOUT_SECS)

    protected fun <T> waitFor(failureMessage: String, expectation: ExpectedCondition<T>, waitTimeoutSecs: Int): T? {
        val wait = WebDriverWait(webDriver, waitTimeoutSecs.toLong())
        try {
            return wait.until(expectation::apply)
        } catch (error: Throwable) {
            fail(failureMessage)
            return null // shame Java doesn't have "never" as a return type like Typescript does
        }
    }

    private fun findElement(by: By): WebElement = webDriver.findElement(by)

    protected fun elementExists(by: By): Boolean {
        try {
            findElement(by)
        } catch (e: NoSuchElementException) {
            return false
        }

        return true
    }

    /**
     * Replacement for direct call to webDriver.findElement which includes a wait of up to 10s for the element to appear.
     * Use this instead of configuring implicit waits.
     * @see WebDriver.findElement
     */
    protected fun findElementSoon(by: By): WebElement =
        WebDriverWait(webDriver, DEFAULT_FIND_TIMEOUT_SECS.toLong()).until(ExpectedConditions.presenceOfElementLocated(by)::apply)!!

    private fun findElementSoon(by: By, failureMessage: String): WebElement? =
        waitFor(failureMessage, ExpectedConditions.presenceOfElementLocated(by))

    /**
     * Replacement for direct call to webDriver.findElement which includes a wait of up to `timeoutSecs`
     * for the element to appear.
     * Use this instead of configuring implicit waits.
     * @see WebDriver.findElement
     */
    private fun findElementSoon(by: By, timeoutSecs: Int): WebElement =
        WebDriverWait(webDriver, timeoutSecs.toLong()).until(ExpectedConditions.presenceOfElementLocated(by)::apply)!!

    /**
     * Replacement for direct call to webDriver.findElements which includes a wait of up to 25s for the elements to appear.
     * Use this instead of configuring implicit waits.
     * @see WebDriver.findElements
     */
    protected fun findElementsSoon(by: By): List<WebElement> = findElementsSoon(by, DEFAULT_FIND_TIMEOUT_SECS)

    private fun findElementsSoon(by: By, timeoutSecs: Int): List<WebElement> =
        WebDriverWait(webDriver, timeoutSecs.toLong()).until(ExpectedConditions.presenceOfAllElementsLocatedBy(by)::apply)!!

    protected fun waitForSavedAlert(waitTimeoutSecs: Int) {
        waitForVisibleElement(
            "expected saved alert",
            By.xpath("//div[@id='snackbar']//span[text() = 'change saved']"),
            waitTimeoutSecs,
        )
    }

    companion object {
        private const val DEFAULT_FIND_TIMEOUT_SECS = 35
        private const val DEFAULT_WAIT_TIMEOUT_SECS = 40
        const val DEFAULT_SAVE_TIMEOUT = 3

        private val ISO_DATE_FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd")
        private val DATE_FORMAT = DateTimeFormat.forPattern("dd/MM/yyyy")
        private val TIME_FORMAT = DateTimeFormat.forPattern("HH:mm")

        // NB this is only used in 'setup initial assessment' for non-Firefox browsers and for native controls
        // which amounts, for testing, to Chrome - where a 'space' does not trigger the time entry, but a tab does
        private val DD_MM_YYYY_HH_MM = DateTimeFormat.forPattern("dd/MM/yyyy\t HH:mm")
    }
}