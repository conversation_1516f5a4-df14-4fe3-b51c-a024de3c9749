package com.ecco.acceptancetests.ui.steps.webdriver;

import com.ecco.acceptancetests.steps.ReportLoginAuditSteps;
import com.ecco.acceptancetests.ui.pages.ReportsPage;
import com.ecco.acceptancetests.ui.pages.UserAuditReportPage;
import com.ecco.acceptancetests.ui.pages.WelcomePage;
import com.google.common.collect.ImmutableTable;
import com.google.common.collect.Table;

import org.jspecify.annotations.NonNull;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import java.util.List;

public class ReportLoginAuditStepsWebDriver extends WebDriverUI implements ReportLoginAuditSteps {

    public ReportLoginAuditStepsWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void viewLoginReport(@NonNull String username) {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        ReportsPage reportsPage = welcomePage.gotoReports();
        UserAuditReportPage userAuditReportPage = reportsPage.gotoUserAudit();
        userAuditReportPage.enterUsername(username);
        userAuditReportPage.clickLoginAuditReport();
    }

    @Override
    public void viewSecurityReport(@NonNull String username) {
        WelcomePage welcomePage = new WelcomePage(webDriver);
        welcomePage.menu();
        ReportsPage reportsPage = welcomePage.gotoReports();
        UserAuditReportPage userAuditReportPage = reportsPage.gotoUserAudit();
        userAuditReportPage.enterUsername(username);
        userAuditReportPage.clickSecurityAuditReport();
    }

    @NonNull
    @Override
    public Table<String, String, String> retrieveDataTable() {
        final ImmutableTable.Builder<String, String, String> builder = ImmutableTable.builder();

        // Fetch the THEAD/TR/TH[role='columnheader'] elements to determine the column headers
        final List<WebElement> columnHeaders = webDriver.findElements(By.cssSelector("table.dataTable th[role = 'columnheader']"));
        // Then the TBODY/TR/TD elements make up the data (row header is the first column)
        final List<WebElement> rows = webDriver.findElements(By.cssSelector("table.dataTable tbody tr"));
        for (WebElement row : rows) {
            final List<WebElement> columns = row.findElements(By.tagName("td"));
            for (int i = 1; i < columns.size(); i++) {
                WebElement cell = columns.get(i);
                WebElement column = columnHeaders.get(i);
                builder.put(columns.get(0).getText(), column.getText(), cell.getText());
            }
        }
        return builder.build();
    }

}
