package com.ecco.acceptancetests.ui.steps.webdriver;

import static org.junit.Assert.fail;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

import com.ecco.acceptancetests.ui.pages.HomePage;
import com.ecco.data.client.WebApiSettings;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.html5.LocalStorage;
import org.openqa.selenium.html5.WebStorage;
import org.openqa.selenium.internal.WrapsDriver;
import org.openqa.selenium.support.pagefactory.ByChained;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Wait;
import org.openqa.selenium.support.ui.WebDriverWait;

import asl.fm.last.commons.RetryTemplate;

import com.ecco.acceptancetests.steps.CommonSteps;
import com.thoughtworks.selenium.webdriven.WebDriverBackedSelenium;

/**
 * Base for WebDriver based UI drivers
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class WebDriverUI implements CommonSteps, Lifecycle {

    private static final int DEFAULT_FIND_TIMEOUT_SECS = 3;

    protected volatile WebDriver webDriver;
    private volatile WebDriverBackedSelenium webDriverBackedSelenium;

    protected WebDriverUI(WebDriver webDriver) {
        this.webDriver = webDriver;
        this.webDriverBackedSelenium = new WebDriverBackedSelenium(webDriver, WebApiSettings.APPLICATION_URL);
    }

    @Override
    final public void quit() {
        webDriverBackedSelenium = null;
        webDriver.quit();
        webDriver = null;
    }


    private boolean isTextPresent(String str) { // FIXME: This is really inefficient.  Use another method other than checkCanSeeText()
        WebElement bodyElement = webDriver.findElement(By.tagName("body"));
        return bodyElement.getText().contains(str);
    }

    private boolean isTextPresentx(String text) {

        try { // tagname body
            WebElement element = webDriver.findElement(By.xpath("//*[contains(text(),'" + text + "')]"));
            log.warn("isTextPresent({}) was found in {}.{}", text, element.getTagName(), element.getAttribute("class"));
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    @Deprecated // Use  checkCanSeeTextAtXPath - as this is awful perf
    public void checkCanSeeText(@NonNull final String text) {
        new RetryTemplate().execute(() -> {
            Assert.assertTrue("Text [" + text + "] was expected to be visible and is not",
                    webDriverBackedSelenium.isTextPresent(text));
            return null;
        });
    }

    @Override
    public void checkCannotSeeText(@NonNull String text) {
        Assert.assertFalse("Text [" + text + "] should not be visible, but it is",
                webDriverBackedSelenium.isTextPresent(text));
    }

    public void checkCanSeeTextInTag(String text, String htmlTag) {
        try {
            findElementSoon(By.xpath("//" + htmlTag + "[text() = '" + text + "']"));
        }
        catch (TimeoutException e) {
            Assert.fail("Text [" + text + "] was expected to be visible in tag: " + htmlTag + " and is not");
        }
    }

    /** Beware: This may not work if the selector exists and is then updated. ByChained is an attempt sort that. */
    public void checkCanSeeTextAtCssSelector(String text, String cssSelector) {

        var selector = new ByChained(By.cssSelector(cssSelector), By.xpath("//*[contains(text(),'" + text + "')]"));
        try {
            findElementSoon(selector); // Not useful if text is updated
        }
        catch (TimeoutException e) {
            Assert.fail("Text [" + text + "] was expected to be visible at: " + cssSelector + " and is not");
        }
    }

    public void checkCanSeeTextAtXpath(String text, String xPath) {
        try {
            findElementSoon(By.xpath(xPath + "[text()='" + text + "']"));
        }
        catch (TimeoutException e) {
            Assert.fail("Text [" + text + "] was expected to be visible at: " + xPath + " and is not");
        }
    }

    public void checkCanSeeTextAtXpath(String text, String xPath, int timeoutSecs) {
        try {
            findElementSoon(By.xpath(xPath + "[text()='" + text + "']"), timeoutSecs);
        }
        catch (TimeoutException e) {
            Assert.fail("Text [" + text + "] was expected to be visible at: " + xPath + " and is not");
        }
    }

    protected WebElement findElementSoon(final By by, int timeoutSecs) {
        return new WebDriverWait(webDriver, timeoutSecs).until(ExpectedConditions.presenceOfElementLocated(by));
    }

    protected WebElement findElementSoon(final By by) {
        return new WebDriverWait(webDriver, DEFAULT_FIND_TIMEOUT_SECS).until(ExpectedConditions.presenceOfElementLocated(by));
    }

    public WebDriver getWebDriver() {
        return webDriver;
    }

    protected boolean isElementPresent(String id) {
        return webDriver.findElements(By.id(id)).size() == 1;
    }

    protected void waitForPageLoaded() {
        ExpectedCondition<Boolean> expectation = new ExpectedCondition<>() {
            @Override
            public Boolean apply(@Nullable WebDriver driver) {
                return ((JavascriptExecutor) driver).executeScript("return document.readyState").equals("complete");
            }
            @Override
            public boolean equals(@Nullable Object obj) {
                return super.equals(obj);
            }
        };

        Wait<WebDriver> wait = new WebDriverWait(webDriver, 30);
        try {
            wait.until(expectation::apply);
        } catch (Throwable error) {
            fail("Timeout waiting for Page Load Request to complete.");
        }
    }

    public void navigateToUrl(String pageAddress) {
        webDriver.get(pageAddress);
    }

    protected void navigate(String contextRelativeUrl) {
        navigateToUrl(WebApiSettings.APPLICATION_URL + contextRelativeUrl);
    }

    @Override
    public void navigateToHome() {
        navigate(HomePage.URL);
//        checkCanSeeText("ecco solutions ltd");

        setLocalsStorage("ecco.introductions.announce-menu-tabs", "done");
        // maximizeWindow();
    }

    private void setLocalsStorage(String key, String value) {

        WebDriver wrapped = (webDriver instanceof WrapsDriver) ?
            ((WrapsDriver) webDriver).getWrappedDriver() : webDriver;

        // only ChromeDriver at mo, and broken due to proxy when done here
        if (wrapped instanceof WebStorage) {
            LocalStorage storage = ((WebStorage) wrapped).getLocalStorage();
            storage.setItem(key, value);
        }
        else if (wrapped instanceof JavascriptExecutor) {
            ((JavascriptExecutor) wrapped).executeScript(String.format(
                    "window.localStorage.setItem('%s','%s');", key, value));
        }
        else {
            throw new UnsupportedOperationException("Can't set local storage");
        }
    }

    // a work around currently for getting back from reporting
    @Override
    public void navigateToWelcome() {
        navigate("/nav/secure/welcomeOld.html"); // NB no longer exists
    }

    @Override
    public void navigateBack() {
        webDriver.navigate().back();
        waitForPageLoaded();
    }

    public String getCurrentUrl() {
        return webDriver.getCurrentUrl();
    }

    static public void pause(int millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
