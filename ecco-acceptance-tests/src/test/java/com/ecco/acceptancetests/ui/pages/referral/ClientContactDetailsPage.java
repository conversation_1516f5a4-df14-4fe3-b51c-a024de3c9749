package com.ecco.acceptancetests.ui.pages.referral;

import org.openqa.selenium.WebDriver;
import com.ecco.acceptancetests.ui.pages.EccoBasePage;

public class ClientContactDetailsPage extends EccoBasePage {

    private static final String FIELD_MOBILE = "mobileNumber";
    private static final String FIELD_EMAIL = "email";
    private static final String FIELD_ADDRESS_1 = "address.line1";
    private static final String FIELD_ADDRESS_2 = "address.line2";
    private static final String FIELD_ADDRESS_3 = "address.line3";
    private static final String FIELD_TOWN = "address.town";
    private static final String FIELD_COUNTY = "address.county";
    private static final String FIELD_POSTCODE = "address.postCode";
    private static final String FIELD_COUNTRY = "address.country";

    private static final String VERIFY_TEXT1 = "mobile";

    ReferralViewPage referralPage;
    String url;

    public ClientContactDetailsPage(String url, ReferralViewPage referralPage, WebDriver webDriver) {
        super(url, webDriver);
        this.referralPage = referralPage;
        this.url = url;
    }

    @Override
    public void verifyIsCurrentPage() {
        super.verifyIsCurrentPage();
        waitForVisibleElementById("mobileNumber");
    }

    public void enterDetails(String mobile, String email, String address1, String address2, String address3,
            String town, String county, String postcode, String country) {
        setField(FIELD_MOBILE, mobile);
        setField(FIELD_EMAIL, email);
        setField(FIELD_ADDRESS_1, address1);
        setField(FIELD_ADDRESS_2, address2);
        setField(FIELD_ADDRESS_3, address3);
        setField(FIELD_TOWN, town);
        setField(FIELD_COUNTY, county);
        setField(FIELD_POSTCODE, postcode);
        setSelection(FIELD_COUNTRY, country);
        clickButton(EccoBasePage.BUTTON_NEXT_ID);
    }

    @Override
    public EccoBasePage defaultAction() {
        enterDetails(null, null, null, null, null, null, null, null, null);
        return new SourceTypePage(url, referralPage, getWebDriver());
    }

}
