package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.TestUtils;
import com.ecco.acceptancetests.ui.BaseSeleniumTest;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.ServiceOptions;
import com.ecco.test.support.RetryRule;
import com.ecco.webApi.evidence.ReferralViewModel;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.IfProfileValue;

import java.util.List;

import static com.ecco.data.client.ServiceOptions.ACCOMMODATION;
import static org.junit.Assert.assertEquals;

public class ReferralCreationTests extends BaseSeleniumTest {

    private static final String TEST_NI_NUMBER = "*********";

    @RetryRule.Retries(1)
    @Test
    @IfProfileValue(name="test.category", values={"all","quarantine"}) // fails on assertActiveBreadcrumbIs('referral')
    public void canCreateDifferentReferralsUsingNewUI() {
        loginSteps.loginAsSysadmin();
        loginAsSysadminApi();

        ReferralOptions options;

        // create new referral using split create/process methods
        options = new ReferralOptions().requiresAccommodation(true);
        ReferralViewModel rvm1 = referralSteps.createReferral(true, false, false, null, options, ACCOMMODATION,
                "FirstName", "LastName", null, TEST_NI_NUMBER, "male", "English", "Missing");
        String referralName1 = rvm1.getClientFirstName() + " " + rvm1.getClientLastName();
        referralSteps.processTaskDefinitions(options, ACCOMMODATION);

        // create new referral using 'ensure' so picks an existing on up if there
        ReferralViewModel rvm = ensureReferralView("lookupThisCode");
        assertEquals("referral code", "lookupThisCode", rvm.getReferralCode());


        // create a new referral to demo all
        options = new ReferralOptions()
            .requiresProjects(false)
            .requiresDataProtection(true)
            .requiresEmergencyDetails(true);
        String referralName2 = referralSteps.processReferral(options, ServiceOptions.DEMO_ALL, "FirstName", "ALL",
                "*********", "this is a new referral comment 1")
                .getSecond();

        //userUI.check().canSeeText("your action has been accepted");
        List<String> referrals = referralSteps.listReferrals();
        TestUtils.listContains(referrals, referralName1);
        TestUtils.listContains(referrals, referralName2);

        loginSteps.logout();
        logoutApi();
    }



    // return or create a referral
    // NB requires restClient.login
    private ReferralViewModel ensureReferralView(String code) {

        // check for existance
        ResponseEntity<ReferralViewModel> response = referralActor.getReferralByCode(code);
        ReferralViewModel rvm = response.getBody();
        if (rvm != null) {
            // move to the referralView, so the ui is consistent with creating
            referralSteps.findClientThenReferral(rvm.getClientId().toString(), rvm.getClientFirstName(),
                    rvm.getClientLastName(), rvm.getReferralCode());
            return rvm;
        }

        // create if not - and stay on a referralView page
        ReferralOptions options = new ReferralOptions().requiresAccommodation(true);
        rvm = referralSteps.createReferral(true, false, false, null, options, ACCOMMODATION, "FirstName", "LastName",
                null, TEST_NI_NUMBER, "male", "English", "Missing");

        // update the referral's code to identify it in further tests
        // use web-api as its not available on the ui (only clients code is available when the 'messages' table has an
        // lastClientID entry)
        // oldValue is by default the same as the id
        // NB this will leave the referralView above out of date (version number incremented), but recent changes
        // should mean that the referral is loaded when clicked
        updateTextCommandActor.updateProperty("referrals", rvm.getReferralId(), "code", code, rvm.getReferralId().toString());

        // re-load because the ui doesn't have access to all the properties of the ReferralViewModel but the web-api does
        // and since we expect it in the return above, simply get it again
        response = referralActor.getReferralByCode(code);
        rvm = response.getBody();
        return rvm;
    }
}
