package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.RotaSteps;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.actors.*;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.service.BuildingCareRunRotaHandler;
import com.ecco.rota.webApi.dto.AgreementResource;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.listsConfig.AppointmentTypeViewModel;
import com.ecco.webApi.rota.*;
import com.ecco.webApi.taskFlow.StaffTaskPrimaryLocationCommandDto;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.JsonPath;
import com.ecco.calendar.core.webapi.EventResource;
import kotlin.Pair;
import org.assertj.core.api.Assertions;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.joda.time.DateTimeZone;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.hateoas.Link;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.Assert;

import java.time.*;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ecco.data.client.ServiceOptions.BUILDINGS;
import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static org.apache.commons.lang3.StringUtils.equalsIgnoreCase;
import static org.hamcrest.Matchers.*;
import static org.joda.time.DateTime.now;
import static org.junit.Assert.*;

@SuppressWarnings("unused")
public class RotaStepsWebApi extends BaseActor implements RotaSteps {

    private static final long REFERRAL_VIEW = 18L; // Could go somewhere shared with Backend as it's fixed but only for linear workflow

    private final SessionDataActor sessionDataActor;
    private final ServiceActor serviceActor;
    private final AgreementActor agreementActor;
    private final BuildingActor buildingActor;
    private final CalendarActor calendarActor;
    private final ReferralActor referralActor;
    private final ServiceRecipientActor serviceRecipientActor;
    private final WorkerActor workerActor;
    private final RotaActor rotaActor;
    private final ReferralSteps referralSteps;
    private Clock clock = Clock.DEFAULT;
    private org.joda.time.DateTime now = clock.now();
    private java.time.ZonedDateTime nowJdk = clock.nowJdk();

    private final UniqueDataService unique = UniqueDataService.instance;

    public RotaStepsWebApi(RestTemplate restTemplate, SessionDataActor sessionDataActor, WorkerActor workerActor, AgreementActor agreementActor,
                           ReferralActor referralActor, CalendarActor calendarActor, BuildingActor buildingActor,
                           RotaActor rotaActor, ServiceRecipientActor serviceRecipientActor, ServiceActor serviceActor,
                           ReferralSteps referralSteps) {
        super(restTemplate);
        this.sessionDataActor = sessionDataActor;
        this.serviceActor = serviceActor;
        this.agreementActor = agreementActor;
        this.buildingActor = buildingActor;
        this.calendarActor = calendarActor;
        this.referralActor = referralActor;
        this.workerActor = workerActor;
        this.rotaActor = rotaActor;
        this.serviceRecipientActor = serviceRecipientActor;
        this.referralSteps = referralSteps;
    }

    @NonNull
    @Override
    public WorkerResult createWorkerAndJob(String username) {
        ResponseEntity<Result> result = workerActor.createWorker(username, username);
        long workerId = Long.valueOf(result.getBody().getId());
        var workerJobId = workerActor.createWorkerJob(workerId, nowJdk.toLocalDate());
        var workerCalendarId = workerActor.getWorker(workerId).getBody().getCalendarId();
        var workerJob = workerActor.getWorkerJobId(workerJobId).getBody();
        assert workerJob != null;
        return new WorkerResult(workerId, username + ' ' + username, workerJobId, workerCalendarId, workerJob.getServiceRecipient().serviceRecipientId);
    }

    @NonNull
    @Override
    public Pair<Long, String> createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(@NonNull String appointmentTypeName, @NonNull LocalDateTime time, @Nullable Integer srId) {
        ReferralOptions options = new ReferralOptions()
                .withNoWorkflow();
//                .withServiceAgreement(agreementAppointmentType);
        Pair<Long, String> result = referralSteps.processReferral(options, DEMO_ALL, "FirstName", "LastName", "AB123456C", "this is a new referral comment 1");
        createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(appointmentTypeName, result, time, srId);
        return result;
    }

    @Override
    public long createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(String appointmentTypeName, Pair<Long, String> referralResult, LocalDateTime time, Integer resourceSrId) {
        final String agreementAppointmentType = agreementActor.createAppointmentType(DEMO_ALL, appointmentTypeName, 60);
        // today for 30 days
        ReferralSummaryViewModel rsvm = referralActor.getReferralSummaryById(referralResult.getFirst()).getBody();
        var agreementResult = agreementActor.createAgreement(rsvm.serviceRecipientId, now.toLocalDate(), now.plusDays(90).toLocalDate(), null);
        // create an appointment (ad hoc is fine, otherwise need more infrastructure)
        //List<AgreementResource> agreements = agreementActor.getAgreements(rsvm.clientDisplayName, now);
        var scheduleResult = checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(rsvm.clientDisplayName, rsvm.serviceRecipientId, time, resourceSrId);
        // NB what is returned is the AGREEMENT ID !
        return AgreementController.EXTRACT_ID_FN.apply(scheduleResult.getLink(SchemaProvidingController.REL_EDIT).getHref());
    }

    @NonNull
    @Override
    public WorkerResult createWorkerAndJobWithAvailabilityForToday(@NonNull String username, @Nullable Integer workerPrimaryLocationBuildingId) {
        var worker = createWorkerAndJob(username);
        workerActor.linkWorkerToUser(worker.getWorkerId(), username);
        createWorkerAvailabilityForToday(worker, workerPrimaryLocationBuildingId);
        return worker;
    }

    @NonNull
    @Override
    public String createWorkerAndJobWithAvailabilityForToday(@NonNull String username, @NonNull String workerFirstName,
                                                             @NonNull String workerLastName, @Nullable Integer workerPrimaryLocationBuildingId) {
        ResponseEntity<Result> result = workerActor.createWorker(workerFirstName, workerLastName);
        long workerId = Long.valueOf(result.getBody().getId());
        int workerJobId = workerActor.createWorkerJob(workerId, nowJdk.toLocalDate());
        var workerCalendarId = workerActor.getWorker(workerId).getBody().getCalendarId();
        var workerJob = workerActor.getWorkerJobId(workerJobId).getBody();
        assert workerJob != null;
        var worker = new WorkerResult(workerId, workerFirstName + " " + workerLastName, workerJobId, workerCalendarId, workerJob.getServiceRecipient().serviceRecipientId);
        workerActor.linkWorkerToUser(workerId, username);
        return createWorkerAvailabilityForToday(worker, workerPrimaryLocationBuildingId);
    }

    private String createWorkerAvailabilityForToday(final WorkerResult worker, final Integer workerPrimaryLocationBuildingId) {
        if (workerPrimaryLocationBuildingId != null) {
            setWorkerPrimaryLocation(worker.getWorkerId().intValue(), worker.getWorkerJobId(), workerPrimaryLocationBuildingId);
        }
        String calendarId = workerActor.findWorkerCalendarId(worker.getWorkerId());
        createAvailabilityForToday(calendarId);
        return worker.getName();
    }
    public void createAvailabilityForToday(String calendarId) {
        // Use the Web API we have as per com.ecco.acceptancetests.api.rota.AvailabilityAPITests
        // TODO: replace with UI interaction once there is one

        ResponseEntity<String> response = calendarActor.getAvailability(calendarId, now, now.plusDays(1));

        JsonObject parsedAvailability = new JsonParser().parse(response.getBody()).getAsJsonObject();
        // Make the user available all day.
        long availableStart = ISODateTimeFormat.dateTime().parseMillis(parsedAvailability.get("dtStart").getAsString());
        long availableEnd = ISODateTimeFormat.dateTime().parseMillis(parsedAvailability.get("dtEnd").getAsString());
        JsonObject newAvailable = new JsonObject();
        newAvailable.addProperty("dtStart", availableStart);
        newAvailable.addProperty("dtEnd", availableEnd);
        final JsonArray availables = new JsonArray();
        availables.add(newAvailable);
        parsedAvailability.add("available", availables);

        final HttpHeaders postHeaders = new HttpHeaders();
        postHeaders.setContentType(MediaType.APPLICATION_JSON);
        final List<Object> collectionLinks = JsonPath.read(response.getBody(), "$.links[?(@.rel == 'collection')].href");
        final HttpEntity<String> postRequest = new HttpEntity<>(parsedAvailability.toString(), postHeaders);
        response = restTemplate.postForEntity(collectionLinks.get(0).toString(), postRequest, String.class);
        assertThat("Successfully created availability", response.getStatusCode(), equalTo(HttpStatus.CREATED));
    }

    @Override
    public void setWorkerPrimaryLocation(long workerId, int workerJobId, int primaryLocationId) {
        var worker = workerActor.getWorker(workerId).getBody();
        var srId = worker.jobs.stream().filter(j -> j.getWorkerJobId().equals(workerJobId)).findFirst().get().getServiceRecipient().serviceRecipientId;
        var dto = new StaffTaskPrimaryLocationCommandDto(srId, srId + "-" + REFERRAL_VIEW); // for all overview exits
        dto.location = ChangeViewModel.changeNullTo(primaryLocationId);
        var response = this.postAsJson(apiBaseUrl + dto.commandUri, dto);

        Assertions.assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        log.info("updated worker primaryLocation: {}", workerId);
    }

    @Override
    public void checkCanAssignResourceToFirstAppointmentToday(
            @NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
            @NonNull String recipientName, @NonNull String resourceName) {
        checkCanAssignResourceToSingleAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, null);
    }

    @Override
    public void checkCanAssignResourceToSingleAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
                                                                @NonNull String recipientName, @NonNull String resourceName, @Nullable LocalDateTime time) {
        checkCanChangeResourceOnAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, null, null, false, ACTIVITY.ALLOCATE);
    }

    @Override
    public void checkCanUnAssignResourceFromSingleAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
                                                                @NonNull String recipientName, @NonNull String resourceName, @Nullable LocalDateTime time) {
        checkCanChangeResourceOnAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, null, null, false, ACTIVITY.DEALLOCATE);
    }

    @Override
    public void checkCanAssignResourceToRecurringAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
                                                                   @NonNull String recipientName, @NonNull String resourceName, LocalDateTime time,
                                                                   DaysOfWeek daysOfWeek, @Nullable LocalDate recurringEnd) {
        checkCanChangeResourceOnAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd, true, ACTIVITY.ALLOCATE);
    }

    @Override
    public void checkCanUnAssignResourceFromRecurringAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
                                                                   @NonNull String recipientName, @NonNull String resourceName, LocalDateTime time,
                                                                   DaysOfWeek daysOfWeek, @Nullable LocalDate recurringEnd) {
        checkCanChangeResourceOnAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd, true, ACTIVITY.DEALLOCATE);
    }

    private enum ACTIVITY {ALLOCATE, DEALLOCATE};

    private void checkCanChangeResourceOnAppointmentAtTime(@NonNull String resourceFilter, @NonNull String serviceRecipientDemandFilter,
                                                          @NonNull String recipientName, @NonNull String resourceName, @Nullable LocalDateTime time,
                                                          @Nullable DaysOfWeek daysOfWeek, @Nullable LocalDate recurringEnd, boolean recurring,
                                                          ACTIVITY activity) {

        org.joda.time.DateTime rotaDate = time != null ? JodaToJDKAdapters.localDateTimeToJoda(time).toDateTime(DateTimeZone.UTC) : now;
        // TODO: replace with UI interaction when it's finalised to avoid this test getting too fragile now
        Rota rota = rotaActor.getRotaOnDate(resourceFilter, serviceRecipientDemandFilter, rotaDate);

        // Check the worker is returned
        RotaResourceViewModel resource = rota.findResourceByName(resourceName);
        Assert.notNull(resource, "Expected resource with name '" + resourceName);

        // Check the unassigned appointment is returned for the service recipient
        List<RotaAppointmentViewModel> activities = activity == ACTIVITY.ALLOCATE
                ? rota.findDemandByServiceRecipientName(recipientName)
                : Objects.requireNonNull(rota.findResourceByName(resourceName)).getAppointments();
        RotaAppointmentViewModel apt = time == null
                ? activities.get(0) // Note: This may give IndexOutOfBounds if looking for something created plusHours(x) and that puts it on the next day
                                    // or provide a 'time', since the date of today is probably not what is wanted (eg daysOfWeek is not today)
                : activities.stream().filter(a -> a.getStart().equals(time)).findFirst().get();

        // TODO: Check the worker has some availability

        var activityRef = activities.get(0).getRef();
        var operation = activity == ACTIVITY.ALLOCATE
                ? AppointmentActionCommandDto.OPERATION_ALLOCATE
                : AppointmentActionCommandDto.OPERATION_DEALLOCATE;
        if (recurring) {
            ServiceRecipientAppointmentScheduleCommandDto partSchedule = new ServiceRecipientAppointmentScheduleCommandDto();
            partSchedule.applicableFromDate = time != null ? time.toLocalDate() : apt.getStart().toLocalDate();
            partSchedule.endDate = recurringEnd != null ? ChangeViewModel.changeNullTo(recurringEnd) : null;
            if (daysOfWeek != null) {
                partSchedule.days = AddedRemovedDto.added(daysOfWeek.toCalendarDayList());
            }

            AppointmentRecurringActionCommandDto action = new AppointmentRecurringActionCommandDto(operation,
                    activityRef, activities.get(0).getServiceRecipientId(), resourceFilter, serviceRecipientDemandFilter, partSchedule);
            if (activity == ACTIVITY.ALLOCATE) {
                action.allocateResourceId = resource.getResourceId().intValue();
            } else {
                action.deallocateResourceId = resource.getResourceId().intValue();
            }
            log.info(activity + " resource on activity ref: {}; {}", resourceName, activityRef);
            executeCommand(action);
            Assert.notNull(this.serviceRecipientActor.findRecurringAllocateCommand(action.uuid.toString()).getBody(), "command not found");
        } else {
            AppointmentActionCommandDto action = new AppointmentActionCommandDto(operation,
                    activityRef, activities.get(0).getServiceRecipientId(), resourceFilter, serviceRecipientDemandFilter);
            if (activity == ACTIVITY.ALLOCATE) {
                action.allocateResourceId = resource.getResourceId().intValue();
            } else {
                action.deallocateResourceId = resource.getResourceId().intValue();
            }
            log.info(activity + " resource on activity ref: {}; {}", resourceName, activityRef);
            executeCommand(action);
            Assert.notNull(this.serviceRecipientActor.findAllocateCommand(action.uuid.toString()).getBody(), "command not found");
        }

        // Fetch the rota again and check the appointment is shown against the worker
        rota = rotaActor.getRotaOnDate(resourceFilter, serviceRecipientDemandFilter, rotaDate);
        List<RotaAppointmentViewModel> appointments = activity == ACTIVITY.ALLOCATE
                ? rota.findResourceByName(resourceName).findAppointmentsByServiceRecipientName(recipientName)
                : rota.findDemandByServiceRecipientName(recipientName);;
        Assert.state(appointments.size() == 1, "Expected one appointment to be returned");
    }

    @NonNull
    @Override
    public Result checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(
            @NonNull String serviceRecipientName, int serviceRecipientId, LocalDateTime time,
            @Nullable Integer resourceSrId) {
        AppointmentTypeViewModel apptType = agreementActor.getAppointmentTypes().get(0);
        AgreementResource agreement = agreementActor.getFirstAgreement(serviceRecipientId, now);
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_ADD, serviceRecipientId);
        cmd.adHoc = true;
        cmd.eventRef = null; // confirm its an 'add' operation
        cmd.agreementId = agreement.getAgreementId().intValue();
        //cmd.workerId = optional
        cmd.appointmentTypeId = ChangeViewModel.changeNullTo(apptType.id);
        cmd.startDate = ChangeViewModel.changeNullTo(time.toLocalDate());
        cmd.time = ChangeViewModel.changeNullTo(time.toLocalTime());
        cmd.durationMins = ChangeViewModel.changeNullTo(10);
        cmd.resourceSrId = resourceSrId;
        return Objects.requireNonNull(executeCommand(cmd).getBody());
    }

    @NonNull
    public Result checkCanCreateScheduleOnFirstAgreement(@NonNull String serviceRecipientName, int serviceRecipientId,
                                                         @Nullable LocalDateTime startDateTime, @Nullable DaysOfWeek daysOfWeek,
                                                         @Nullable LocalDate endDate, int additionalStaff,
                                                         @Nullable Integer resourceSrId) {
        return Objects.requireNonNull(executeCommand(constructScheduleCmdOnFirstAgreement(serviceRecipientName, serviceRecipientId, startDateTime, daysOfWeek, endDate, additionalStaff, null, resourceSrId)).getBody());
    }

    @NonNull
    public Result checkCanCreateScheduleOnFirstAgreement(@NonNull String serviceRecipientName, int serviceRecipientId,
                                                         @Nullable LocalDateTime startDateTime, @Nullable DaysOfWeek daysOfWeek,
                                                         @Nullable LocalDate endDate, int additionalStaff,
                                                         @Nullable List<ServiceRecipientAppointmentScheduleDirectTaskCommandDto> taskChanges,
                                                         @Nullable Integer resourceSrId) {
        return Objects.requireNonNull(executeCommand(constructScheduleCmdOnFirstAgreement(serviceRecipientName, serviceRecipientId, startDateTime, daysOfWeek, endDate, additionalStaff, taskChanges, resourceSrId)).getBody());
    }

    public ServiceRecipientAppointmentScheduleCommandDto constructScheduleCmdOnFirstAgreement(@NonNull String serviceRecipientName, int serviceRecipientId,
                                                         @Nullable LocalDateTime startDateTime, @Nullable DaysOfWeek daysOfWeek, @Nullable LocalDate endDate, int additionalStaff,
                                                         @Nullable List<ServiceRecipientAppointmentScheduleDirectTaskCommandDto> taskChanges,
                                                         @Nullable Integer resourceSrId) {

        org.joda.time.DateTime pointInTime = startDateTime != null ? JodaToJDKAdapters.localDateTimeToJoda(startDateTime).toDateTime(DateTimeZone.UTC) : now;

        AppointmentTypeViewModel apptType = agreementActor.getAppointmentTypes().get(0);
        AgreementResource agreement = agreementActor.getFirstAgreement(serviceRecipientId, pointInTime);
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_ADD, serviceRecipientId);
        cmd.eventRef = null; // confirm its an 'add' operation
        cmd.agreementId = agreement.getAgreementId().intValue();
        //cmd.workerId = optional
        cmd.appointmentTypeId = ChangeViewModel.changeNullTo(apptType.id);
        cmd.startDate = ChangeViewModel.changeNullTo(startDateTime != null ? startDateTime.toLocalDate() : nowJdk.toLocalDate());
        cmd.time = ChangeViewModel.changeNullTo(startDateTime != null ? startDateTime.toLocalTime() : nowJdk.toLocalTime().plusHours(2));
        cmd.durationMins = ChangeViewModel.changeNullTo(10);
        cmd.additionalStaff = additionalStaff;
        cmd.tasksDirect = taskChanges;
        cmd.resourceSrId = resourceSrId;

        // repeating part
        cmd.endDate = endDate != null
                ? ChangeViewModel.changeNullTo(endDate)
                : null;

        if (daysOfWeek != null) {
            cmd.days = AddedRemovedDto.added(daysOfWeek.toCalendarDayList());
        } else {
            cmd.withISODayOfWeek(DayOfWeek.of(pointInTime.getDayOfWeek())); // ISO to ISO
        }

        return cmd;
    }

    @Override
    /** Checks referral is found and also contains expected calendar data */
    // NB another test for calendars based on offline rota test
    public void checkClientReferralIsAvailableOffline(@NonNull String clientName) {
        ReferralViewModel[] myReferrals = referralActor.getMyReferrals().getBody();
        ReferralViewModel[] matched = Lists.newArrayList(myReferrals).stream().filter(input -> {
            log.info("found {} in my referrals list", input.clientDisplayName);
            return input.clientDisplayName.equals(clientName);
        }).toArray(ReferralViewModel[]::new);
        assertEquals("client '" + clientName + "' should be listed in my referrals", matched.length, 1);

        // Appts have been created for the start of today to +30days (see ServiceAgreementPage.defaultAction)
        // calendarEvents are grabbed from now-1week to now+4weeks (see ReferralServiceImpl.getNearbyCalendarEventsForContact)
        // plusWeeks "calculation operates as if it were adding the equivalent in days." so its now-7days to now+28days

        // NB some timezone SETTING ENTITY follow-through:
        // ServiceAgreementPage.defaultAction creates an appointment on new LocalDate() at 11am for 30 days at 1 hour
        // which is saved from the page directly to the entity AgreementOfAppointments as a LocalDate
        // this is persisted without a need for a jadira annotation to a DATE field in liquibase (on appointmentagreements)

        // NB some timezone RETRIEVAL ENTITY follow-through:
        // the calendarEvents are gathered from:
        //      ReferralController.getReferralsAssignedToMeLive
        //          which calls ReferralServiceImpl.getNearbyCalendarEventsForContact and uses
        //          start = DateTime.now().minusWeeks(1) to start.plusWeeks(5)
        //          to call EventServiceImpl.getCalendars -> cosmo
        //      and then maps to the view model CalendarEntryToViewModel using toLocalDateTime()
        // Therefore the getCalendars is called with UTC date, and assuming the jvm is set as UTC timezone
        // then the returned items are accurate. The toLocalDateTime uses the default chronology
        // which, according to DateTime, "will be set internally to be in the UTC time zone for all calculations."
        // and so the returned calendarEvents are UTC based and match the UTC based 'now().minusWeeks(1)' passed in

        // NB timezone jvm
        // the jvm of the webapp is set to UTC - and checked on startup in LegacyConfig
        // the jvm of the tests are set to UTC - and checked during tests cycle in UTCTest

        // it should be collecting start of today-7days to end of today+28days, which means
        // today's plus one on each of 28 days after = 29

        // filter 'rota appointments' which appear as 'Other'
        List<EventResource> events = matched[0].calendarEvents.stream()
                .filter(eventResource -> !("Interview".equals(eventResource.getTitle()))).collect(Collectors.toList());

        assertEquals("Referral should contain rota appt for today and 28 following, but found " +
                events.size() + " at " + now(), 1, events.size());
    }

    @Override
    public void createAppointmentTypeForBuilding(@NonNull String appointmentTypeName) {
        agreementActor.createAppointmentType(BUILDINGS, appointmentTypeName, 60);
    }

    @NonNull
    @Override
    public FixedContainerViewModel createBuilding(@NonNull String buildingName) {
        return createBuilding(buildingName, ListDefinitionEntry.BUILDING_RESOURCETYPE_ID, null);
    }

    @NonNull
    @Override
    public FixedContainerViewModel createBuilding(@NonNull String buildingName, @NonNull FixedContainerViewModel parentBuilding) {
        return createBuilding(buildingName, ListDefinitionEntry.BUILDING_RESOURCETYPE_ID, parentBuilding);
    }

    @Override
    public FixedContainerViewModel createCareRun(@NonNull String careRunName, @NonNull FixedContainerViewModel parentBuilding) {
        var runName = unique.nameFor(careRunName);
        int buildingId = buildingActor.createResource(runName, null, BuildingCareRunRotaHandler.CARERUN_RESOURCE_ID, null, parentBuilding.buildingId, null);
        FixedContainerViewModel building = buildingActor.getBuildingById(buildingId);
        log.info("created building: {} with id {}", runName, building.buildingId);
        return building;
    }

    private FixedContainerViewModel createBuilding(@NonNull String buildingName, int resourceTypeId, FixedContainerViewModel parentBuilding) {
        final String buildingNameUniq = unique.nameFor(buildingName);
        buildingActor.createResource(buildingNameUniq, null, resourceTypeId, null, parentBuilding != null ? parentBuilding.buildingId : null, null); // eg 'building' list, with children 'leased property' / 'own property'
        FixedContainerViewModel building = buildingActor.findBuildingByName(buildingNameUniq);
        log.info("created building: {} with id {}", buildingNameUniq, building.buildingId);
        return building;
    }
    @Override
    public void createBuildingAgreement(@NonNull FixedContainerViewModel building) {
        Rota rota = getRotaForBuilding(building);
        agreementActor.createAgreement(building.serviceRecipientId, now.toLocalDate(), now.plusMonths(1).toLocalDate(), null);
    }
    private Rota getRotaForBuilding(FixedContainerViewModel building) {
        var buildingRotaUri = Link.valueOf(building.getRequiredLink("rota").toString()) // HACK to populate template - which is now otherwise null as we probably need @EnableHypermediaSupport blah
                .expand(null, null);
        return rotaActor.getRotaUsingLink(buildingRotaUri);
    }

    @Override
    /**
     * This creates a demand for a workerJob to visit a building with the first appointment type in the list for the
     * buildings service (e.g. shared hours) against the first service agreement for that building.
     * @param building where the demand is
     */
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(@NonNull FixedContainerViewModel building, @NonNull LocalDateTime time) {
        checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(building, null, time);
    }

    @Override
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(@NonNull FixedContainerViewModel building, @Nullable String appointmentTypeName, @Nullable LocalDateTime time) {
        var sessionData = sessionDataActor.getSessionData().getBody();
        assert sessionData != null;
        var bldgCat = serviceActor.getServiceCategorisation(sessionData, building.serviceAllocationId);
        AppointmentTypeViewModel apptType = agreementActor.getAppointmentTypes().stream()
                .filter(input -> input.serviceId.intValue() == bldgCat.serviceId)
                .filter(input -> appointmentTypeName == null || equalsIgnoreCase(appointmentTypeName, input.name))
                .findFirst().get();
        java.time.LocalDateTime aptTime = time == null ? nowJdk.toLocalDateTime().withNano(0) : time;
        AgreementResource agreement = agreementActor.getFirstAgreement(building.serviceRecipientId, JodaToJDKAdapters.dateTimeToJoda(aptTime.atZone(ZoneOffset.UTC)));
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_ADD, building.serviceRecipientId);
        cmd.adHoc = true;
        cmd.eventRef = null; // confirm its an 'add' operation
        cmd.appointmentTypeId = ChangeViewModel.changeNullTo(apptType.id);
        cmd.startDate = ChangeViewModel.changeNullTo(aptTime.toLocalDate());
        cmd.time = ChangeViewModel.changeNullTo(aptTime.toLocalTime());
        cmd.durationMins = ChangeViewModel.changeNullTo(60);

        cmd.agreementId = agreement.getAgreementId().intValue();
        //cmd.workerId = optional
        executeCommand(cmd);
    }

    @Override
    public void createBuildingAvailabilityFromToday(int buildingId) {
        FixedContainerViewModel bld = buildingActor.getBuildingById(buildingId);
        agreementActor.createAppointmentType(DEMO_ALL, Integer.toString(buildingId), 60);
        var today = now.toLocalDate();
        var result = agreementActor.createAgreement(bld.serviceRecipientId, today, today.plusDays(10), null);
        var agreement = agreementActor.getFirstAgreement(bld.serviceRecipientId, now);
        var days = DaysOfWeek.allWeek();
        checkCanCreateScheduleOnFirstAgreement(bld.name, bld.serviceRecipientId, nowJdk.toLocalDateTime(), days, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);
    }
}
