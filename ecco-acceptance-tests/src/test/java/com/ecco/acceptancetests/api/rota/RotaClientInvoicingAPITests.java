package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.api.referral.ReferralStepsWebApi;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.steps.ReferralData;
import com.ecco.data.client.steps.ReferralStepDefinitions;
import com.ecco.infrastructure.time.Clock;
import com.ecco.rota.webApi.dto.AgreementResource;
import com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource;
import com.ecco.finance.webApi.dto.ClientSalesInvoiceResource;
import com.ecco.finance.webApi.dto.SalesInvoice;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.ecco.webApi.rota.ContractViewModel;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.hateoas.Link;

import org.jspecify.annotations.NonNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static com.ecco.dom.EvidenceGroup.NEEDS;
import static com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource.REL_PROFORMA_LINES;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.joda.time.DateTimeZone.UTC;
import static org.springframework.hateoas.IanaLinkRelations.SELF;

/**
 * @since 12/10/2016
 */
@SuppressWarnings("ConstantConditions")
public class RotaClientInvoicingAPITests extends BaseJsonTest {
    private final Clock clock = Clock.DEFAULT;
    private final DateTime now = clock.now();

    @Test
    public void canCreateInvoiceAndFindItViaInvoicesForRecipient() {
        // GIVEN: I have a referral with a service agreement
        ReferralOptions options = new ReferralOptions()
                .withNoWorkflow()
                .withServiceAgreement("one-to-one", null);
        var pair = referralSteps.processReferral(options, DEMO_ALL, "Rota", "Test", "*********", "this is a rota test referral");
        ReferralViewModel rvm = referralActor.getReferralById(pair.getFirst()).getBody();

        // AND: I check the first ServiceAgreement exists
        var serviceRecipientId = rvm.serviceRecipientId;
        getFirstServiceAgreement(serviceRecipientId);

        // WHEN: I create an invoice dated at the end of the current month for this client
        var invoiceDate = createInvoiceFor(serviceRecipientId);

        // THEN: I can retrieve it and it's a valid draft one
        var invoice = getFirstInvoice(serviceRecipientId);
        assertIsEmptyDraftInvoiceWithDate(invoice, invoiceDate);

        // AND: I can fetch the invoice detail from the link
        var invoiceDetail = rotaActivityInvoiceActor.fetchClientSalesInvoiceDetail(invoice);
        assertThat("Expect detail to be for the same invoice", invoiceDetail.getInvoiceId(), equalTo(invoice.getInvoiceId()));
    }

    @Test
    public void canAddRecordedRotaVisitDetailsToInvoice() {
        // GIVEN: A contract exists with an adequate rate card
        var eventStatusRateId = createRateListDefEntry().id;
        ContractViewModel contract1 = contractSteps.givenContractWithRateCard(eventStatusRateId);

        // AND: We have a referral with a service agreement linked to this contract
        ReferralOptions options = new ReferralOptions()
                .withNoWorkflow()
                .withServiceAgreement("one-to-one", contract1.contractId);

        final long referralId = referralSteps.processReferralToId(options, DEMO_ALL, "Rota", "Test", "*********", "this is a rota test referral");
        ReferralViewModel referralViewModel = referralActor.getReferralById(referralId).getBody();

        ReferralData referralData = new ReferralData(referralViewModel, null, null, workflowActor.getTasksByTaskName(referralViewModel));
        String clientName = referralData.referralViewModel.getClientDisplayName();

        // AND: I check the first ServiceAgreement exists
        var serviceRecipientId = referralData.referralViewModel.serviceRecipientId;
        getFirstServiceAgreement(serviceRecipientId);

        // WHEN: I create an invoice dated at the end of the current month for this client
        var invoiceDate = createInvoiceFor(serviceRecipientId);

        // THEN: I can retrieve it and it's a valid draft one
        var invoice = getFirstInvoice(serviceRecipientId);
        assertIsEmptyDraftInvoiceWithDate(invoice, invoiceDate);

        // AND: I can fetch the proforma lines from the link
        List<ClientSalesRotaInvoiceDetailResource.Line> proformaLines = rotaActivityInvoiceActor.fetchUninvoicedLines(invoice);
        assertThat("Expect no pro forma lines", proformaLines, emptyCollectionOf(ClientSalesRotaInvoiceDetailResource.Line.class));

        // WHEN I create a rota visit event
        rotaSteps.checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(clientName, serviceRecipientId, LocalDate.now().atTime(22, 0), null);
        // get any eventRef to save with the evidence to get back to the AppointmentSchedule/<id> to get the ServiceAgreement's Contract
        var demand = agreementActor.getAppointmentSchedulesFromAgreement(referralViewModel.serviceRecipientId, DateTime.now(UTC)).getBody();
        String eventRef = demand.get(0).getEventRef();

        // AND I fulfill the visit with eventStatusRateId set (destined for the linked event)
        String rotaVisitComment = "rota visit comment";
        var eventStatusId = createEventListDefEntry().id;
        options = new ReferralOptions().withRotaVisit(eventRef, eventStatusId, eventStatusRateId, rotaVisitComment);
        referralData = new ReferralData(referralViewModel, null, options, workflowActor.getTasksByTaskName(referralViewModel));
        //noinspection UnusedAssignment
        referralData = ((ReferralStepsWebApi) referralSteps).executeTask(referralData, ReferralStepDefinitions.ROTA_VISIT);

        // THEN the audit is there for the comment
        var commentsAsc = commentCommandActor.findCommentCommands(serviceRecipientId, NEEDS).getBody();
        assertThat("comment comment not correct", rotaVisitComment.equals(commentsAsc.get(commentsAsc.size()-1).comment.to));

        // AND we have one uninvoiced line on the invoice (from RotaActivityInvoiceController.findUninvoicedWork)
        proformaLines = rotaActivityInvoiceActor.fetchUninvoicedLines(invoice);
        assertThat("Expect one pro forma line", proformaLines, hasSize(1));

        // WHEN Post the proforma line info back
        final UUID lineId = rotaActivityInvoiceActor.addClientSalesInvoiceLine(invoice.getInvoiceId(), proformaLines.get(0));

        // THEN: The approved lines should disappear from pro-forma lines
        proformaLines = rotaActivityInvoiceActor.fetchUninvoicedLines(invoice);
        assertThat("Expect one pro forma line", proformaLines, hasSize(0));

        // AND Fetch the whole invoice again and make sure the line matches
        invoice = fetchInvoiceDetailAndAssertContainsLine(invoice, lineId);

        List<ClientSalesRotaInvoiceDetailResource.Line> lines = rotaActivityInvoiceActor.fetchInvoiceLines(invoice);
        assertThat("Expect invoice line to have been added", lines.size(), equalTo(1));
        assertThat( "Expect line value to be correct", lines.get(0).getNetAmount().floatValue(), equalTo(20.05f));
        ClientSalesRotaInvoiceDetailResource.Line invoiceLine = lines.get(0);
        assertThat("Expect line to be them same ID", invoiceLine.getLineUuid(), equalTo(lineId));
        // AND Invoice line has Check the linked work
        assertThat(invoiceLine.getWorkUuid(), notNullValue());

        // AND the related work says ...
        var work = supportEvidenceActor.findAllSupportWorkSummaryByServiceRecipientId(invoiceLine.getServiceRecipientId(), NEEDS)
                .getBody().getContent().get(0);
        assertThat(work.id, equalTo(invoiceLine.getWorkUuid()));

        // AND the related work has an event with the rateId set
        var eventId = work.eventId;
        var event = calendarActor.getEntries(eventId).getBody()[0];
        assertThat(event.getEventStatusId(), equalTo(eventStatusId));
        assertThat(event.getEventStatusRateId(), equalTo(eventStatusRateId));
    }

    @Test
    @Disabled("Needs fixing")
    public void canAddPlannedRotaDetailsToInvoice() {
        // GIVEN: A contract exists with an adequate rate card
        var eventStatusRateId = createRateListDefEntry().id;
        ContractViewModel contract1 = contractSteps.givenContractWithRateCard(eventStatusRateId);

        // AND: We have a referral with a service agreement linked to this contract
        ReferralOptions options = new ReferralOptions()
                .withNoWorkflow()
                .withServiceAgreement("one-to-one", contract1.contractId);

        final long referralId = referralSteps.processReferralToId(options, DEMO_ALL, "Rota", "Test", "*********", "this is a rota test referral");
        ReferralViewModel referralViewModel = referralActor.getReferralById(referralId).getBody();

        ReferralData referralData = new ReferralData(referralViewModel, null, null, workflowActor.getTasksByTaskName(referralViewModel));
        String clientName = referralData.referralViewModel.getClientDisplayName();

        // AND: I check the first ServiceAgreement exists
        var serviceRecipientId = referralData.referralViewModel.serviceRecipientId;
        getFirstServiceAgreement(serviceRecipientId);

        // WHEN: I create an invoice dated at the end of the current month for this client
        var invoiceDate = createInvoiceFor(serviceRecipientId);

        // THEN: I can retrieve it and it's a valid draft one
        var invoice = getFirstInvoice(serviceRecipientId);
        assertIsEmptyDraftInvoiceWithDate(invoice, invoiceDate);

        // Create an agreement schedule (2 months)
        var now = java.time.LocalDate.now();
        rotaSteps.checkCanCreateScheduleOnFirstAgreement(clientName, serviceRecipientId, now.atTime(4,56), null, clock.nowJdk().toLocalDate().plusMonths(2), 0, null);
        // get any eventRef to save with
        var demand = agreementActor.getAppointmentSchedulesFromAgreement(referralViewModel.serviceRecipientId, DateTime.now(UTC)).getBody();
        String eventId = demand.get(0).getEventRef();

        // Now to start manipulating things...

        // AND: I can fetch all uninvoiced lines
        // uninvoiced lines from RotaActivityInvoiceController.findUninvoicedWork
        List<ClientSalesRotaInvoiceDetailResource.Line> proformaLines = rotaActivityInvoiceActor.findAllUninvoicedLines(now, now);

        assertThat("Expect more than one pro forma line", proformaLines, hasSize(greaterThanOrEqualTo(1)));
        // Post the proforma line info back
        final UUID lineId = rotaActivityInvoiceActor.addClientSalesInvoiceLine(invoice.getInvoiceId(), proformaLines.get(0));

        // THEN: The approved lines should disappear from pro-forma lines
        proformaLines = rotaActivityInvoiceActor.fetchUninvoicedLines(invoice);
        assertThat("Expect one pro forma line", proformaLines, hasSize(0));

        // Fetch the whole invoice again and make sure the line matches
        invoice = fetchInvoiceDetailAndAssertContainsLine(invoice, lineId);

        List<ClientSalesRotaInvoiceDetailResource.Line> lines = rotaActivityInvoiceActor.fetchInvoiceLines(invoice);
        assertThat("Expect invoice line to have been added", lines.size(), equalTo(1));
        ClientSalesRotaInvoiceDetailResource.Line invoiceLine = lines.get(0);
        assertThat("Expect line to be them same ID", invoiceLine.getLineUuid(), equalTo(lineId));
        // Check the linked event and work
        assertThat(invoiceLine.getEventId(), notNullValue());

        // AND the related work has an event with the rateId set
        var event = calendarActor.getEntries(eventId).getBody()[0];
        assertThat(event.getEventStatusRateId(), equalTo(eventStatusRateId));
    }

    @NonNull
    private ClientSalesRotaInvoiceDetailResource fetchInvoiceDetailAndAssertContainsLine(ClientSalesRotaInvoiceDetailResource invoice, UUID lineId) {
        invoice = rotaActivityInvoiceActor.fetchClientSalesInvoiceDetail(invoice);
        // Fetch the invoice lines and check this has been added and now has all the visit detail on it.
        assertThat("Expect amount to be not zero", invoice.getAmount(), greaterThan(BigDecimal.ZERO));
        assertThat("Expect invoice line on the detail too", invoice.getLines().size(), equalTo(1));
        assertThat("Expect the same line", invoice.getLines().get(0).getLineUuid(), equalTo(lineId));
        return invoice;
    }

    private java.time.@NonNull LocalDate createInvoiceFor(Integer serviceRecipientId) {
        var invoiceDate = java.time.LocalDate.now().withDayOfMonth(java.time.LocalDate.now().lengthOfMonth());
        rotaActivityInvoiceActor.createClientSalesInvoice(serviceRecipientId, invoiceDate);
        return invoiceDate;
    }

    private ClientSalesRotaInvoiceDetailResource getFirstInvoice(int serviceRecipientId) {
        var invoices = rotaActivityInvoiceActor.fetchClientSalesInvoices(serviceRecipientId);
        return invoices.stream().findFirst()
                .orElseThrow(() -> new AssertionError("Expect invoice to be found"));
    }

    private AgreementResource getFirstServiceAgreement(int serviceRecipientId) {
        final List<AgreementResource> activeAgreementsOnDate = agreementActor.getActiveAgreementsOnDate(now);
        return activeAgreementsOnDate.stream()
                .filter(a -> serviceRecipientId == a.getServiceRecipientId())
                .findFirst()
                .orElseThrow(() -> new AssertionError("Expect client agreement to be found"));
    }

    private void assertIsEmptyDraftInvoiceWithDate(ClientSalesInvoiceResource invoice, LocalDate invoiceDate) {
        assertThat("Expect date to match", invoice.getInvoiceDate(), equalTo(invoiceDate));
        assertThat("Expect status to be draft", invoice.getStatus(), equalTo(SalesInvoice.Status.DRAFT));
        assertThat("Expect amount to be zero", invoice.getAmount(), equalTo(BigDecimal.ZERO));
        assertThat("Expect link to detail", invoice.getRequiredLink(SELF), notNullValue(Link.class));
        if (invoice instanceof ClientSalesRotaInvoiceDetailResource) {
            assertThat("Expect no lines", ((ClientSalesRotaInvoiceDetailResource) invoice).getLines(), emptyCollectionOf(ClientSalesRotaInvoiceDetailResource.Line.class));
            assertThat("Expect link to proforma lines", invoice.getRequiredLink(REL_PROFORMA_LINES), notNullValue(Link.class));
        }
    }

    private ListDefinitionEntryViewModel createRateListDefEntry() {
        ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
        vm.listName = "eventStatusRateId";
        vm.name = unique.nameFor("my outcome rate");
        return listDefActor.ensureAndReturnListDefinitionEntry(vm.listName, vm).iterator().next();
    }
    private ListDefinitionEntryViewModel createEventListDefEntry() {
        ListDefinitionEntryViewModel vm = new ListDefinitionEntryViewModel();
        vm.listName = "eventStatusId";
        vm.name = unique.nameFor("not in");
        return listDefActor.ensureAndReturnListDefinitionEntry(vm.listName, vm).iterator().next();
    }
}
