
<div style="text-align: center; width: 90%; margin: 0 auto;">
    <span style="font-size: 2em; font-weight: bold;">

        <span id="printable-title">{{message referralPrintPageTitleCode default=referralPrintPageTitleCode}}</span>
    </span>

    <div class="row">

        <div class="col-sm-3">
            {{#if referral.client.contact.avatarId}}
                <img style="margin: 10px;"
                     src="{{applicationProperties.applicationRootPath}}api/secure/images/{{referral.client.contact.avatarId}}/"
                     width="150" alt="photo"/>
            {{/if}}
        </div>
        <div class="col-sm-6">
            {{> partials/menu/referral}}
            {{! additional detail relating to referral }}
            <span style="font-size: 0.9em;">
                {{#or referral.recievingServiceDate referral.acceptedOnService}}
                {{else}}
                    {{#if referral.decisionMadeOn}}
                        {{message "referralState.rejected"}}: {{localDate referral.decisionMadeOn}}<br/>
                    {{/if}}
                {{/or}}
                {{#if referral.recievingServiceDate}}
                    {{message "referralState.started"}}: {{userFriendlyDateFromLocal_usingJodaDateTime referral.receivingServiceDate}}<br/>
                {{/if}}
            </span>
        </div>
    </div>
</div>

