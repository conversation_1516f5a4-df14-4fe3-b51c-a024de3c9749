# setup
#   delete from cfg_externalsystem where name='homemaster-demo';
#   INSERT INTO cfg_externalsystem (`name`, `description`, `apiType`, `uri`, `clientSource`, `notifyWorkflow`, `clientSink`)
#   VALUES ('homemaster-demo', 'homemaster instance', 'DEFAULT', 'http://localhost:8090/homemaster/', false, false, true);
# update a client
#   select externalclientref, externalClientSource from clientdetails where id=<>;
#   update clientdetails set externalclientref='7', externalClientSource='homemaster-demo' where id=<>;

java -jar ecco-int-homemaster-1.0.0.CI-SNAPSHOT.jar --ecco.instance.tunnel.enabled=false --ecco.instance.baseUrl=http://localhost:8080/ecco-war/ --ecco.homemaster.url=https://test.designersoftware.co.uk/DemoWebService/HomeMasterWebService.asmx/UpdatePersonDetailsDemo
