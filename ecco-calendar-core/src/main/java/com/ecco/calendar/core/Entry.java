package com.ecco.calendar.core;

import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;

import org.jspecify.annotations.Nullable;
import java.net.URI;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.instantFromJoda;

/**
 * Represents a calendar entry
 * As per CalendarEntries and CombinedEntry, this is used as an intermediary class, not a dto to the client
 * Its what is gathered from the calendaring system.
 */
@SuppressWarnings("JavadocReference")
@Getter
@Setter
public class Entry implements HasAttributes<EntryAttribute> {

    /** This is either a 36 char UUID string or a UUID plus an ISO8601 date-time (with poss UTC Z on the end)
     * e.g. a62bbcd6-1b6d-4de7-95c9-d0f09349489c:20200102T102409Z (53 chars or 52 without the Z).
     * See {@link org.osaf.cosmo.model.ModificationUid}
     */
    private String itemUid;

    /**
     * Whether this event is a recurrence, so is part of a recurring series.
     * NB This is not true for the parent recurring entry itself (this checks for ':' - see CosmoCalendarService.isRecurring).
     * A parent recurring entry can be determined through recurringEntry below.
     */
    private boolean recurrence;

    /**
     * Indication that this event is itself setting the schedule for recurrences.
     * @see RecurringEntry
     * Determined using BaseEventStamp#isRecurring which looks for RRULE's - and we exclude recurrences
     */
    private boolean recurringEntry;

    /*
     * See NoteItemToRecurringEntryAdapter#getOwnerCalendarId and AttendeeAdapter#getCalendarIdUserReferenceUri and getCalendarId
     * The itemUid's owner uid
     * Note: We have no way of using this through existing cosmo APIs to get the home calendar for this person.
     */
    //private String ownerUid;

    /**
     * Unique reference to the native object of the user (calendarId) who created the event.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    private String calendarIdUserReferenceUri;

    /**
     * The owner's calendarId - complements AttendeeAdapter#attendee.
     * This is the calendarId of calendarIdUserReferenceUri.
     */
    private String ownerCalendarId;

    private String title;
    private String description;

    /**
     * the the iCalendar LOCATION property value of the event (can be null).
     */
    private String location;
    private Set<Attendee> attendees = new HashSet<>();

    public Instant startInstant() {
        return instantFromJoda(start);
    }

    public Instant endInstant() {
        return instantFromJoda(end);
    }

    private DateTime start;
    @Nullable
    private DateTime end;
    private boolean allDay;
    private final AttributeSet<EntryAttribute> attributes = new AttributeSet<>(EntryAttribute.class);

    /**
     * e.g. X-ECCO-MANAGED-BY:entity://AppointmentSchedule/{id}
     */
    private URI managedByUri;

    /**
     * Reference to the last command that modified the entry.
     * Added for debugging purposes to see which rota schedules need extending.
     * Having this information via events (and not the rota) is because we only want confirmed events
     * which are already on the calendar, and its simpler to query the calendar.
     * e.g. X-ECCO-UPDATED-BY:entity://AppointmentRecurringActionCommand/{uuid}
     */
    private URI updatedByUri;

    public Set<Attendee> getAttendees() {
        return ImmutableSet.copyOf(attendees);
    }

    public void addAttendee(Attendee attendee) {
        attendees.add(attendee);
    }

    @Override
    public Set<EntryAttribute> getAttributes() {
        return attributes.getAttributes();
    }

    @Override
    public String getAttributeValue(String key) {
        return attributes.getAttributeValue(key);
    }

    @Override
    public boolean hasAttribute(String key) {
        return attributes.hasAttribute(key);
    }

    @Override
    public void addAttribute(String key, String value) {
        attributes.addAttribute(key, value);
    }

    @Override
    public void removeAttribute(String key) {
        attributes.removeAttribute(key);
    }

    @Override
    public String toString() {
        return this.getClass().getSimpleName() + "{" +
                "  id='" + itemUid + '\'' +
                //", ownerUid='" + ownerUid + '\'' +
                ", title='" + title + '\'' +
                ", location='" + location + '\'' +
                ", attendees=" + attendees +
                ", start=" + start +
                ", end=" + end +
                "  }";
    }

}
