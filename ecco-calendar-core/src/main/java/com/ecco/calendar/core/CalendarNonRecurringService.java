package com.ecco.calendar.core;

import com.ecco.calendar.dom.EventEntryDefinition;
import org.joda.time.DateTimeZone;
import org.joda.time.Interval;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.util.Set;

/**
 * iCal-based calendar service.
 */
public interface CalendarNonRecurringService {

    /**
     * Find a calendar URI of the calendarId
     */
    String findCalendarIdUserReferenceUri(String calendarId);

    CalendarEntries findEntries(@NonNull Interval interval);

    /**
     * Find entries in a calendar within a given interval, or all time, if no interval is specified
     *
     */
    CalendarEntries findEntries(String calendarId, @Nullable Interval interval);

    /**
     * Find entries across a number of calendars within a given interval, or all time, if no interval is specified
     * @return a set of entries grouped by calendar
     */
    Set<CalendarEntries> findEntries(Set<String> calendarIds, @Nullable Interval interval);

    /**
     * Find an existing entry in a calendar by id
     */
    Entry findEntry(String id);

    /**
     * Create a new entry in a calendar.
     * @return newly created entry, along with the generated id
     */
    Entry addEntry(String calendarId, Entry entry);

    /**
     * Edit an existing entry (the change is reflected in all calendars)
     */
    void editEntry(Entry entry);

    /**
     * Delete an existing entry (reflected in all calendars).
     */
    void deleteEntry(String entryId);

    void moveEntryToAnotherCalendar(String srcCalendarId, String dstCalendarId, String entryUid);

    /**
     *  Creates a new calendar and associated owner user account
     * @return new calendarId string
     */
    String createCalendar(CalendarOwnerDefinition owner);

    /**
     * Updates the owner user account associated with a calendar.
     * Only non-null properties will be updated.
     *
     * @param calendarId the unique ID of the calendar
     * @param ownerDefinition the new user account details
     */
    void updateCalendarOwner(String calendarId, CalendarOwnerDefinition ownerDefinition);

    /**
     * Find the calendar owner's availability within a given interval.
     * The availability returned is unaffected by existing entries.
     * If {@code baselineOnly} is set, then the availability is also unaffected by any overrides created with
     * {@link #blockAvailability(String, UnavailableIntervalDefinition)}.
     *
     * @param calendarId the unique ID of the calendar
     * @param interval the period of the availability information to be returned
     * @param baselineOnly true to ignore any overrides when returning the calculated availability
     * @return the availability descriptor for the time period
     */
    Availability findAvailability(String calendarId, Interval interval, boolean baselineOnly);

    /**
     * Update the calendar owner's availability within a given interval.
     * Any time within the interval specified as part of the availability object which is not marked as available
     * will be set to be unavailable, clearing any pre-existing availability in the same timespan.
     * <p>
     * Only <em>baseline</em> records are affected. Any overrides created with {@link #blockAvailability(String, UnavailableIntervalDefinition)}
     * remain in place regardless of the availability provided to this method.
     *
     * @param availability the availability descriptor for the time period specified
     */
    void updateAvailability(Availability availability);

    /**
     * Block out a period of unavailability in a calendar. This overrides any availability set with
     * {@link #updateAvailability(Availability)} in an overlapping period.
     *
     * @param calendarId the unique ID of the calendar in which to block availability
     * @param entry the definition of the unavailable interval to block out
     * @return newly created unavailable interval, along with the handle for future reference
     */
    UnavailableInterval blockAvailability(String calendarId, final UnavailableIntervalDefinition entry);

    /**
     * Reinstate any availability for the previously unavailable period. This allows any available set with
     * {@link #updateAvailability(Availability)} to be reflected in a calendar owner's overall availability.
     *
     * @param handle the handle of the unavailable interval to rescind
     */
    void reinstateAvailability(UnavailableInterval.Handle handle);

    /**
     * Modify a previously-created period of unavailability in a calendar.
     *
     * @param handle the handle of the unavailable interval to modify
     * @param update the updated definition of the unavailable interval
     * @return newly updated unavailable interval
     */
    UnavailableInterval updateUnavailableInterval(UnavailableInterval.Handle handle, UnavailableIntervalDefinition update);

    // =====================
    // START as-was AOP methods
    String getUUID();
    void postEntry(EventEntryDefinition event, String collectionUid, String modifiedByUsername, DateTimeZone timeZone) throws CalendarException;
    void putEntry(EventEntryDefinition event, String modifiedByUsername, DateTimeZone timeZone);
    void deleteEntry(EventEntryDefinition event) throws CalendarException;
    // END as-was AOP methods

}
