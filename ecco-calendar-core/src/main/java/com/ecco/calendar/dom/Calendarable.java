package com.ecco.calendar.dom;

import com.ecco.calendar.core.CalendarOwnerDefinition;

public interface Calendarable {

    /** Has this been persisted yet */
    boolean isNewEntity();

    /** Name of this entity for display in the calendar */
    String getDisplayName();

    String getCalendarId();

    void setCalendarId(String createCalendar);

    /**
     * Fill in calendar owner details from the contact subtype, except for email and password which can be found on here.
     * @param builder the builder to enrich
     * @return the builder passed in
     */
    CalendarOwnerDefinition.Builder buildCalendarOwner(CalendarOwnerDefinition.Builder builder);

    /**
     * Fill in calendar owner name update details from the contact subtype
     * @param builder the builder to enrich
     * @return the builder passed in
     */
    CalendarOwnerDefinition.Builder buildCalendarNamesUpdate(CalendarOwnerDefinition.Builder builder);
}
